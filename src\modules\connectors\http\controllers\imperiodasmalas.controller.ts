import { Request, Response } from 'express';
import { BigHubService } from '../../../bighub/services/bighub.service';
import { ImperiodasmalasService } from '../../services/imperiodasmalas.service';
import fs from 'fs'

export class ImperiodasmalasController {

  imperiodasmalasService: ImperiodasmalasService;
  bigHubService: BigHubService;
  
  constructor(bigHubService: BigHubService, imperiodasmalasService: ImperiodasmalasService) {
    // coolaccesorios service
    this.imperiodasmalasService = imperiodasmalasService;
    // bighub service
    this.bigHubService = bigHubService;
  }
  
  async getProducts(req: Request, res: Response) {
    let response = {
      status: 200,
      message: 'Products found'
    }

    try {
      const products = await this.imperiodasmalasService.getProducts();
      res.status(response.status).json(products)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubCsv(req: Request, res: Response) {
    try {
      const filename = '411-bighub-imperiodasmalas.csv';
      res.setHeader('Content-disposition', 'attachment; filename=' + filename);
      res.setHeader('Content-type', 'text/csv');
      fs.createReadStream(filename).pipe(res)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubExportCsv(req: Request, res: Response) {
    try {
      const filename = '411-bighub-imperiodasmalas.csv';
      const products = await this.imperiodasmalasService.getProductsBighub();
      await this.bigHubService.supplierCreateCsv(filename, products, 411); //411

      res.status(200).send('ok');
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }
}