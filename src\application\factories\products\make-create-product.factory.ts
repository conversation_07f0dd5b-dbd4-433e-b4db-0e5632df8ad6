import { ProductKnexRepository } from '../../../infra/db/knex/repositories/product-knex.repository'
import { CreateProductUseCaseInterface } from '../../interfaces/use-cases/products/create-product-use-case.interface'
import { CreateProductUseCase } from '../../uses-cases/products/create-product.use-case'

export const makeCreateProductFactory = (): CreateProductUseCaseInterface => {
  const repository = new ProductKnexRepository()
  const useCase = new CreateProductUseCase(repository)
  return useCase
}