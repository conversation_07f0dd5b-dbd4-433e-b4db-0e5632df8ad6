import { Marketplace } from "../../../../domain/entities/marketplace"
export interface GetMarketplacesRepositoryInterface {
  getMarketplaces: (request: GetMarketplacesRepositoryInterface.Request) => Promise<GetMarketplacesRepositoryInterface.Response>
}

export namespace GetMarketplacesRepositoryInterface {
  export type Request = Pick<Marketplace, 'status'>
  export type Response = Array<Marketplace> | void
}