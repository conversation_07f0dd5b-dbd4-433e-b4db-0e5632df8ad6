import { OfferKnexRepository } from "../../../infra/db/knex/repositories/offer.knex.repository"
import { UpdateOfferStockByEanUseCaseInterface } from "../../interfaces/use-cases/offers/update-offer-by-ean-use-case.interface"
import { UpdateOfferStockByEanUseCase } from "../../uses-cases/offers/update-offer-stock-by-ean.use-case"

export const makeUpdateOfferStockByEanFactory = (): UpdateOfferStockByEanUseCaseInterface => {
  const offerRepository = new OfferKnexRepository()
  return new UpdateOfferStockByEanUseCase(offerRepository)
}