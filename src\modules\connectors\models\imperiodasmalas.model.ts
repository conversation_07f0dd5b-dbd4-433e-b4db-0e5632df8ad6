export interface ImperiodasmalasModel {
    product_id: string;
    ean: string;
    sku: string;
    title: string;
    description: string;
    brand: string;
    price: number;
    original_price: number;
    discount_percent: number;
    stock: number;
    image: string;
    additional_images?: string[];
    weight: number;
    
    // Imperio das Malas specific fields
    product_type: string;
    category_ids: number[];
    product_features: Record<string, any>;
    status: string;
    list_price: number;
    timestamp: number;
    updated_timestamp: number;
    seo_name: string;
    main_category: number | null;
    
    // Variation data
    variation_group_id: number | null;
    variation_group_code: string;
    variation_features: Record<string, any>;
    variation_name: string;
    
    // Shipping and dimensions
    shipping_freight: number;
    length: number;
    width: number;
    height: number;
    
    // Promotions
    has_promotions: boolean;
    
    // Original data can be kept here
    [key: string]: any;
  }