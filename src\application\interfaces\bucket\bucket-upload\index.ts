export namespace BucketUpload {

  export type Request = {
    file: string
    path: string
  }

  export type Response = {
    result: Result
  }

  export type Result = boolean

  export interface Repository {
    bucketUpload: (request: BucketUpload.Request) => Promise<BucketUpload.Result>
  }

  export interface UseCase {
    execute: (request: BucketUpload.Request) => Promise<BucketUpload.Response>
  }
}