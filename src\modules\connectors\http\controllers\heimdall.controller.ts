import { Request, Response } from 'express';
import { BigHubService } from '../../../bighub/services/bighub.service';
import { HeimdallService } from '../../services/heimdall.service';
import fs from 'fs'

export class HeimdallController {

  heimdallService: HeimdallService;
  bigHubService: BigHubService;
  
  constructor(bigHubService: BigHubService, heimdallService: HeimdallService) {
    // coolaccesorios service
    this.heimdallService = heimdallService;
    // bighub service
    this.bigHubService = bigHubService;
  }
  
  async getProducts(req: Request, res: Response) {
    let response = {
      status: 200,
      message: 'Products found'
    }

    try {
      const products = await this.heimdallService.getProducts();
      res.status(response.status).json(products)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubCsv(req: Request, res: Response) {
    try {
      const filename = '1439-bighub-heimdall.csv';
      res.setHeader('Content-disposition', 'attachment; filename=' + filename);
      res.setHeader('Content-type', 'text/csv');
      fs.createReadStream(filename).pipe(res)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubExportCsv(req: Request, res: Response) {
    try {
      const filename = '1439-bighub-heimdall.csv';
      const products = await this.heimdallService.getProductsBighub();
      this.bigHubService.supplierCreateCsv(filename, products, 1439); //1439

      res.status(200).send('ok');
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }
}