import { JardimsuspensoModel } from '../models/jardimsuspenso.model';
import got from 'got';
import { YamlRepo } from '../../configurations/repos/yaml.repo';
import { JardimsuspensoPort } from '../ports/jardimsuspenso.port';
import { ProductModel } from 'src/modules/bighub/models/product.model';
import * as xml2js from 'xml2js';
import { S3Client, PutObjectCommand, ObjectCannedACL } from '@aws-sdk/client-s3';

export class JardimsuspensoRepo implements JardimsuspensoPort {
  private rows: JardimsuspensoModel[] = [];
  private configuration: YamlRepo;
  private parser: xml2js.Parser;
  private s3Client: S3Client;
  private enableDetailedLogging: boolean = false; // Add a boolean flag to control logging, default to false

  constructor(configuration: YamlRepo) {
    this.configuration = configuration;
    // Use different parser options to better handle the XML structure
    this.parser = new xml2js.Parser({ 
      explicitArray: false, 
      mergeAttrs: true,
      // Force everything to be plain text where possible
      valueProcessors: [
        (value: any) => typeof value === 'object' ? JSON.stringify(value) : value
      ]
    });
    
    // Initialize S3 client
    this.s3Client = new S3Client({
      credentials: {
        accessKeyId: '9B2RRD2N4Y6HF86RNBOF',
        secretAccessKey: 'tbZCsFe2hIk69uV2RBuqKnHV4TctCsUy64LcEhNd',
      },
      region: 'us-east-1',
      endpoint: 'https://ewr1.vultrobjects.com/',
      forcePathStyle: true,
    });
  }

  // Helper method for conditional logging
  private log(message: string, data?: any): void {
    if (this.enableDetailedLogging) {
      if (data) {
        console.log(message, data);
      } else {
        console.log(message);
      }
    }
  }

  // Allow external control of logging flag
  setDetailedLogging(enable: boolean): void {
    this.enableDetailedLogging = enable;
    console.log(`Detailed logging ${enable ? 'enabled' : 'disabled'}`);
  }

  validateOffers(products: ProductModel[]): Promise<ProductModel[]> {
    throw new Error('Method not implemented.');
  }

  async getAll(): Promise<JardimsuspensoModel[]> {
    return new Promise(async (resolve, reject) => {
      try {
        console.log('Starting Jardimsuspenso product fetch process');
        const loginCredentials = this.configuration.get('conexions', 'connector', 'jardimsuspenso');
        const apiUrl = loginCredentials.api_url;
        const token = loginCredentials.prestashop_token;

        // Get all products
        console.log(`Fetching all products from ${apiUrl}/products/`);
        const allProductsResponse = await got(`${apiUrl}/products/`, {
          responseType: 'text',
          headers: {
            'Authorization': `Basic ${token}`
          }
        });

        // Parse XML to JSON
        const allProductsJson = await this.parser.parseStringPromise(allProductsResponse.body);
        
        const allProducts = allProductsJson.prestashop.products;
        if (!allProducts || !allProducts.product) {
          console.log('No products found.');
          resolve(this.rows);
          return;
        }

        // Make sure product is an array even if there's only one
        const productList = Array.isArray(allProducts.product) 
          ? allProducts.product 
          : [allProducts.product];

        console.log(`Found ${productList.length} products to process`);
        
        // Track image upload stats
        let totalProducts = productList.length;
        let productsWithImages = 0;
        let successfulUploads = 0;
        let failedUploads = 0;

        // Process each product
        for (let i = 0; i < productList.length; i++) {
          const productSummary = productList[i];
          const productId = String(productSummary.id);
          
          this.log(`Processing product ${i+1}/${totalProducts}: ID ${productId}`);
          
          // Get detailed product information
          const productDetailResponse = await got(`${apiUrl}/products/${productId}`, {
            responseType: 'text',
            headers: {
              'Authorization': `Basic ${token}`
            }
          });
          
          // Parse XML to JSON
          const productDetailJson = await this.parser.parseStringPromise(productDetailResponse.body);
          const productDetail = productDetailJson.prestashop.product;
          
          // Get product image if it exists and upload to S3
          let imageUrl = '';
          if (productDetail.id_default_image) {
            productsWithImages++;
            
            // Try multiple approaches to extract the actual image ID
            let imageId;
            if (typeof productDetail.id_default_image === 'string') {
              imageId = productDetail.id_default_image;
            } else if (typeof productDetail.id_default_image === 'object') {
              // If it's an object, try to find a value property or other common property names
              if (productDetail.id_default_image.value) {
                imageId = productDetail.id_default_image.value;
              } else if (productDetail.id_default_image._) {
                imageId = productDetail.id_default_image._;
              } else if (productDetail.id_default_image.$) {
                imageId = productDetail.id_default_image.$;
              } else {
                // Last resort: try the first key's value
                const firstKey = Object.keys(productDetail.id_default_image)[0];
                if (firstKey) {
                  imageId = productDetail.id_default_image[firstKey];
                }
              }
            }
            
            // Final check and conversion to string
            if (imageId !== undefined) {
              imageId = String(imageId).trim();
              
              try {
                // Upload image to S3 and get public URL
                imageUrl = await this.uploadProductImageToS3(apiUrl, token, productId, imageId);
                if (imageUrl) {
                  successfulUploads++;
                  this.log(`✅ Product ${productId}: Image uploaded successfully`);
                } else {
                  failedUploads++;
                  this.log(`⚠️ Product ${productId}: Image upload returned empty URL`);
                }
              } catch (imageError) {
                failedUploads++;
                console.error(`❌ Error processing image for product ${productId}:`, imageError);
              }
            } else {
              console.error(`Could not extract a valid image ID for product ${productId}`);
              failedUploads++;
            }
          } else {
            this.log(`Product ${productId} has no image defined`);
          }
          
          // Store the category data with the product for filtering in the service
          let categoryData = null;
          if (productDetail.associations && 
              productDetail.associations.categories && 
              productDetail.associations.categories.category) {
            // Make sure category is always an array
            const categories = Array.isArray(productDetail.associations.categories.category) 
              ? productDetail.associations.categories.category 
              : [productDetail.associations.categories.category];
              
            categoryData = categories;
          }
          
          // Process the product information
          this.processLine({
            ...productDetail,
            image: imageUrl,
            categoryData: categoryData
          });
          /*
          // Log progress every 10 products or at the end
          if ((i + 1) % 10 === 0 || i === productList.length - 1) {
            console.log(`Progress: ${i + 1}/${totalProducts} products processed`);
            console.log(`Image stats: ${productsWithImages} with images, ${successfulUploads} uploads successful, ${failedUploads} uploads failed`);
          }
          */
        }
        
        // Final upload statistics
        console.log('========== FERRIMEX PRODUCT FETCH COMPLETE ==========');
        console.log(`Total products processed: ${totalProducts}`);
        console.log(`Products with images: ${productsWithImages}`);
        console.log(`Successful S3 uploads: ${successfulUploads}`);
        console.log(`Failed S3 uploads: ${failedUploads}`);
        console.log(`Upload success rate: ${((successfulUploads / productsWithImages) * 100).toFixed(2)}%`);
        console.log('====================================================');
        
        resolve(this.rows);
      } catch (error) {
        console.error('Error fetching products:', error);
        reject(error);
      }
    });
  }

  /**
   * Fetches a product image from PrestaShop and uploads it to S3
   */
  private async uploadProductImageToS3(apiUrl: string, token: string, productId: string, imageId: string): Promise<string> {
    // Validate inputs
    if (!productId || !imageId) {
      console.error('Invalid product ID or image ID:', { productId, imageId });
      return '';
    }

    try {
      this.log(`Starting image upload process for product ${productId}, image ${imageId}`);
      
      // Build the URL
      const imageUrl = `${apiUrl}/images/products/${productId}/${imageId}`;
      this.log(`Fetching image from: ${imageUrl}`);
      
      const imageResponse = await got(imageUrl, {
        responseType: 'buffer',
        headers: {
          'Authorization': `Basic ${token}`
        }
      });

      // Check if we got the image
      if (!imageResponse.body || imageResponse.statusCode === 404) {
        console.error(`Failed to fetch image for product ${productId}: No content or 404 status`);
        return '';
      }

      this.log(`Successfully fetched image for product ${productId}, size: ${imageResponse.body.length} bytes`);

      // Define S3 file path and name
      const fileName = `image_${productId}_${imageId}.jpg`;
      const s3Path = `images/${fileName}`;
      
      // S3 Upload Logic 
       
      this.log(`Preparing to upload image to S3 - Bucket: '153', Path: '${s3Path}'`);
      
      // Upload to S3
      const uploadParams = {
        Bucket: '153',
        Key: s3Path,
        Body: imageResponse.body,
        ContentType: 'image/jpeg',
        ACL: 'public-read' as ObjectCannedACL
      };
    
      const command = new PutObjectCommand(uploadParams);
      
      try {
        // Actually perform the upload
        const uploadResult = await this.s3Client.send(command);
        this.log(`✅ Successfully uploaded image to S3:`, {
          product: productId,
          imageId: imageId,
          s3Path: s3Path,
          uploadResult: uploadResult
        });
      } catch (uploadError) {
        console.error(`❌ S3 upload failed for product ${productId}:`, uploadError);
        throw uploadError;
      }
      // Return the public URL
      const publicUrl = `https://ewr1.vultrobjects.com/153/${s3Path}`;
      this.log(`Generated public URL for image: ${publicUrl}`);
      return publicUrl;
    } catch (error) {
      //console.error(`❌ Error in uploadProductImageToS3 for product ${productId}:`, error);
      throw error;
    }
  }

  private processLine(product: any) {
    const imagePlaceholder = 'https://cdn.bighub.store/image/product-placeholder.png';

    // Function to remove emojis from text
    const removeEmojis = (text: string): string => {
      if (!text) return '';
      
      // This regex pattern matches most emoji characters
      return text.replace(/[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F700}-\u{1F77F}\u{1F780}-\u{1F7FF}\u{1F800}-\u{1F8FF}\u{1F900}-\u{1F9FF}\u{1FA00}-\u{1FA6F}\u{1FA70}-\u{1FAFF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu, '');
    };

    // Extract name directly from name.language property and remove emojis
    const getName = () => {
      let name = '';
      
      if (product.name && product.name.language) {
        // If language is a string, use it directly
        if (typeof product.name.language === 'string') {
          name = product.name.language;
        } 
        // If language is an object with an underscore property (_)
        else if (product.name.language._) {
          name = product.name.language._;
        } 
        // If language is an array (multiple languages)
        else if (Array.isArray(product.name.language)) {
          // Use the first language's content if available
          name = product.name.language[0] && product.name.language[0]._ 
            ? product.name.language[0]._ 
            : '';
        }
      }
      // Fallback (rarely needed given the structure of the sample data)
      else if (typeof product.name === 'string') {
        name = product.name;
      }
      
      // Remove emojis and return the cleaned name
      return removeEmojis(name);
    };

    // Extract description directly from description_short.language property and remove emojis
    const getDescription = () => {
      let description = '';
      
      if (product.description_short && product.description_short.language) {
        // If language is a string, use it directly
        if (typeof product.description_short.language === 'string') {
          description = product.description_short.language;
        } 
        // If language is an object with an underscore property (_)
        else if (product.description_short.language._) {
          description = product.description_short.language._;
        } 
        // If language is an array (multiple languages)
        else if (Array.isArray(product.description_short.language)) {
          // Use the first language's content if available
          description = product.description_short.language[0] && product.description_short.language[0]._ 
            ? product.description_short.language[0]._ 
            : '';
        }
      }
      // Fallback
      else if (typeof product.description_short === 'string') {
        description = product.description_short;
      }
      
      // Remove emojis and return the cleaned description
      return removeEmojis(description);
    };

    // Get manufacturer name
    const getManufacturerName = () => {
      if (product.manufacturer_name) {
        return typeof product.manufacturer_name === 'string' 
          ? product.manufacturer_name 
          : (product.manufacturer_name._ || product.manufacturer_name.$ || '');
      }
      return '';
    };

    // Get product quantity/stock
    const getQuantity = () => {
      // Check all possible locations where quantity might be stored
      if (product.quantity !== undefined) {
        // Direct quantity property
        return this.parseNumberValue(product.quantity);
      } else if (product.stock_available && product.stock_available.quantity !== undefined) {
        // Nested in stock_available property
        return this.parseNumberValue(product.stock_available.quantity);
      } else if (product.associations && 
                product.associations.stock_availables && 
                product.associations.stock_availables.stock_available) {
        // In associations
        const stockAvailable = product.associations.stock_availables.stock_available;
        if (Array.isArray(stockAvailable)) {
          // If multiple stock entries, use the first one
          return stockAvailable[0] && stockAvailable[0].quantity !== undefined ? 
            this.parseNumberValue(stockAvailable[0].quantity) : 0;
        } else {
          // Single stock entry
          return stockAvailable.quantity !== undefined ? 
            this.parseNumberValue(stockAvailable.quantity) : 0;
        }
      }
      // Default to 0 if no quantity information found
      return 0;
    };

    // Map PrestaShop product data to JardimsuspensoModel
    const newRow: JardimsuspensoModel = {
      id: product.reference,
      name: getName(),
      description_short: getDescription(),
      ean13: product.ean13 || '',
      manufacturer_name: getManufacturerName(),
      price: parseFloat(product.price || '0'),
      quantity: getQuantity(), // Use the actual quantity
      image: product.image || imagePlaceholder,
      id_default_image: typeof product.id_default_image === 'object' ? 
                         JSON.stringify(product.id_default_image) : 
                         String(product.id_default_image || ''),
      weight: parseFloat(product.weight || '0'),
      // Store category data for filtering in service
      categoryData: product.categoryData || null
    };

    this.rows.push(newRow);
  }

  /**
   * Helper method to parse number values from different formats
   */
  private parseNumberValue(value: any): number {
    if (value === undefined || value === null) {
      return 0;
    }
    
    if (typeof value === 'number') {
      return value;
    }
    
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? 0 : parsed;
    }
    
    if (typeof value === 'object') {
      // Try to extract number from an object representation
      if (value._ !== undefined) {
        return this.parseNumberValue(value._);
      }
      if (value.value !== undefined) {
        return this.parseNumberValue(value.value);
      }
      // Last resort - stringify and try to parse
      return this.parseNumberValue(JSON.stringify(value));
    }
    
    return 0;
  }
}