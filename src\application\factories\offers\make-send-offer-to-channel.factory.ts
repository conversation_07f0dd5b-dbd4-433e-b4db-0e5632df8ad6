import { SendOfferToChannelUseCaseInterface } from "../../interfaces/use-cases/offers/send-offer-to-channel-use-case.interface"
import { QueueUseCaseInterface } from "../../interfaces/use-cases/queue/queue-use-case.interface"
import { SendOfferToChannelUseCase } from "../../uses-cases/offers/send-offer-to-channel.use-case"
export const makeSendOfferToChannelFactory = (queueUseCase: QueueUseCaseInterface): SendOfferToChannelUseCaseInterface => {
  return new SendOfferToChannelUseCase(queueUseCase)
}