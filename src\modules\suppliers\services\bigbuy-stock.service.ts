import { ProductModel as BighubProductModel } from '../../bighub/models/product.model';
import { BIGHubCsvPort } from '../../bighub/ports/bighub-csv.port';
import { ProductModel } from '../../catalogue/models/product.model';
import { SupplierCode } from '../enums/supplier-code.enum';
import { BigbuyStockModel } from '../models/bigbuy-stock.model';
import { BigbuyStockPort } from '../ports/bigbuy-stock.port';
import { defer } from '@shared/utils/promise.util';
import * as fs from 'fs';
import * as path from 'path';

export class BigbuyStockService {
  bigbuystockRepo: BigbuyStockPort;
  bighubRepo: BIGHubCsvPort;

  constructor(
    repo: BigbuyStockPort,
    bighubRepo: BIGHubCsvPort,
  ) {
    this.bigbuystockRepo = repo;
    this.bighubRepo = bighubRepo;
  }

  async getBigbuyStocks(): Promise<BigbuyStockModel[]> {
    try {
      console.log('Starting BigBuy stock synchronization...');
      
      // Get all products stock from BigBuy
      const stockData = await this.bigbuystockRepo.getAllProductsStock();
      console.log(`Retrieved stock data for ${stockData.length} products`);

      // Get product variations stock
      const variationStockData = await this.bigbuystockRepo.getProductVariationsStock();
      console.log(`Retrieved stock data for ${variationStockData.length} product variations`);

      // Combine all stock data
      const allStockData = [...stockData, ...variationStockData];
      console.log(`Total combined stock data: ${allStockData.length} items`);

      // Save raw stock data to JSON file in project root with verification
      try {
        // Save with both filenames to ensure compatibility with all code
        const jsonFilePath = path.join(process.cwd(), 'bigbuy_stocks.json');
        const altJsonFilePath = path.join(process.cwd(), 'bigbuy-stock.json');
        console.log(`💾 Starting to save stock data to: ${jsonFilePath} and ${altJsonFilePath}`);
        
        // Prepare JSON string (do this once for efficiency)
        const jsonData = JSON.stringify(allStockData, null, 2);
        
        // Write files synchronously (blocks until complete)
        fs.writeFileSync(jsonFilePath, jsonData, 'utf8');
        fs.writeFileSync(altJsonFilePath, jsonData, 'utf8');
        
        // Verify the files were written correctly
        if (fs.existsSync(jsonFilePath) && fs.existsSync(altJsonFilePath)) {
          const fileStats = fs.statSync(jsonFilePath);
          const fileSizeKB = Math.round(fileStats.size / 1024);
          console.log(`✅ File verification successful:`);
          console.log(`   - Files exist: ${jsonFilePath}, ${altJsonFilePath}`);
          console.log(`   - File size: ${fileSizeKB} KB`);
          console.log(`   - Items saved: ${allStockData.length}`);
          
          console.log(`🎉 Stock data completely saved and verified!`);
        } else {
          throw new Error('Files were not created successfully');
        }
        
      } catch (saveError) {
        console.error('❌ Error saving raw stock data to JSON files:', saveError);
        throw saveError; // Re-throw to prevent proceeding with corrupted data
      }
      
      return allStockData;

    } catch (error) {
      console.error('Error in getBigbuyStocks:', error);
      throw error;
    }
  }

  /**
   * Clear the repository cache
   */
  clearCache(): void {
    this.bigbuystockRepo.clear();
  }

  /**
   * Get stock summary information
   */
  async getStockSummary(): Promise<{
    totalProducts: number;
    totalVariations: number;
    totalInStock: number;
    outOfStock: number;
  }> {
    try {
      const stockData = await this.bigbuystockRepo.getAllProductsStock();
      const variationStockData = await this.bigbuystockRepo.getProductVariationsStock();
      
      const allStockData = [...stockData, ...variationStockData];
      
      const totalInStock = allStockData.filter(item => 
        item.stocks.some(stock => stock.quantity > 0)
      ).length;
      
      const outOfStock = allStockData.length - totalInStock;

      return {
        totalProducts: stockData.length,
        totalVariations: variationStockData.length,
        totalInStock,
        outOfStock
      };
    } catch (error) {
      console.error('Error getting stock summary:', error);
      throw error;
    }
  }

  /**
   * Sync stock data and save to local files with verification
   */
  async syncAndSaveStock(outputPath: string = './data/stock'): Promise<void> {
    try {
      // Ensure output directory exists
      if (!fs.existsSync(outputPath)) {
        fs.mkdirSync(outputPath, { recursive: true });
      }

      // Get all stock data
      const stockData = await this.bigbuystockRepo.getAllProductsStock();
      const variationStockData = await this.bigbuystockRepo.getProductVariationsStock();

      // Combine all stock data
      const allStockData = [...stockData, ...variationStockData];
      console.log(`Total combined stock data: ${allStockData.length} items`);

      // Save combined stock to project root with verification
      try {
        const rootJsonFilePath = path.join(process.cwd(), 'bigbuy-stock.json');
        console.log(`💾 Saving combined stock data to: ${rootJsonFilePath}`);
        
        const jsonData = JSON.stringify(allStockData, null, 2);
        fs.writeFileSync(rootJsonFilePath, jsonData, 'utf8');
        
        // Verify file was saved correctly
        if (fs.existsSync(rootJsonFilePath)) {
          const fileStats = fs.statSync(rootJsonFilePath);
          const fileSizeKB = Math.round(fileStats.size / 1024);
          console.log(`✅ Combined file saved and verified:`);
          console.log(`   - File: ${rootJsonFilePath}`);
          console.log(`   - Size: ${fileSizeKB} KB`);
          console.log(`   - Items: ${allStockData.length}`);
        } else {
          throw new Error('Combined file was not created successfully');
        }
      } catch (saveError) {
        console.error('❌ Error saving combined stock data to JSON file:', saveError);
        throw saveError;
      }

      // Save products stock (separate file for backup)
      const productsFile = path.join(outputPath, `bigbuy-products-stock-${Date.now()}.json`);
      fs.writeFileSync(productsFile, JSON.stringify(stockData, null, 2), 'utf8');
      console.log(`Saved products stock to: ${productsFile}`);

      // Save variations stock (separate file for backup)
      const variationsFile = path.join(outputPath, `bigbuy-variations-stock-${Date.now()}.json`);
      fs.writeFileSync(variationsFile, JSON.stringify(variationStockData, null, 2), 'utf8');
      console.log(`Saved variations stock to: ${variationsFile}`);

      // Generate summary report
      const summary = await this.getStockSummary();
      const summaryFile = path.join(outputPath, `bigbuy-stock-summary-${Date.now()}.json`);
      fs.writeFileSync(summaryFile, JSON.stringify(summary, null, 2), 'utf8');
      console.log(`Saved stock summary to: ${summaryFile}`);

      console.log(`🎉 All files saved and verified successfully!`);

    } catch (error) {
      console.error('Error syncing and saving stock:', error);
      throw error;
    }
  }

  /**
   * Helper method to safely save JSON with verification
   */
  private async saveJsonWithVerification(filePath: string, data: any, description: string): Promise<void> {
    try {
      console.log(`💾 Saving ${description} to: ${filePath}`);
      
      // Convert to JSON string
      const jsonData = JSON.stringify(data, null, 2);
      
      // Write file synchronously (blocks until complete)
      fs.writeFileSync(filePath, jsonData, 'utf8');
      
      // Verify the file exists and has content
      if (fs.existsSync(filePath)) {
        const fileStats = fs.statSync(filePath);
        const fileSizeKB = Math.round(fileStats.size / 1024);
        
        // Verify content can be parsed back
        const savedData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        
        console.log(`✅ ${description} saved and verified:`);
        console.log(`   - File: ${filePath}`);
        console.log(`   - Size: ${fileSizeKB} KB`);
        console.log(`   - Records: ${Array.isArray(savedData) ? savedData.length : 'N/A'}`);
        
        return;
      } else {
        throw new Error(`File was not created: ${filePath}`);
      }
      
    } catch (error) {
      console.error(`❌ Error saving ${description}:`, error);
      throw error;
    }
  }
}