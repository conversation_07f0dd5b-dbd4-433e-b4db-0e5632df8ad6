import { ProductModel as BighubProductModel } from '../../bighub/models/product.model';
import { BIGHubCsvPort } from '../../bighub/ports/bighub-csv.port';
import { ProductModel } from '../../catalogue/models/product.model';
import { YamlRepo } from '../../configurations/repos/yaml.repo';
import { DumicorPort } from '../ports/dumicor.port';
import { DumicorModel } from '../models/dumicor.model';
import { ConnectorCode } from '../enums/connector-code.enum';
import { DumicorMapper } from '../mappers/dumicor.mapper';

export class DumicorService {
  dumicorRepo: DumicorPort;
  bighubRepo: BIGHubCsvPort
  configuration: YamlRepo

  constructor(
    repo: DumicorPort,
    bighubRepo: BIGHubCsvPort,
    configuration: YamlRepo
  ) {
    this.dumicorRepo = repo
    this.bighubRepo = bighubRepo
    this.configuration = configuration
  }

  async getProducts(): Promise<ProductModel[]> {
    const dumicor = await this.dumicorRepo.getAll();
    return dumicor.map((item: DumicorModel) => DumicorMapper
      .toProduct(item))
      .filter(product => product.stock > 0)
  }

  async getProductsBighub(): Promise<BighubProductModel[]> {
    const BIGHubServiceProducts = await this.dumicorRepo.getAll();

    let products = BIGHubServiceProducts.map((item: DumicorModel) => DumicorMapper.toProductBigHub(item))

    // products = await this.dumicorRepo.validateOffers(products)
    console.log('Dumicor products', products.length)
    products = products.filter(product => product.ean !== '')
    console.log('Dumicor products with EAN filter', products.length)
    // stock rules
    
    products = products.filter(product => product.stock > 0)
    console.log('Dumicor products with stock filter', products.length)
    // price rules
    products = products.filter(product => product.global_price > 0)
    // NaN's filter
    products = products.filter(product => !isNaN(product.global_price))
    console.log('Dumicor products with price filter', products.length)
    // tax rules
    
    products = await this.bighubRepo.applyTaxes(products, ConnectorCode.DUMICOR)

    return products;
  }
}