import { ProductModel as BighubProductModel } from '../../bighub/models/product.model';
import { BIGHubCsvPort } from '../../bighub/ports/bighub-csv.port';
import { ProductModel } from '../../catalogue/models/product.model';
import { YamlRepo } from '../../configurations/repos/yaml.repo';
import { ManchatoysPort } from '../ports/manchatoys.port';
import { ManchatoysModel } from '../models/manchatoys.model';
import { ConnectorCode } from '../enums/connector-code.enum';
import { ManchatoysMapper } from '../mappers/manchatoys.mapper';

export class ManchatoysService {
  manchatoysRepo: ManchatoysPort;
  bighubRepo: BIGHubCsvPort
  configuration: YamlRepo

  constructor(
    repo: ManchatoysPort,
    bighubRepo: BIGHubCsvPort,
    configuration: YamlRepo
  ) {
    this.manchatoysRepo = repo
    this.bighubRepo = bighubRepo
    this.configuration = configuration
  }

  async getProducts(): Promise<ProductModel[]> {
    const manchatoys = await this.manchatoysRepo.getAll();
    return manchatoys.map((item: ManchatoysModel) => ManchatoysMapper
      .toProduct(item))
      .filter(product => product.stock > 0)
  }

  async getProductsBighub(): Promise<BighubProductModel[]> {
    const BIGHubServiceProducts = await this.manchatoysRepo.getAll();

    let products = BIGHubServiceProducts.map((item: ManchatoysModel) => ManchatoysMapper.toProductBigHub(item))

    // products = await this.manchatoysRepo.validateOffers(products)
    console.log('Manchatoys products', products.length)
    products = products.filter(product => product.ean !== '')
    console.log('Manchatoys products with EAN filter', products.length)
    // stock rules
    products = products.filter(product => product.stock > 0)
    console.log('Manchatoys products with stock filter', products.length)
    // price rules
    products = products.filter(product => product.global_price > 0)
    // NaN's filter
    products = products.filter(product => !isNaN(product.global_price))
    console.log('Manchatoys products with price filter', products.length)
    // tax rules
    // 10 % discount in all products

    products = await this.bighubRepo.applyTaxes(products, ConnectorCode.MANCHATOYS)

    return products;
  }
}