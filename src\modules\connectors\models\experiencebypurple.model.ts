export interface ExperiencebypurpleModel {
    // Core product information
    id: number;
    name?: string;
    title: string;
    description: string;
    status?: string;
    sku: string;
    ean: string;
    barcode?: string;
    brand: string;
    price: number;
    stock: number;
    weight: number;
    
    // Image information
    image: string;
    images?: {
      image: {
        id: number;
        position: number;
        url: string;
      }
    }[];
    
    // Additional product metadata
    featured?: boolean;
    permalink?: string;
    created_at?: string;
    updated_at?: string;
    type?: string;
    
    // Category information
    categories?: {
      id: number;
      name: string;
      parent_id: number;
      permalink: string;
    }[];
    
    // Variants information
    variants?: {
      variant: {
        id: number;
        price: number;
        sku: string;
        barcode: string;
        stock: number;
        stock_unlimited?: boolean;
        weight: number;
        cost_per_item?: number;
        compare_at_price?: number;
        options?: any[];
        image?: any;
      }
    }[];
    
    // Custom fields
    custom_fields?: any[];
    
    // Additional Jumpseller-specific fields
    stock_unlimited?: boolean;
    compare_at_price?: number;
    cost_per_item?: number;
    body_html?: string; // For compatibility with Shopify model
    vendor?: string;    // For compatibility with Shopify model
  }