import { CommunicationMessage } from '@domain/entities/communication-message'

export namespace SendEmailCommunication {

  export type Request = Pick<CommunicationMessage, 'id'>

  export type Response = {
    result: Result
  }

  export type Result = boolean

  export interface Repository {
    sendEmailCommunication: (request: SendEmailCommunication.Request) => Promise<SendEmailCommunication.Result>
  }

  export interface UseCase {
    execute: (request: SendEmailCommunication.Request) => Promise<SendEmailCommunication.Response>
  }
}