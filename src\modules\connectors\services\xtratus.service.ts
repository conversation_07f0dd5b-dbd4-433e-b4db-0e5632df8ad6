import { ProductModel as BighubProductModel } from '../../bighub/models/product.model';
import { BIGHubCsvPort } from '../../bighub/ports/bighub-csv.port';
import { ProductModel } from '../../catalogue/models/product.model';
import { YamlRepo } from '../../configurations/repos/yaml.repo';
import { XtratusPort } from '../ports/xtratus.port';
import { XtratusModel } from '../models/xtratus.model';
import { ConnectorCode } from '../enums/connector-code.enum';
import { XtratusMapper } from '../mappers/xtratus.mapper';

export class XtratusService {
  xtratusRepo: XtratusPort;
  bighubRepo: BIGHubCsvPort
  configuration: YamlRepo

  constructor(
    repo: XtratusPort,
    bighubRepo: BIGHubCsvPort,
    configuration: YamlRepo
  ) {
    this.xtratusRepo = repo
    this.bighubRepo = bighubRepo
    this.configuration = configuration
  }

  async getProducts(): Promise<ProductModel[]> {
    const xtratus = await this.xtratusRepo.getAll();
    return xtratus.map((item: XtratusModel) => XtratusMapper
      .toProduct(item))
      .filter(product => product.stock > 0)
  }

  async getProductsBighub(): Promise<BighubProductModel[]> {
    const BIGHubServiceProducts = await this.xtratusRepo.getAll();

    let products = BIGHubServiceProducts.map((item: XtratusModel) => XtratusMapper.toProductBigHub(item))

    // products = await this.xtratusRepo.validateOffers(products)
    console.log('Xtratus products', products.length)
    products = products.filter(product => product.ean !== '')
    console.log('Xtratus products with EAN filter', products.length)
    // stock rules
    products = products.filter(product => product.stock > 0)
    console.log('Xtratus products with stock filter', products.length)
    // price rules
    products = products.filter(product => product.global_price > 0)
    // NaN's filter
    products = products.filter(product => !isNaN(product.global_price))
    console.log('Xtratus products with price filter', products.length)
    // tax rules

    products = await this.bighubRepo.applyTaxes(products, ConnectorCode.XTRATUS)

    return products;
  }
}