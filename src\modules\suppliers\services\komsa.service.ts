import { ProductModel as BighubProductModel } from '../../bighub/models/product.model';
import { BIGHubCsvPort } from '../../bighub/ports/bighub-csv.port';
import { ProductModel } from '../../catalogue/models/product.model';
import { SupplierCode } from '../enums/supplier-code.enum';
import { KomsaMapper } from '../mappers/komsa.mapper';
import { KomsaModel } from '../models/komsa.model';
import { KomsaPort } from '../ports/komsa.port';

export class KomsaService {
  komsaRepo: KomsaPort
  bighubRepo: BIGHubCsvPort

  constructor(
    repo: KomsaPort,
    bighubRepo: BIGHubCsvPort,
  ) {
    this.komsaRepo = repo
    this.bighubRepo = bighubRepo
  }

  async getProducts(): Promise<ProductModel[]> {
    const komsa = await this.komsaRepo.getAll();
    return komsa.map((item: KomsaModel) => KomsaMapper
      .toProduct(item))
      .filter(product => product.stock > 0)
  }

  async getProductsBighub(): Promise<BighubProductModel[]> {
    try {
      const BIGHubServiceProducts = await this.komsaRepo.getAll();

      if (BIGHubServiceProducts.length === 0) {
        throw new Error('No products found')
      }

      let products = BIGHubServiceProducts.map((item: KomsaModel) => KomsaMapper.toProductBigHub(item))
      console.log(products.length)
      // required fields rules
      products = products.filter(product => product.ean !== '')
      console.log(`after ean filter ${products.length}`)
      // stock rules
      products = products.filter(product => product.stock > 0)
      console.log(`after stock filter ${products.length}`)
      // price rules
      products = products.filter(product => product.global_price > 0)
      // NaN's filter
      products = products.filter(product => !isNaN(product.global_price))
      console.log(`after price filter ${products.length}`)
      // tax rules
      products = await this.bighubRepo.applyTaxes(products, SupplierCode.KOMSA)

      return products
    } catch (error: any) {
      console.error(error)
      return []
    }
  }
}

