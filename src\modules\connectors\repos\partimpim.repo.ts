import { PartimpimModel } from '../models/partimpim.model';
import { PartimpimPort } from '../ports/partimpim.port';
import got from 'got';
import { promisify } from 'util'
import stream from 'stream';
import iconv from 'iconv-lite';
import { parseString } from 'xml2js';
import { defer } from '../../../shared/utils/promise.util';

const pipeline = promisify(stream.pipeline)

export class PartimpimRepo implements PartimpimPort {

  private url: string;
  private rows: PartimpimModel[] = []

  constructor(url: string) {
    this.url = url;
  }

  async getAll(): Promise<PartimpimModel[]> {

    this.rows = []
    const deferred = defer()

    try {
      // Get the XML data
      const response = await got(this.url);
      const xmlData = response.body;

      // Parse XML
      parseString(xmlData, { 
        explicitArray: false,
        ignoreAttrs: false,
        tagNameProcessors: [(name) => name.replace(/^g:/, '')] // Remove g: prefix
      }, (err, result) => {
        if (err) {
          console.log('Erro ao fazer parse do XML', err);
          deferred.reject(err);
          return;
        }

        try {
          // Navigate to the items in the RSS feed
          const items = result.rss?.channel?.item || [];
          
          // Ensure items is an array
          const itemsArray = Array.isArray(items) ? items : [items];
          
          itemsArray.forEach(item => {
            this.processItem(item);
          });

          console.log(`Processados ${this.rows.length} produtos`);
          deferred.resolve();
        } catch (processError) {
          console.log('Erro ao processar items XML', processError);
          deferred.reject(processError);
        }
      });

    } catch (error) {
      console.log('Erro na obtenção do XML', error);
      deferred.reject(error);
    }

    await deferred.promise;
    return this.rows;
  }

  private processItem(item: any) {
    try {
      const newRow: PartimpimModel = {
        'Referencia Loja': this.getXmlValue(item.id),
        Nome: this.getXmlValue(item.title),
        Marca: this.getXmlValue(item.brand),
        Preço: Number(this.extractPrice(this.getXmlValue(item.price))),
        Link: this.getXmlValue(item.link),
        Disponibilidade: this.getXmlValue(item.availability),
        Familia: this.getXmlValue(item.product_type) || this.getXmlValue(item.google_product_category),
        Foto: this.getXmlValue(item.image_link),
        EAN: this.normalizeEAN(this.getXmlValue(item.gtin) || this.getXmlValue(item.ean)),
        'Refencia Produto': this.getXmlValue(item.id),
        Caracteristicas: this.getXmlValue(item.description),
        Portes: 0, // Not typically available in Google Shopping feeds
        Stock: Number(this.getXmlValue(item.stock) || this.getXmlValue(item.quantity)),
        'Preparação Mínimo': '', // Not available in Google Shopping feeds
        'Preparação Máximo': '', // Not available in Google Shopping feeds
        'Entrega Mínimo': '', // Not available in Google Shopping feeds
        'Entrega Máximo': '', // Not available in Google Shopping feeds
      };

      this.rows.push(newRow);
    } catch (error) {
      console.log('Erro ao processar item individual:', error);
    }
  }

  private getXmlValue(field: any): string {
    if (!field) return '';
    
    // Handle both direct values and nested structures
    if (typeof field === 'string') {
      return field;
    } else if (typeof field === 'object' && field._) {
      return field._;
    } else if (typeof field === 'object' && field.$t) {
      return field.$t;
    }
    
    return String(field) || '';
  }

  private normalizeEAN(ean: string): string {
    if (!ean) return '';
    
    // Remove any non-numeric characters
    const numericEAN = ean.replace(/\D/g, '');
    
    // If the length is already 13, return as is
    if (numericEAN.length === 13) {
      return numericEAN;
    }
    
    // If length is less than 13, prepend zeros
    if (numericEAN.length < 13) {
      return numericEAN.padStart(13, '0');
    }
    
    // If length is more than 13, truncate to 13 digits (take the last 13)
    return numericEAN.slice(-13);
  }

  private extractPrice(priceField: string): string {
    if (!priceField) return '';
    
    // Extract numeric price from "4.00 EUR" format
    const match = priceField.match(/(\d+\.?\d*)/);
    return match ? match[1] : '';
  }
}