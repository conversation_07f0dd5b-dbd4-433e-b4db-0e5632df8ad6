import { Marketplace, MarketplaceProps } from "../../../../domain/entities/marketplace"
import { UseCase } from "../use-case.interface"

export interface GetMarketplacesUseCaseInterface
  extends UseCase<GetMarketplacesUseCaseInterface.Request, GetMarketplacesUseCaseInterface.Response> {
  execute: (request: GetMarketplacesUseCaseInterface.Request) => Promise<GetMarketplacesUseCaseInterface.Response>
}

export namespace GetMarketplacesUseCaseInterface {
  export type Request = Pick<MarketplaceProps, 'status'>
  export type Response = Array<Marketplace> | void
}