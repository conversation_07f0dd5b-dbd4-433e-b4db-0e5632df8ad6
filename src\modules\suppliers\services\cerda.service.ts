import { ProductModel as BighubProductModel } from '../../bighub/models/product.model';
import { BIGHubCsvPort } from '../../bighub/ports/bighub-csv.port';
import { ProductModel } from '../../catalogue/models/product.model';
import { SupplierCode } from '../enums/supplier-code.enum';
import { CerdaMapper } from '../mappers/cerda.mapper';
import { CerdaModel } from '../models/cerda.model';
import { CerdaPort } from '../ports/cerda.port';

export class CerdaService {
  cerdaRepo: CerdaPort
  bighubRepo: BIGHubCsvPort


  constructor(
    repo: CerdaPort, 
    bighubRepo: BIGHubCsvPort,
  ) {
    this.cerdaRepo = repo
    this.bighubRepo = bighubRepo
  }

  async getProducts(): Promise<ProductModel[]> {
    const folder = '/';
    const filename = '117068.csv';
    const cerda = await this.cerdaRepo.get(folder, filename);
    // applyTaxes
    return cerda.map((item: CerdaModel) => CerdaMapper
      .toProduct(item))
      .filter(product => product.stock > 0)
  }

  async getProductsBighub(): Promise<BighubProductModel[]> {
    try {
      const filename = '117068.csv';
      const folder = '/';
      const BIGHubServiceProducts = await this.cerdaRepo.get(folder, filename);

      if (BIGHubServiceProducts.length === 0) {
        throw new Error('No products found')
      }
      let products = BIGHubServiceProducts.map((item: CerdaModel) => CerdaMapper.toProductBigHub(item))

      console.log('Total products: ',products.length)
      // required fields rules
      products = products.filter(product => product.ean !== '')
      console.log('products after ean filter: ',products.length)
      // stock rules
      products = products.filter(product => product.stock > 0)
      console.log('products after stock filter: ',products.length)
      // price rules
      products = products.filter(product => product.global_price > 0)
      // NaN's filter
      products = products.filter(product => !isNaN(product.global_price))
      console.log('products after price filter: ',products.length)
      // tax rules
      products = await this.bighubRepo.applyTaxes(products, SupplierCode.CERDA)
      
      return products;
      
    } catch (error: any) {
      console.error(error)
      return []
    }
  }
}