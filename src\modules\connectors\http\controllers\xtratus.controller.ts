import { Request, Response } from 'express';
import { BigHubService } from '../../../bighub/services/bighub.service';
import { XtratusService } from '../../services/xtratus.service';
import fs from 'fs'

export class XtratusController {

  xtratusService: XtratusService;
  bigHubService: BigHubService;
  
  constructor(bigHubService: BigHubService, xtratusService: XtratusService) {
    // coolaccesorios service
    this.xtratusService = xtratusService;
    // bighub service
    this.bigHubService = bigHubService;
  }
  
  async getProducts(req: Request, res: Response) {
    let response = {
      status: 200,
      message: 'Products found'
    }

    try {
      const products = await this.xtratusService.getProducts();
      res.status(response.status).json(products)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubCsv(req: Request, res: Response) {
    try {
      const filename = '1450-bighub-xtratus.csv';
      res.setHeader('Content-disposition', 'attachment; filename=' + filename);
      res.setHeader('Content-type', 'text/csv');
      fs.createReadStream(filename).pipe(res)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubExportCsv(req: Request, res: Response) {
    try {
      const filename = '1450-bighub-xtratus.csv';
      const products = await this.xtratusService.getProductsBighub();
      this.bigHubService.supplierCreateCsv(filename, products, 1450); //1450

      res.status(200).send('ok');
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }
}