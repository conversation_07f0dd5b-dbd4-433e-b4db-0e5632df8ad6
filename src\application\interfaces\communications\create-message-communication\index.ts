import { CommunicationMessage, CommunicationMessageEntity } from '@domain/entities/communication-message'

export namespace CreateMessageCommunication {

  export type Request = Omit<CommunicationMessage, 'id'>

  export type Response = {
    result: Result
  }

  export type Result = CommunicationMessageEntity

  export interface Repository {
    createMessageCommunication: (request: CreateMessageCommunication.Request) => Promise<CreateMessageCommunication.Result>
  }

  export interface UseCase {
    execute: (request: CreateMessageCommunication.Request) => Promise<CreateMessageCommunication.Response>
  }
}