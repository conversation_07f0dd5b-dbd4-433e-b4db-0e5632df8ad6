import { KomsaModel } from '../models/komsa.model';
import { KomsaPort } from '../ports/komsa.port';
import got from 'got';
import { promisify } from 'util'
import stream, { Transform } from 'stream';
import iconv from 'iconv-lite';
import { parse } from 'fast-csv';
import { defer } from '../../../shared/utils/promise.util';
import { BigHubService } from 'src/modules/bighub/services/bighub.service';

const pipeline = promisify(stream.pipeline)

export class KomsaRepo implements KomsaPort {

  private url: string;
  private headers: string[] = []

  private rows: KomsaModel[] = []

  constructor(url: string) {
    this.url = url;
  }

  async getAll(): Promise<KomsaModel[]> {

    this.rows = []
    const deferred = defer()

    const csvOptions = {
      headers: false,
      delimiter: ',',
      ignoreEmpty: true,
      discardUnmappedColumns: true,
      strictColumnHandling: false,
    }

    try {
      const parseCsv = parse(csvOptions)

      parseCsv.on('data', (row: any) => {
        this.processLine(row)
      })
      parseCsv.on('end', (rowCount: number) => {
        console.log(`Parsed ${rowCount} rows`)
        deferred.resolve()
      })
      await pipeline(got.stream(this.url), iconv.decodeStream('UTF-8'), parseCsv) //ISO-8859-1
      console.log('CSV obtido com sucesso')

      // After successfully processing the CSV, use BigHubService to download and upload to S3
      try {
        const s3Url = await BigHubService.downloadAndUploadToS3(this.url, 'komsa', 'seller',1423);
        console.log(`Uploaded to S3: ${s3Url}`);
      } catch (s3Error) {
        // Log the error but don't fail the main operation
        console.error('Error uploading to S3, but continuing with data processing:', s3Error);
      }
    } catch (error) {
      console.log('Erro na obtenção do csv', error)
      deferred.reject()
    }

    await deferred.promise

    return this.rows

  }

  private processLine(row: any) {
    if (this.headers.length === 0) {
      this.headers = row;
    } else {
      const newRow: KomsaModel = {
        KomsaArtikelnummer: row[0],
        EAN: row[1],
        HerstellerArtikelnummer: row[2],
        Bezeichnung: row[3],
        Status: row[4],
        Warengruppe: row[5],
        Hersteller: row[6],
        Marke: row[7],
        "EK in €": row[8],
        "UVP in €": row[9],
        "Gewicht in g": row[10],
        Bestand: row[11],
        "Export-Datum": row[12]
      }
      this.rows.push(newRow);
    }
  }
}