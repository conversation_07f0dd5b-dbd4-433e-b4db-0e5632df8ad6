import fs from 'fs';
import path from 'path';
import { createReadStream, createWriteStream } from 'fs';
import { createInterface } from 'readline';
import FtpClient from 'ftp';

/**
 * Simple logging utility with timestamps and log levels
 */
class Logger {
  static LOG_LEVELS = {
    DEBUG: 'DEBUG',
    INFO: 'INFO',
    WARNING: 'WARNING',
    ERROR: 'ERROR'
  };

  static log(level: string, message: string, data: any = null): string {
    const timestamp = new Date().toISOString();
    let logMessage = `[${timestamp}] [${level}] ${message}`;

    if (data) {
      if (typeof data === 'object' && data !== null) {
        try {
          // Safely stringify objects, hiding sensitive data
          const safeData = { ...data } as Record<string, any>;
          if (safeData.password) safeData.password = '******';
          logMessage += ` ${JSON.stringify(safeData)}`;
        } catch (e) {
          logMessage += ` [Object cannot be stringified]`;
        }
      } else {
        logMessage += ` ${data}`;
      }
    }

    console.log(logMessage);
    return logMessage;
  }

  static debug(message: string, data: any = null): string {
    return this.log(this.LOG_LEVELS.DEBUG, message, data);
  }

  static info(message: string, data: any = null): string {
    return this.log(this.LOG_LEVELS.INFO, message, data);
  }

  static warning(message: string, data: any = null): string {
    return this.log(this.LOG_LEVELS.WARNING, message, data);
  }

  static error(message: string, error: any = null): string {
    let errorDetails: any = null;

    if (error) {
      if (error instanceof Error) {
        errorDetails = {
          message: error.message,
          stack: error.stack,
          name: error.name
        };
      } else {
        errorDetails = error;
      }
    }

    return this.log(this.LOG_LEVELS.ERROR, message, errorDetails);
  }
}

/**
 * Downloads multiple files from an FTP server with enhanced logging
 * @param ftpOptions - FTP connection options
 * @param ftpDirectoryPath - Directory path on the FTP server where files are located
 * @param filenames - Array of filenames to download
 * @param destinationPath - Local directory where files will be saved
 * @returns Promise that resolves when all files are downloaded
 */
export async function extractFilesFromFTP(
    ftpOptions: FtpClient.Options,
    ftpDirectoryPath: string,
    filenames: string[],
    destinationPath: string
): Promise<string[]> {
    const client = new FtpClient();
    const downloadedFiles: string[] = [];

    // Sanitize FTP options for logging (hide password)
    const sanitizedOptions = { ...ftpOptions };
    if (sanitizedOptions.password) {
        sanitizedOptions.password = '******';
    }

    Logger.info('Starting FTP extraction process', {
        server: sanitizedOptions.host,
        user: sanitizedOptions.user,
        directory: ftpDirectoryPath,
        fileCount: filenames.length,
        destination: destinationPath
    });

    return new Promise<string[]>((resolve, reject) => {
        // Log connection attempt
        Logger.debug('Attempting to connect to FTP server', sanitizedOptions);

        // Set up event handlers for connection
        client.on('ready', async () => {
            Logger.info('Successfully connected to FTP server');

            try {
                // Ensure destination directory exists
                Logger.debug('Checking if destination directory exists', { path: destinationPath });
                if (!fs.existsSync(destinationPath)) {
                    Logger.info('Creating destination directory', { path: destinationPath });
                    fs.mkdirSync(destinationPath, { recursive: true });
                }

                Logger.info('Starting to download files', { fileCount: filenames.length });

                // Download each file sequentially
                for (const filename of filenames) {
                    const remotePath = path.posix.join(ftpDirectoryPath, filename);
                    const localPath = path.join(destinationPath, filename);

                    Logger.debug('Preparing to download file', {
                        remotePath,
                        localPath,
                        filename
                    });

                    await new Promise<void>((downloadResolve, downloadReject) => {
                        Logger.debug('Initiating FTP GET command', { remotePath });

                        client.get(remotePath, (err, stream) => {
                            if (err) {
                                Logger.error(`Failed to download file ${filename}`, err);
                                downloadReject(`Error downloading file ${filename}: ${err.message}`);
                                return;
                            }

                            Logger.debug('FTP GET successful, creating write stream', { localPath });
                            const fileStream = fs.createWriteStream(localPath);

                            Logger.debug('Piping FTP stream to file stream');
                            stream.pipe(fileStream);

                            fileStream.on('finish', () => {
                                const stats = fs.statSync(localPath);
                                Logger.info(`Downloaded ${filename} successfully`, {
                                    size: stats.size,
                                    path: localPath
                                });
                                downloadedFiles.push(localPath);
                                downloadResolve();
                            });

                            fileStream.on('error', (streamErr) => {
                                Logger.error(`Error writing file ${filename}`, streamErr);
                                downloadReject(`Error writing file ${filename}: ${streamErr.message}`);
                            });

                            // Add additional stream error handling
                            stream.on('error', (streamErr) => {
                                Logger.error(`FTP stream error for ${filename}`, streamErr);
                                downloadReject(`FTP stream error for ${filename}: ${streamErr.message}`);
                            });
                        });
                    });
                }

                Logger.info('All files downloaded successfully', {
                    totalFiles: downloadedFiles.length,
                    filesList: downloadedFiles
                });

                Logger.debug('Closing FTP connection');
                client.end();
                resolve(downloadedFiles);
            } catch (error) {
                Logger.error('Error during file download process', error);
                client.end();
                if (error instanceof Error) {
                    reject(`Error downloading files from FTP: ${error.message}`);
                } else {
                    reject(`Unknown error occurred during FTP download: ${error}`);
                }
            }
        });

        client.on('error', (err) => {
            Logger.error('FTP connection error', err);
            reject(`FTP connection error: ${err.message}`);
        });

        // Add more event handlers for debugging
        client.on('greeting', (msg) => {
            Logger.debug('FTP server greeting', { message: msg });
        });

        client.on('close', (hadError) => {
            Logger.debug('FTP connection closed', { hadError });
        });

        client.on('end', () => {
            Logger.debug('FTP connection ended');
        });

        // Connect to the FTP server
        try {
            Logger.debug('Calling connect method');
            client.connect(ftpOptions);
        } catch (connectError) {
            Logger.error('Exception during FTP connect call', connectError);
            reject(`Exception during FTP connect: ${connectError instanceof Error ? connectError.message : connectError}`);
        }
    });
}

/**
 * Combines multiple CSV files into a single CSV file, keeping only the first header
 * @param files - Array of file paths to combine
 * @param outputFilePath - Path where the combined CSV will be saved
 * @returns Promise that resolves when the combining is complete
 */
export async function combineMultipleFilesToSingleFileFromRoot(
    files: string[],
    outputFilePath: string
): Promise<void> {
    Logger.info('Starting CSV file combination process', {
        fileCount: files.length,
        outputPath: outputFilePath,
        files
    });

    if (files.length === 0) {
        const error = 'No files provided to combine';
        Logger.error(error);
        throw new Error(error);
    }

    Logger.debug('Creating output write stream', { path: outputFilePath });
    const outputStream = createWriteStream(outputFilePath);
    let headerProcessed = false;
    let totalLinesProcessed = 0;
    let filesProcessed = 0;

    for (let i = 0; i < files.length; i++) {
        const filePath = files[i];
        filesProcessed++;

        Logger.info(`Processing file ${i+1}/${files.length}`, { filePath });

        try {
            // Check if file exists
            if (!fs.existsSync(filePath)) {
                Logger.warning(`File does not exist, skipping`, { filePath });
                continue;
            }

            const fileStats = fs.statSync(filePath);
            Logger.debug('File statistics', {
                filePath,
                size: fileStats.size,
                created: fileStats.birthtime,
                modified: fileStats.mtime
            });

            Logger.debug('Creating read stream', { filePath });
            const fileStream = createReadStream(filePath);
            const rl = createInterface({
                input: fileStream,
                crlfDelay: Infinity
            });

            let isFirstLine = true;
            let fileLineCount = 0;

            Logger.debug('Starting to read file line by line', { filePath });

            for await (const line of rl) {
                fileLineCount++;

                // Skip header row for all files except the first one
                if (isFirstLine) {
                    isFirstLine = false;
                    Logger.debug('Processing first line of file', {
                        isHeader: true,
                        skipHeader: headerProcessed && i > 0,
                        fileIndex: i
                    });

                    if (headerProcessed && i > 0) {
                        Logger.debug('Skipping header for subsequent file', { fileIndex: i });
                        continue; // Skip header for subsequent files
                    }
                    headerProcessed = true;
                }

                outputStream.write(line + '\n');
                totalLinesProcessed++;
            }

            Logger.info(`Finished processing file`, {
                filePath,
                linesProcessed: fileLineCount
            });

        } catch (fileError) {
            Logger.error(`Error processing file ${filePath}`, fileError);
            // Continue processing other files despite errors
        }
    }

    return new Promise((resolve, reject) => {
        outputStream.on('finish', () => {
            const stats = fs.statSync(outputFilePath);
            Logger.info(`Successfully combined CSV files`, {
                filesProcessed,
                totalLinesProcessed,
                outputPath: outputFilePath,
                outputSize: stats.size
            });
            resolve();
        });

        outputStream.on('error', (err) => {
            Logger.error(`Error writing combined CSV`, err);
            reject(`Error writing combined CSV: ${err.message}`);
        });

        Logger.debug('Closing output stream');
        outputStream.end();
    });
}

// Additional utilities and troubleshooting code for FTP connections

/**
 * Utility function to test FTP connection
 * Use this to verify credentials before attempting file operations
 */
export async function testFTPConnection(ftpOptions: FtpClient.Options): Promise<boolean> {
  const client = new FtpClient();
  
  // Sanitize for logging
  const sanitizedOptions = { ...ftpOptions };
  if (sanitizedOptions.password) sanitizedOptions.password = '******';
  
  Logger.info('Testing FTP connection', sanitizedOptions);

  return new Promise<boolean>((resolve, reject) => {
    client.on('ready', () => {
      Logger.info('FTP connection test successful');
      client.end();
      resolve(true);
    });

    client.on('error', (err) => {
      Logger.error('FTP connection test failed', err);
      reject(new Error(`FTP connection error: ${err.message}`));
    });

    try {
      client.connect(ftpOptions);
    } catch (error) {
      Logger.error('Exception during FTP connect', error);
      reject(new Error(`FTP connect exception: ${error instanceof Error ? error.message : String(error)}`));
    }
  });
}

/**
 * Retries an FTP operation with exponential backoff
 * @param operation - Function to retry
 * @param maxRetries - Maximum number of retry attempts
 * @param initialDelay - Initial delay in milliseconds
 */
export async function retryFTPOperation<T>(
  operation: () => Promise<T>,
  maxRetries = 3,
  initialDelay = 1000
): Promise<T> {
  let lastError;
  let delay = initialDelay;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      Logger.info(`Operation attempt ${attempt}/${maxRetries}`);
      return await operation();
    } catch (error) {
      lastError = error;
      Logger.warning(`Attempt ${attempt} failed`, error);
      
      if (attempt < maxRetries) {
        Logger.info(`Waiting ${delay}ms before retry`);
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= 2; // Exponential backoff
      }
    }
  }
  
  throw lastError;
}

/**
 * List contents of an FTP directory
 * Useful for debugging path issues
 */
export async function listFTPDirectory(
  ftpOptions: FtpClient.Options,
  directoryPath: string = '/'
): Promise<string[]> {
  const client = new FtpClient();
  
  return new Promise<string[]>((resolve, reject) => {
    client.on('ready', () => {
      client.list(directoryPath, (err, list) => {
        if (err) {
          client.end();
          reject(new Error(`Error listing directory ${directoryPath}: ${err.message}`));
          return;
        }
        
        const fileNames = list.map(item => item.name);
        Logger.info(`Directory listing for ${directoryPath}`, { files: fileNames });
        client.end();
        resolve(fileNames);
      });
    });
    
    client.on('error', (err) => {
      Logger.error('FTP list error', err);
      reject(new Error(`FTP error during list: ${err.message}`));
    });
    
    client.connect(ftpOptions);
  });
}

/**
 * Usage example for the BigbuyController
 */

// First test the connection
async function safelyProcessBigBuyFiles() {
  const ftpOptions = {
    host: 'www.dropshippers.com.es',
    port: 21,
    user: 'bbehA3I6Z0qE',
    password: '5CVTVWt7ji', // Corrected password
    passive: true,
  };
  
  try {
    // 1. Test connection first
    await testFTPConnection(ftpOptions);
    
    // 2. List directory contents to verify path
    const files = await listFTPDirectory(ftpOptions, '/files/products/csv/');
    Logger.info('Available files in directory', { files });
    
    // 3. Filter to only get CSV files if needed
    const csvFiles = files.filter(file => file.endsWith('.csv'));
    
    // 4. Now we can safely proceed with download and combine
    const destinationPath = './downloads/bigbuy';
    const downloadedFiles = await extractFilesFromFTP(
      ftpOptions, 
      '/files/products/csv/', 
      csvFiles,
      destinationPath
    );
    
    // 5. Combine the files
    const outputFilePath = path.join(destinationPath, 'bigbuy_combined.csv');
    await combineMultipleFilesToSingleFileFromRoot(downloadedFiles, outputFilePath);
    
    return {
      success: true,
      downloadedFiles: downloadedFiles.length,
      outputFilePath
    };
  } catch (error) {
    Logger.error('Safe process failed', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}
