import { FitnisModel } from '../models/fitnis.model';
import { FitnisPort } from '../ports/fitnis.port';
import got from 'got';
import { promisify } from 'util'
import stream, { Transform } from 'stream';
import iconv from 'iconv-lite';
import { parse } from 'fast-csv';
import { defer } from '../../../shared/utils/promise.util';

const pipeline = promisify(stream.pipeline)

export class FitnisRepo implements FitnisPort {

  private url: string;
  private headers: string[] = []

  private rows: FitnisModel[] = []

  constructor(url: string) {
    this.url = url;
  }

  async getAll(): Promise<FitnisModel[]> {

    this.rows = []
    const deferred = defer()

    const csvOptions = {
      headers: false,
      delimiter: ';',
      ignoreEmpty: true,
      discardUnmappedColumns: true,
      strictColumnHandling: false,
    }

    try {

      const parseCsv = parse(csvOptions)

      parseCsv.on('data', (row: any) => {
        this.processLine(row)
      })  
      parseCsv.on('end', (rowCount: number) => {
        console.log(`Parsed ${rowCount} rows`)
        deferred.resolve()
      })
      await pipeline(got.stream(this.url), iconv.decodeStream('UTF-8'), parseCsv) //ISO-8859-1
      console.log('CSV obtido com sucesso')
    } catch (error) {
      console.log('Erro na obtenção do csv', error)
      deferred.reject()
    }

    await deferred.promise
    
    return this.rows

  }

  private processLine(row: any) {
    if (this.headers.length === 0) {
      this.headers = row;
    } else {
      const newRow: FitnisModel = {
        ean: row[0],
        sku: row[1],
        title: row[2],
        description: row[3],
        brand: row[4],
        global_price: row[5],
        stock: row[6],
        condition: row[7],
        image: row[8],
      }
      this.rows.push(newRow);
    }
  }
}