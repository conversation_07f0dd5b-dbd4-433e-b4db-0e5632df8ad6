import got from 'got';
import crypto from 'crypto';
import { BeautyfortPort } from '../ports/beautyfort.port';
import { BeautyfortModel, BeautyFortCredentials } from '../models/beautyfort.model';
import { YamlRepo } from 'src/modules/configurations/repos/yaml.repo';
import { Logger, LogLevel, logToFile } from '@shared/utils/logger.util';

export class BeautyfortRepo implements BeautyfortPort {
    private rows: BeautyfortModel[] = [];
    private configuration: YamlRepo;
    private stockFileFormat: string;
    private logger: Logger;
    private enableLogging: boolean = false; // Added logging control variable, default false

    constructor(configuration: YamlRepo, enableLogging: boolean = false) {
        this.configuration = configuration;
        this.stockFileFormat = 'XML'; // Default format, can be XML, JSON, Delimited, or XLSX
        this.enableLogging = enableLogging; // Initialize logging control from constructor
        this.logger = new Logger('BeautyfortRepo', {
            minLevel: LogLevel.DEBUG,
            logToFile: this.enableLogging, // Use enableLogging to control file logging
            logFilePath: './logs/beautyfort'
        });
    }

    /**
     * Set logging state
     * @param enable - Whether to enable logging
     */
    setLogging(enable: boolean): void {
        this.enableLogging = enable;
        // Update logger configuration
        this.logger = new Logger('BeautyfortRepo', {
            minLevel: LogLevel.DEBUG,
            logToFile: this.enableLogging,
            logFilePath: './logs/beautyfort'
        });
    }

    /**
     * Fetch all products from the Beauty Fort API
     * @returns Promise with array of products
     */
    async getAll(): Promise<BeautyfortModel[]> {
        return new Promise(async (resolve, reject) => {
            try {
                // Reset rows array for fresh data
                this.rows = [];

                // Get configuration from YAML
                const loginCredentials: BeautyFortCredentials = this.configuration.get('conexions', 'supplier', 'beautyfort');

                if (!loginCredentials) {
                    throw new Error('Beauty Fort configuration not found in config.yml. Please check your configuration.');
                }

                if (!loginCredentials.username || !loginCredentials.password) {
                    throw new Error('Beauty Fort API credentials (username/password) are missing in configuration.');
                }

                // Generate authentication credentials
                const authCredentials = this.generateAuthCredentials(loginCredentials.password);

                // Create SOAP request
                const xml = this.buildGetStockFileRequest(
                    loginCredentials.username,
                    authCredentials,
                    loginCredentials.test_mode
                );

                if (this.enableLogging) {
                    this.logger.info(`Fetching Beauty Fort stock data at ${new Date()}`);
                    this.logger.info(`Using API URL: ${loginCredentials.api_url}`);
                    this.logger.info(`Using format: ${this.stockFileFormat}`);
                    this.logger.info(`Test mode: ${loginCredentials.test_mode ? 'Enabled' : 'Disabled'}`);

                    // Log sanitized request for debugging
                    const sanitizedXml = this.logger.sanitize(xml);
                    this.logger.debug('SOAP Request:', sanitizedXml.substring(0, 1000) + '...');
                }

                try {
                    // Send SOAP request to API with increased timeout
                    const response = await got.post(loginCredentials.api_url, {
                        body: xml,
                        headers: {
                            'Content-Type': 'text/xml; charset=UTF-8',
                            'Accept': 'text/xml',
                            'Content-Length': Buffer.byteLength(xml, 'utf8').toString()
                        },
                        responseType: 'text',
                        timeout: {
                            request: 60000 // 60 second timeout
                        },
                        retry: {
                            limit: 2,
                            methods: ['POST']
                        }
                    });

                    if (this.enableLogging) {
                        this.logger.info(`Response status: ${response.statusCode}`);
                        this.logger.info(`Response size: ${response.body.length} bytes`);

                        // Log the API exchange for debugging
                        this.logger.logApiExchange(xml, response.body, 'beautyfort');
                    }

                    // Check if response contains a SOAP fault
                    if (response.body.includes('<soap:Fault>') || response.body.includes('<faultstring>')) {
                        const faultMatch = /<faultstring>(.*?)<\/faultstring>/i.exec(response.body);
                        const faultMessage = faultMatch && faultMatch[1]
                            ? `SOAP Fault: ${faultMatch[1]}`
                            : 'Unknown SOAP Fault';

                        if (this.enableLogging) {
                            this.logger.error(faultMessage, { responseBody: response.body.substring(0, 1000) });
                        }
                        throw new Error(`Beauty Fort API Error: ${faultMessage}`);
                    }

                    if (this.enableLogging) {
                        // Log a sample of the response for debugging
                        this.logger.debug('Response (sample):', response.body.substring(0, 1000) + '...');
                    }

                    // Parse the response based on the stock file format
                    const products = this.parseResponse(response.body);

                    if (!products || products.length === 0) {
                        if (this.enableLogging) {
                            this.logger.warn('No products found in the response.');
                        }
                        resolve([]);
                        return;
                    }

                    // Process each product
                    for (const product of products) {
                        this.processProduct(product);
                    }

                    if (this.enableLogging) {
                        this.logger.info(`Finished processing ${this.rows.length} products at ${new Date()}`);
                    }
                    resolve(this.rows);
                } catch (error) {
                    // More detailed error handling
                    const gotError = error as any;

                    if (gotError.response) {
                        if (this.enableLogging) {
                            // Log error details
                            this.logger.error(`API Error: Status ${gotError.response.statusCode}`, {
                                statusCode: gotError.response.statusCode,
                                responseBody: gotError.response.body.substring(0, 2000)
                            });

                            // Log the error response
                            if (this.enableLogging) {
                                logToFile(
                                    `Status code: ${gotError.response.statusCode}\n\nResponse body:\n${gotError.response.body}`,
                                    `beautyfort-error-${new Date().toISOString().replace(/[:.-]/g, '_')}.txt`,
                                    './logs/beautyfort/errors',
                                    false
                                );
                            }
                        }

                        // Try to extract more specific error information
                        let errorMessage = `Beauty Fort API Error: Status ${gotError.response.statusCode}`;

                        // Try to find SOAP fault messages in the response
                        if (typeof gotError.response.body === 'string' &&
                            gotError.response.body.includes('<soap:Fault>')) {
                            const faultMatch = /<faultstring>(.*?)<\/faultstring>/i.exec(gotError.response.body);
                            if (faultMatch && faultMatch[1]) {
                                errorMessage += ` - SOAP Fault: ${faultMatch[1]}`;
                            }
                        }

                        throw new Error(errorMessage);
                    } else if (gotError.code === 'ETIMEDOUT') {
                        if (this.enableLogging) {
                            this.logger.error('API request timed out', gotError);
                        }
                        throw new Error('Beauty Fort API request timed out. The server took too long to respond.');
                    } else if (gotError.code === 'ECONNREFUSED') {
                        if (this.enableLogging) {
                            this.logger.error('Connection refused', gotError);
                        }
                        throw new Error('Connection refused. The Beauty Fort API server is not accepting connections.');
                    } else if (gotError.code === 'ENOTFOUND') {
                        if (this.enableLogging) {
                            this.logger.error('DNS lookup failed', gotError);
                        }
                        throw new Error(`DNS lookup failed. The API URL ${loginCredentials.api_url} could not be resolved.`);
                    } else {
                        if (this.enableLogging) {
                            this.logger.error('Unknown API error', gotError);
                        }
                        throw error;
                    }
                }
            } catch (error) {
                if (this.enableLogging) {
                    this.logger.error('Error fetching Beauty Fort products:', error);
                }
                reject(error);
            }
        });
    }

    /**
     * Generate authentication credentials for Beauty Fort API
     * Exact match to PHP's implementation
     * @param apiSecret - Secret key from configuration
     * @returns Authentication credentials object
     */
    private generateAuthCredentials(apiSecret: string): { nonce: string; created: string; password: string } {
        // PHP's uniqid() function generates a 13-character string based on the current timestamp
        // It returns something like: "5f8e9b3c4d2a1"
        const generateUniqid = () => {
            const time = Math.floor(Date.now() / 1000).toString(16);
            return time + Math.floor(Math.random() * 1000).toString(16).padStart(3, '0');
        };

        // Generate nonce using PHP-compatible uniqid
        const nonce = generateUniqid();

        // Generate created timestamp in ISO 8601 format (exactly like PHP's date("c"))
        // Make sure to use the same timezone as your PHP script
        const now = new Date();
        const pad = (num: number): string => num.toString().padStart(2, '0');

        // Format timezone like PHP (e.g., +01:00)
        const offset = now.getTimezoneOffset() * -1;
        const offsetHours = pad(Math.floor(Math.abs(offset) / 60));
        const offsetMinutes = pad(Math.abs(offset) % 60);
        const offsetSign = offset >= 0 ? '+' : '-';

        // Format created date exactly like PHP's date("c")
        const created = `${now.getFullYear()}-${pad(now.getMonth() + 1)}-${pad(now.getDate())}T${pad(now.getHours())}:${pad(now.getMinutes())}:${pad(now.getSeconds())}${offsetSign}${offsetHours}:${offsetMinutes}`;

        // Combine values to match PHP's $nonce.$created.API_SECRET
        const passwordRaw = nonce + created + apiSecret;

        // PHP sha1 returns a 40-character hex string
        const sha1Hex = crypto.createHash('sha1').update(passwordRaw).digest('hex');

        // In PHP, when base64_encode is called on a hex string,
        // PHP treats each character as a byte value
        // We need to convert the hex string to a binary buffer first, then base64 encode

        // This is where previous attempts might have gone wrong
        // PHP treats the 40-character hex string as 40 bytes, not as a hex representation of 20 bytes
        // So we need to create a buffer from the hex string treating each character as a byte
        const buffer = Buffer.from(sha1Hex, 'ascii');

        // Now base64 encode the buffer
        const password = buffer.toString('base64');

        if (this.enableLogging) {
            // Log everything for debugging
            this.logger.debug('PHP compatibility check:', {
                nonce,
                created,
                passwordRaw: passwordRaw,
                sha1Hex: sha1Hex,
                password: password,
                // Also log the length of each value
                nonceLength: nonce.length,
                createdLength: created.length,
                sha1HexLength: sha1Hex.length,
                passwordLength: password.length
            });
        }

        return {
            nonce,
            created,
            password
        };
    }

    /**
     * Escape XML special characters
     * @param str - String to escape
     * @returns Escaped string
     */
    private escapeXml(str: string): string {
        return str
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&apos;');
    }

    /**
     * Build SOAP request XML for GetStockFile
     * @param username - API username
     * @param auth - Authentication credentials
     * @param testMode - Whether to use test mode
     * @returns SOAP request XML
     */
    private buildGetStockFileRequest(
        username: string,
        auth: { nonce: string; created: string; password: string },
        testMode: boolean
    ): string {
        // Define stock file fields
        const stockFileFields = [
            'StockCode',
            'FullName',
            'Brand',
            'Collection',
            'Category',
            'Description',
            'Price',
            'Quantity',
            'Barcode',
            'Size',
            'StockLevel',
            'HighResImageUrl',
            'ThumbnailImageUrl'
        ];

        // Build stock file fields XML
        const stockFileFieldsXml = stockFileFields
            .map(field => `\t\t\t\t<bf:StockFileField>${this.escapeXml(field)}</bf:StockFileField>`)
            .join('\n');

        // Build XML request - Fixed indentation which was causing issues
        const xml = `<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:bf="http://www.beautyfort.com/api/">
  <soap:Header>
    <!-- Authentication information -->
    <bf:AuthHeader>
      <bf:Username>${this.escapeXml(username)}</bf:Username>
      <bf:Nonce>${this.escapeXml(auth.nonce)}</bf:Nonce>
      <bf:Created>${this.escapeXml(auth.created)}</bf:Created>
      <bf:Password>${this.escapeXml(auth.password)}</bf:Password>
    </bf:AuthHeader>
  </soap:Header>
  <soap:Body>
    <bf:GetStockFileRequest>
      <!-- Must match the test mode of the account you're using -->
      <bf:TestMode>${testMode ? 'true' : 'false'}</bf:TestMode>
      
      <!-- Options are Delimited, XML, JSON, XLSX -->
      <bf:StockFileFormat>${this.escapeXml(this.stockFileFormat)}</bf:StockFileFormat>
      
      ${this.stockFileFormat === 'Delimited' ? `<!-- Delimiter for delimited format -->
      <bf:FieldDelimiter>,</bf:FieldDelimiter>` : ''}
      
      <!-- What stock file fields to return -->
      <bf:StockFileFields>
${stockFileFieldsXml}
      </bf:StockFileFields>
      
      <!-- What field to sort the stock file by -->
      <bf:SortBy>FullName</bf:SortBy>
      
      <!-- Force the character encoding of the returned stock file -->
      <bf:StockFileEncoding>UTF-8</bf:StockFileEncoding>
    </bf:GetStockFileRequest>
  </soap:Body>
</soap:Envelope>`;
        return xml;
    }

    /**
     * Parse API response based on stock file format
     * @param responseData - Raw response data from API
     * @returns Array of products
     */
    private parseResponse(responseData: string): BeautyfortModel[] {
        try {
            if (this.stockFileFormat === 'XML') {
                if (this.enableLogging) {
                    this.logger.info('XML response received, attempting to parse products');
                }

                // Extract the base64 encoded data from the SOAP response
                const fileMatch = /<ns1:File>([\s\S]*?)<\/ns1:File>/.exec(responseData);

                if (fileMatch && fileMatch[1]) {
                    // Decode the base64 content
                    const decodedContent = Buffer.from(fileMatch[1], 'base64').toString('utf-8');
                    
                    if (this.enableLogging) {
                        this.logger.debug('Decoded XML content (sample):', decodedContent.substring(0, 500) + '...');
                    }

                    // Find product entries in the decoded XML
                    // The actual element name might be different, look for common patterns
                    const products: BeautyfortModel[] = [];

                    // Try different possible XML element names for products
                    const possibleProductTags = ['Product', 'item', 'product', 'Item', 'StockItem'];

                    let productMatches = null;
                    let matchingTag = '';

                    for (const tag of possibleProductTags) {
                        const regex = new RegExp(`<${tag}>(.*?)<\/${tag}>`, 'gs');
                        const matches = [...decodedContent.matchAll(regex)];

                        if (matches.length > 0) {
                            productMatches = matches;
                            matchingTag = tag;
                            if (this.enableLogging) {
                                this.logger.info(`Found ${matches.length} products with tag <${tag}>`);
                            }
                            break;
                        }
                    }

                    if (productMatches && productMatches.length > 0) {
                        for (const match of productMatches) {
                            try {
                                const productXml = match[1];
                                const product = this.extractProductData(productXml);
                                if (product) {
                                    products.push(product);
                                }
                            } catch (error) {
                                if (this.enableLogging) {
                                    this.logger.error('Error extracting product data', error);
                                }
                            }
                        }

                        if (this.enableLogging) {
                            this.logger.info(`Extracted ${products.length} products from XML response`);
                        }
                        return products;
                    } else {
                        // Log a sample of the decoded content to help debug
                        if (this.enableLogging) {
                            this.logger.warn('No product tags found in the decoded XML. Content sample:', decodedContent.substring(0, 500));
                        }
                    }
                } else {
                    if (this.enableLogging) {
                        this.logger.warn('No base64 encoded file found in the response');
                    }
                }
            } else if (this.stockFileFormat === 'JSON') {
                // Find the start of the JSON data in the SOAP response
                const jsonStart = responseData.indexOf('{');
                const jsonEnd = responseData.lastIndexOf('}') + 1;

                if (jsonStart >= 0 && jsonEnd > jsonStart) {
                    const jsonData = responseData.substring(jsonStart, jsonEnd);
                    const parsed = JSON.parse(jsonData);
                    return Array.isArray(parsed) ? parsed : [parsed];
                }
            } else if (this.stockFileFormat === 'Delimited') {
                // For CSV/delimited data, split by lines and parse
                if (this.enableLogging) {
                    this.logger.info('Delimited response received, attempting to parse');
                }

                // Basic CSV parsing
                const lines = responseData.split('\n');
                const csvStartIndex = lines.findIndex(line => line.includes(','));

                if (csvStartIndex >= 0) {
                    const csvLines = lines.slice(csvStartIndex);
                    const headers = csvLines[0].split(',').map(h => h.trim());

                    if (this.enableLogging) {
                        this.logger.debug('CSV Headers:', headers);
                    }

                    const products: BeautyfortModel[] = [];

                    for (let i = 1; i < csvLines.length; i++) {
                        const line = csvLines[i].trim();
                        if (!line) continue;

                        const values = line.split(',');
                        const product: any = {};

                        headers.forEach((header, index) => {
                            // Handle numeric fields
                            if (header === 'Price' || header === 'Quantity' || header === 'StockLevel') {
                                product[header] = parseFloat(values[index]) || 0;
                            } else {
                                product[header] = values[index]?.trim() || '';
                            }
                        });

                        products.push(product as BeautyfortModel);
                    }

                    if (this.enableLogging) {
                        this.logger.info(`Parsed ${products.length} products from delimited response`);
                    }
                    return products;
                }
            }

            // Default fallback
            if (this.enableLogging) {
                this.logger.warn('Response format not recognized or empty response');
            }
            return [];
        } catch (error) {
            if (this.enableLogging) {
                this.logger.error('Error parsing response:', error);
            }
            return [];
        }
    }

    /**
     * Extract product data from XML fragment
     * @param productXml - XML fragment containing product data
     * @returns Product object
     */
    private extractProductData(productXml: string): BeautyfortModel {
        // Extract fields using regex (simple approach - use a proper XML parser in production)
        const product: any = {};

        const fields = [
            'StockCode', 'FullName', 'Brand', 'Collection', 'Category',
            'Description', 'Price', 'Quantity', 'Barcode', 'Size',
            'StockLevel', 'HighResImageUrl', 'ThumbnailImageUrl'
        ];

        fields.forEach(field => {
            const regex = new RegExp(`<${field}>(.*?)<\/${field}>`, 's');
            const match = productXml.match(regex);
            if (match) {
                // Get the raw value
                let value = match[1].trim();

                // Decode HTML entities for text fields
                if (field !== 'Price' && field !== 'Quantity' && field !== 'StockLevel') {
                    value = this.decodeHtmlEntities(value);
                }

                // Convert numeric fields - ensure we handle empty values properly
                if (field === 'Price' || field === 'Quantity' || field === 'StockLevel') {
                    // Parse as number, but if empty or NaN, use 0
                    product[field] = value ? parseFloat(value) || 0 : 0;
                } else {
                    product[field] = value;
                }
            } else if (field === 'Quantity' || field === 'StockLevel') {
                // Default to 0 for missing stock fields
                product[field] = 0;
            }
        });

        return product as BeautyfortModel;
    }

    /**
     * Decode HTML entities to their actual characters
     * @param html - String with HTML entities
     * @returns Decoded string
     */
    private decodeHtmlEntities(html: string): string {
        // Create a temporary element to decode HTML entities
        if (!html) return '';

        // Replace common HTML entities
        return html
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&quot;/g, '"')
            .replace(/&#039;/g, "'")
            .replace(/&apos;/g, "'")
            .replace(/&#x27;/g, "'")
            .replace(/&#x2F;/g, '/')
            .replace(/&#39;/g, "'")
            .replace(/&#47;/g, '/')
            .replace(/&nbsp;/g, ' ');
    }

    /**
     * Process a product from the API response
     * @param product - Product data from API
     */
    private processProduct(product: BeautyfortModel) {
        // Map product data to match your existing structure
        const ean = product.Barcode ? product.Barcode.split(/\s*,\s*/).map(code => code.trim()) : [];

        // Use StockLevel instead of Quantity for stock
        const stock = product.StockLevel || 0;

        // Make sure the image URLs are preserved and not replaced with placeholders
        // Only use placeholder if the image URL is empty or undefined
        const highResImageUrl = product.HighResImageUrl && product.HighResImageUrl.trim() !== ''
            ? product.HighResImageUrl
            : 'https://cdn.bighub.store/image/product-placeholder.png';

        const thumbnailImageUrl = product.ThumbnailImageUrl && product.ThumbnailImageUrl.trim() !== ''
            ? product.ThumbnailImageUrl
            : 'https://cdn.bighub.store/image/product-placeholder.png';

        if (this.enableLogging) {
            // Log the image URLs for debugging
            this.logger.debug('Product image URLs:', {
                sku: product.StockCode,
                highResImageUrl,
                thumbnailImageUrl
            });
        }

        const categories = product.Category ? [product.Category] : [];
        const brandAttribute = product.Gender || '';
        const brand = product.Brand || '';
        const attributes = product.Size || '';
        const tags = '';

        // Handle multiple EANs by creating separate products
        if (ean.length > 1) {
            // Create a separate product for each EAN with its portion of the stock
            const stockPerVariant = Math.floor(stock / ean.length);

            ean.forEach(singleEan => {
                // Create a new product object for each EAN
                const newProduct: BeautyfortModel = {
                    ...product,
                    EANs: [singleEan],
                    Barcode: singleEan,
                    Stock: stockPerVariant,
                    CompleteFamilies: categories,
                    NombreColor: attributes,
                    BrandName: brand,
                    Tags: tags,
                    HighResImageUrl: highResImageUrl,
                    ThumbnailImageUrl: thumbnailImageUrl
                };

                this.rows.push(newProduct);
            });
        } else {
            // Create a new product object with a single EAN
            const newProduct: BeautyfortModel = {
                ...product,
                EANs: ean,
                Stock: stock,
                CompleteFamilies: categories,
                NombreColor: attributes,
                BrandName: brand,
                Tags: tags,
                HighResImageUrl: highResImageUrl,
                ThumbnailImageUrl: thumbnailImageUrl
            };

            this.rows.push(newProduct);
        }
    }

    /**
     * Helper method to create a product entry
     * @param product - Original product data
     * @param ean - EAN array for this variant
     * @param stock - Stock allocation for this variant
     * @param imageUrl - Processed high-res image URL
     * @param thumbnailUrl - Processed thumbnail URL
     */
    private createProductEntry(
        product: BeautyfortModel,
        ean: string[],
        stock: number,
        imageUrl: string,
        thumbnailUrl: string
    ) {
        let imagePlaceholder: string = 'https://cdn.bighub.store/image/product-placeholder.png';

        const categories = product.Category ? [product.Category] : [];
        const brandAttribute = product.Gender || '';
        const brand = product.Brand || '';
        const attributes = product.Size || '';
        const tags = '';

        // Create a new product object
        const newProduct: BeautyfortModel = {
            ...product,
            EANs: ean,                   // Use the specific EAN for this variant
            Barcode: ean[0] || '',       // Set the first EAN as the primary barcode
            Stock: stock,                // Use the calculated stock for this variant
            CompleteFamilies: categories,
            NombreColor: attributes,
            BrandName: brand,
            Tags: tags,
            HighResImageUrl: imageUrl || imagePlaceholder,
            ThumbnailImageUrl: thumbnailUrl || imagePlaceholder
        };

        this.rows.push(newProduct);
    }

    /**
     * Process the image URL from Beauty Fort
     * @param url - Original image URL from API
     * @returns Processed image URL
     */
    private processImageUrl(url: string): string {
        if (!url) return '';

        // Check if the URL is already properly formatted
        if (url.startsWith('https://www.beautyfort.com/pic/')) {
            // The URL appears to be encoded - make sure it's correctly formatted
            // Beauty Fort uses these encoded URLs for their product images
            // Sometimes we need to add parameters to make them work properly

            if (this.enableLogging) {
                // Log the URL for debugging
                this.logger.debug('Processing image URL:', url);
            }

            // Return the URL directly - it's already properly formatted
            // If your testing shows this doesn't work, you might need 
            // to add query parameters or modify the URL
            return url;
        }

        // If URL doesn't match expected format, return default
        return url;
    }

    /**
     * Test the API connection with minimal request
     * This can be used to diagnose connection issues
     */
    async testConnection(): Promise<boolean> {
        try {
            if (this.enableLogging) {
                this.logger.info('Testing Beauty Fort API connection...');
            }

            // Get configuration
            const loginCredentials: BeautyFortCredentials = this.configuration.get('conexions', 'supplier', 'beautyfort');

            if (!loginCredentials) {
                if (this.enableLogging) {
                    this.logger.error('Beauty Fort configuration not found');
                }
                return false;
            }

            // Generate authentication
            const authCredentials = this.generateAuthCredentials(loginCredentials.password);

            // Build a minimal request with just one field - FIXED XML syntax
            const minimalRequest = `<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:bf="http://www.beautyfort.com/api/">
  <soap:Header>
    <bf:AuthHeader>
      <bf:Username>${this.escapeXml(loginCredentials.username)}</bf:Username>
      <bf:Nonce>${this.escapeXml(authCredentials.nonce)}</bf:Nonce>
      <bf:Created>${this.escapeXml(authCredentials.created)}</bf:Created>
      <bf:Password>${this.escapeXml(authCredentials.password)}</bf:Password>
    </bf:AuthHeader>
  </soap:Header>
  <soap:Body>
    <bf:GetStockFileRequest>
      <bf:TestMode>${loginCredentials.test_mode ? 'true' : 'false'}</bf:TestMode>
      <bf:StockFileFormat>XML</bf:StockFileFormat>
      <bf:StockFileFields>
        <bf:StockFileField>StockCode</bf:StockFileField>
      </bf:StockFileFields>
      <bf:SortBy>StockCode</bf:SortBy>
      <bf:StockFileEncoding>UTF-8</bf:StockFileEncoding>
    </bf:GetStockFileRequest>
  </soap:Body>
</soap:Envelope>`;

            // Log sanitized request
            if (this.enableLogging) {
                const sanitizedRequest = this.logger.sanitize(minimalRequest);
                this.logger.debug('Test Request:', sanitizedRequest);

                // Save request for analysis
                logToFile(
                    sanitizedRequest,
                    `connection-test-request-${new Date().toISOString().replace(/[:.-]/g, '_')}.xml`,
                    './logs/beautyfort/tests',
                    false
                );
            }

            // Send request
            const response = await got.post(loginCredentials.api_url, {
                body: minimalRequest,
                headers: {
                    'Content-Type': 'text/xml; charset=UTF-8',
                    'Accept': 'text/xml',
                    'Content-Length': Buffer.byteLength(minimalRequest, 'utf8').toString()
                },
                responseType: 'text',
                timeout: {
                    request: 30000
                }
            });

            if (this.enableLogging) {
                this.logger.info(`Test connection successful. Status: ${response.statusCode}`);

                // Save response for analysis
                logToFile(
                    response.body,
                    `connection-test-response-${new Date().toISOString().replace(/[:.-]/g, '_')}.xml`,
                    './logs/beautyfort/tests',
                    false
                );
            }

            // Check for SOAP faults
            if (response.body.includes('<soap:Fault>') || response.body.includes('<faultstring>')) {
                const faultMatch = /<faultstring>(.*?)<\/faultstring>/i.exec(response.body);
                if (faultMatch && faultMatch[1]) {
                    if (this.enableLogging) {
                        this.logger.error(`SOAP Fault: ${faultMatch[1]}`);
                    }
                    return false;
                }
            }

            // Check if response contains products
            const hasProducts = response.body.includes('<Product>') ||
                response.body.includes('<GetStockFileResponse>');

            if (hasProducts) {
                if (this.enableLogging) {
                    this.logger.info('API connection is working correctly');
                }
                return true;
            } else {
                if (this.enableLogging) {
                    this.logger.warn('Response received but no products found');
                }
                return false;
            }

        } catch (error) {
            if (this.enableLogging) {
                this.logger.error('Connection test failed:', error);

                // Provide specific error guidance
                const err = error as any;
                if (err.code === 'ETIMEDOUT') {
                    this.logger.error('The API server is not responding (timeout)');
                } else if (err.code === 'ECONNREFUSED') {
                    this.logger.error('The API server refused the connection');
                } else if (err.code === 'ENOTFOUND') {
                    this.logger.error('The API URL could not be resolved (DNS error)');
                } else if (err.response?.statusCode === 500) {
                    this.logger.error('The API server returned a 500 Internal Server Error', {
                        response: err.response.body.substring(0, 1000)
                    });

                    // Save the error response for analysis
                    logToFile(
                        err.response.body,
                        `connection-test-error-${new Date().toISOString().replace(/[:.-]/g, '_')}.xml`,
                        './logs/beautyfort/errors',
                        false
                    );

                    // Try to analyze the 500 error
                    if (err.response.body.includes('SOAP-ENV:Client')) {
                        this.logger.error('This appears to be a client error. Check your API credentials and test mode setting.');
                    } else if (err.response.body.includes('SOAP-ENV:Server')) {
                        this.logger.error('This appears to be a server error. The Beauty Fort API may be experiencing issues.');
                    }
                }
            }

            return false;
        }
    }

    /**
     * Try different API formats to diagnose issues
     * Sometimes switching format can help identify problems
     */
    async tryDifferentFormats(): Promise<string | null> {
        // Store original format
        const originalFormat = this.stockFileFormat;

        if (this.enableLogging) {
            this.logger.info('Trying different API formats to diagnose issues...');
        }

        // Try each format
        const formats = ['XML', 'JSON', 'Delimited', 'XLSX'];

        for (const format of formats) {
            if (this.enableLogging) {
                this.logger.info(`Testing format: ${format}`);
            }
            this.stockFileFormat = format;

            try {
                const success = await this.testConnection();
                if (success) {
                    if (this.enableLogging) {
                        this.logger.info(`Format ${format} works successfully!`);
                    }
                    return format; // Return the first successful format
                } else {
                    if (this.enableLogging) {
                        this.logger.warn(`Format ${format} did not return products`);
                    }
                }
            } catch (error) {
                if (this.enableLogging) {
                    this.logger.error(`Format ${format} failed:`, error);
                }
            }
        }

        // Restore original format if none worked
        this.stockFileFormat = originalFormat;
        if (this.enableLogging) {
            this.logger.info('Format testing complete. No successful formats found.');
        }
        return null;
    }

    /**
     * Fix common issues with XML formatting that can cause 500 errors
     */
    fixXmlFormatting(): void {
        // One common issue is excessive whitespace in XML which some SOAP servers reject
        if (this.enableLogging) {
            this.logger.info('Applying XML formatting fixes that might resolve 500 errors');
        }

        // Modify the XML building method to use a different indentation style
        // This is an internal method that's called by this.buildGetStockFileRequest
        this.stockFileFormat = 'JSON'; // Try a different format, JSON often has fewer formatting issues

        if (this.enableLogging) {
            this.logger.info('Changed format to JSON which may resolve XML formatting issues');
        }
    }
}