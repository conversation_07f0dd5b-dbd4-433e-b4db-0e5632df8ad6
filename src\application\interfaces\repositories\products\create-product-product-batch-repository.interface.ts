import { Product } from '../../../../domain/entities/product'

export interface CreateProductBatchRepositoryInterface {
  createProductBatch(request: CreateProductBatchRepositoryInterface.Request, chunkSize: number): Promise<CreateProductBatchRepositoryInterface.Response>
}

export namespace CreateProductBatchRepositoryInterface {
  export type Request = Array<Omit<Product, 'id' | 'created_at' | 'updated_at'>>
  export type Response = boolean
}