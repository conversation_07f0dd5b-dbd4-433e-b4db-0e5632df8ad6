import { MusaModel } from '../models/musa.model';
import got from 'got';
import { YamlRepo } from '../../configurations/repos/yaml.repo';
import { ProductModel } from 'src/modules/bighub/models/product.model';

export class MusaRepo {
  private rows: MusaModel[] = [];
  private configuration: YamlRepo;
  private baseUrl: string;
  private consumerKey: string;
  private consumerSecret: string;

  constructor(configuration: YamlRepo) {
    this.configuration = configuration;

    // Load Musa configuration
    const wooConfig = this.configuration.get('conexions', 'connector', 'musa');

    // Fix URL if needed - ensure no www prefix is used due to certificate issues
    let baseUrl = wooConfig.api_url;
    if (baseUrl.includes('www.musanaturalcosmetics.com')) {
      baseUrl = baseUrl.replace('www.musanaturalcosmetics.com', 'musanaturalcosmetics.com');
      console.log('URL modified to match SSL certificate: ', baseUrl);
    }

    this.baseUrl = baseUrl;
    this.consumerKey = wooConfig.consumer_key;
    this.consumerSecret = wooConfig.consumer_secret;
  }

  validateOffers(products: ProductModel[]): Promise<ProductModel[]> {
    throw new Error('Method not implemented.');
  }

  /**
   * Get all products from Musa including variations
   */
  async getAll(): Promise<MusaModel[]> {
    try {
      console.log('Fetching products from Musa');
      this.rows = []; // Reset products array

      // Musa API pagination
      let page = 1;
      const perPage = 100;
      let hasMoreProducts = true;

      while (hasMoreProducts) {
        // Construct endpoint with pagination and authentication
        const endpoint = `/wp-json/wc/v3/products?page=${page}&per_page=${perPage}&status=publish&consumer_key=${this.consumerKey}&consumer_secret=${this.consumerSecret}`;

        // Make API request with certificate validation disabled
        const response = await got(`${this.baseUrl}${endpoint}`, {
          responseType: 'json',
          https: {
            rejectUnauthorized: false
          }
        });

        // Process products
        const products = response.body as any[];

        if (products.length === 0) {
          // No more products to fetch
          hasMoreProducts = false;
        } else {
          // Process each product (and its variations if any)
          for (const product of products) {
            // Check if product has variations
            if (product.type === 'variable' && product.variations && product.variations.length > 0) {
              // Product has variations, fetch and process each one
              await this.processVariableProduct(product);
            } else {
              // Simple product, process as before
              this.rows.push(this.mapToModel(product));
            }
          }

          // Check if we need to fetch more pages
          if (products.length < perPage) {
            hasMoreProducts = false;
          } else {
            page++;
          }
        }
      }

      console.log(`Fetched ${this.rows.length} products (including variations) from Musa`);
      return this.rows;
    } catch (error) {
      console.error('Error fetching products from Musa:', error);
      throw error;
    }
  }

  /**
   * Process a variable product by fetching and mapping all its variations
   */
  private async processVariableProduct(product: any): Promise<void> {
    try {
      // First fetch the summary of all variations for this product
      const variationsEndpoint = `/wp-json/wc/v3/products/${product.id}/variations?per_page=100&consumer_key=${this.consumerKey}&consumer_secret=${this.consumerSecret}`;
      
      const response = await got(`${this.baseUrl}${variationsEndpoint}`, {
        responseType: 'json',
        https: {
          rejectUnauthorized: false
        }
      });

      const variationsSummary = response.body as any[];
      
      if (variationsSummary.length === 0) {
        // No variations found, add parent product instead
        this.rows.push(this.mapToModel(product));
        return;
      }

      // Process each variation by making an additional API call to get complete data
      for (const variationSummary of variationsSummary) {
        try {
          // Make a separate API call for each variation to get complete data including meta_data
          const variationDetailEndpoint = `/wp-json/wc/v3/products/${product.id}/variations/${variationSummary.id}?consumer_key=${this.consumerKey}&consumer_secret=${this.consumerSecret}`;
          
          const detailResponse = await got(`${this.baseUrl}${variationDetailEndpoint}`, {
            responseType: 'json',
            https: {
              rejectUnauthorized: false
            }
          });
          
          const variationDetail = detailResponse.body as any;
          
          // Map the detailed variation to model, passing parent product info
          this.rows.push(this.mapVariationToModel(variationDetail, product));
        } catch (variationError) {
          console.error(`Error fetching details for variation ${variationSummary.id}:`, variationError);
          // If we can't fetch detailed variation, use the summary data we already have
          this.rows.push(this.mapVariationToModel(variationSummary, product));
        }
      }
    } catch (error) {
      console.error(`Error fetching variations for product ${product.id}:`, error);
      // If we can't fetch variations, add the parent product as fallback
      this.rows.push(this.mapToModel(product));
    }
  }

  /**
   * Map a variation to our model, combining parent and variation data
   */
  private mapVariationToModel(variation: any, parentProduct: any): MusaModel {
    // Start with the parent product attributes
    const baseProduct = this.mapToModel(parentProduct);
    
    // Format variation attributes for the name
    let attributeString = '';
    if (variation.attributes && Array.isArray(variation.attributes)) {
      const attributeParts = variation.attributes
        .filter((attr: any) => attr.option) // Only consider attributes with options
        .map((attr: any) => `${attr.option}`); // Just use the option value
      
      if (attributeParts.length > 0) {
        attributeString = ` - ${attributeParts.join(', ')}`;
      }
    }

    // Prepare SKU - use variation SKU if available, otherwise combine parent SKU with variation ID
    const sku = variation.sku || `${parentProduct.sku}-${variation.id}`;
    
    // Try to extract EAN code from various possible locations for variations
    let ean = '';
    
    // First check if the variation has a direct global_unique_id property
    if (variation.global_unique_id) {
      ean = variation.global_unique_id;
    }
    // If not found as direct property, try in meta_data
    else if (variation.meta_data && Array.isArray(variation.meta_data)) {
      // Try with variable_global_unique_id_0
      const variableEanMeta0 = variation.meta_data.find((meta: any) => meta.key === 'variable_global_unique_id_0');
      if (variableEanMeta0 && variableEanMeta0.value) {
        ean = variableEanMeta0.value;
      }
      
      // If not found, try with variable_global_unique_id
      if (!ean) {
        const variableEanMeta = variation.meta_data.find((meta: any) => meta.key === 'variable_global_unique_id');
        if (variableEanMeta) {
          // Handle both array and string formats
          if (Array.isArray(variableEanMeta.value) && variableEanMeta.value.length > 0) {
            ean = variableEanMeta.value[0];
          } else if (typeof variableEanMeta.value === 'string') {
            ean = variableEanMeta.value;
          }
        }
      }
      
      // Try with _global_unique_id if variable version not found
      if (!ean) {
        const globalEanMeta = variation.meta_data.find((meta: any) => meta.key === '_global_unique_id');
        if (globalEanMeta && globalEanMeta.value) {
          ean = globalEanMeta.value;
        }
      }
      
      // Try plain global_unique_id
      if (!ean) {
        const plainEanMeta = variation.meta_data.find((meta: any) => meta.key === 'global_unique_id');
        if (plainEanMeta && plainEanMeta.value) {
          ean = plainEanMeta.value;
        }
      }
    }
    
    // If not found in meta_data, try variation SKU
    if (!ean && variation.sku) {
      if (variation.sku.includes('-')) {
        ean = variation.sku.split('-')[0];
      } else {
        ean = variation.sku;
      }
    }
    
    // Fallback to parent product EAN if still not found
    if (!ean && baseProduct.ean) {
      ean = baseProduct.ean;
    }

    // Get stock quantity for variation
    let stockQuantity = 0;
    if (variation.manage_stock && variation.stock_quantity !== null) {
      stockQuantity = parseInt(variation.stock_quantity, 10);
    } else if (variation.stock_status === 'instock') {
      stockQuantity = 99; // Default stock for in-stock variations without specific quantity
    }

    // Get price - use variation price if available
    const price = parseFloat(variation.regular_price || variation.price || parentProduct.regular_price || parentProduct.price || '0');

    // Get image - use variation image if available, otherwise parent image
    let imageUrl = baseProduct.image;
    if (variation.image && variation.image.src) {
      imageUrl = variation.image.src;
    }

    return {
      ...baseProduct,
      sku: sku,
      name: `${baseProduct.name}${attributeString}`, // Append variation attributes to name
      ean: ean,
      price: price,
      quantity: stockQuantity,
      image: imageUrl,
      weight: variation.weight ? parseFloat(variation.weight) : baseProduct.weight,
      permalink: variation.permalink || baseProduct.permalink
    };
  }

  /**
   * Map Musa API product to internal model
   */
  private mapToModel(product: any): MusaModel {
    const imagePlaceholder = 'https://cdn.bighub.store/image/product-placeholder.png';

    // Function to remove emojis from text
    const removeEmojis = (text: string): string => {
      if (!text) return '';

      // This regex pattern matches most emoji characters
      return text.replace(/[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F700}-\u{1F77F}\u{1F780}-\u{1F7FF}\u{1F800}-\u{1F8FF}\u{1F900}-\u{1F9FF}\u{1FA00}-\u{1FA6F}\u{1FA70}-\u{1FAFF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu, '');
    };

    // Extract the first image if available
    let imageUrl = imagePlaceholder;
    if (product.images && Array.isArray(product.images) && product.images.length > 0) {
      // The image is in the src property of the first image object
      imageUrl = product.images[0].src || imagePlaceholder;
    }

    // Extract category information
    const category = product.categories && product.categories.length > 0 ? product.categories[0].name : '';

    // Try to extract EAN code from various possible locations
    let ean = '';
    
    // Try meta_data for _global_unique_id
    if (product.meta_data && Array.isArray(product.meta_data)) {
      // First try with _global_unique_id
      const globalEanMeta = product.meta_data.find((meta: any) => meta.key === '_global_unique_id');
      if (globalEanMeta && globalEanMeta.value) {
        ean = globalEanMeta.value;
      }
      
      // Also try with global_unique_id (without underscore)
      if (!ean) {
        const plainEanMeta = product.meta_data.find((meta: any) => meta.key === 'global_unique_id');
        if (plainEanMeta && plainEanMeta.value) {
          ean = plainEanMeta.value;
        }
      }
    }
    
    // If not found in meta_data, try direct property
    if (!ean && product.global_unique_id) {
      ean = product.global_unique_id;
    }
    
    // If still not found, try to extract from SKU as fallback
    if (!ean && product.sku) {
      // Many stores use SKU pattern with EAN as first part
      if (product.sku.includes('-')) {
        ean = product.sku.split('-')[0];
      } else {
        ean = product.sku;
      }
    }

    // Get actual stock quantity or calculate based on stock status and manage_stock
    let stockQuantity = 0;
    if (product.manage_stock && product.stock_quantity !== null) {
      // If the product manages stock, use the actual stock quantity
      stockQuantity = parseInt(product.stock_quantity, 10);
    } else if (product.stock_status === 'instock') {
      // If stock not managed but product is in stock, default to a reasonable value
      stockQuantity = 99; // Default stock for in-stock items without specific quantity
    }

    // Generate description from short_description or full description
    let description = '';
    if (product.short_description) {
      // Strip HTML tags for clean text
      description = product.short_description.replace(/<[^>]*>?/gm, '');
    } else if (product.description) {
      // Use full description as fallback, also strip HTML
      description = product.description.replace(/<[^>]*>?/gm, '');
    }

    // Extract brand from meta_data with key "_wc_gla_brand"
    let brand = '';
    if (product.meta_data && Array.isArray(product.meta_data)) {
      const brandMeta = product.meta_data.find((meta: any) => meta.key === '_wc_gla_brand');
      if (brandMeta && brandMeta.value) {
        brand = brandMeta.value;
      }
    }

    return {
      sku: product.sku || String(product.id),
      name: product.name || '',
      description_short: removeEmojis(description) || '',
      ean: ean, // Use the properly extracted EAN code
      price: parseFloat(product.regular_price || product.price || '0'),
      quantity: stockQuantity, // Use the proper stock quantity logic
      brand: brand, // Now using the brand from meta_data
      condition: 'new',
      image: imageUrl,
      weight: product.weight ? parseFloat(product.weight) : 0,
      permalink: product.permalink || '',
      category: category
    };
  }
}