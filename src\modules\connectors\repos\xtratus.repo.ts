import { <PERSON><PERSON><PERSON> } from "jsdom";
import { XtratusModel } from '../models/xtratus.model';
import got from 'got';
import { YamlRepo } from '../../configurations/repos/yaml.repo';
import { XtratusPort } from '../ports/xtratus.port';
import { ProductModel } from 'src/modules/bighub/models/product.model';
import { logToFile } from "@shared/utils/logger.util";

/**
 * Cleans an HTML string by removing all tags and strange characters.
 * @param htmlString The raw HTML string.
 * @returns A clean text description enclosed in double quotes.
 */
export function cleanHTML(htmlString: string): string {
  // Parse the HTML
  const dom = new JSDOM(htmlString);

  // Extract text content without HTML tags
  let cleanText = dom.window.document.body.textContent || "";

  // Remove all existing double quotes
  cleanText = cleanText.replace(/"/g, "");

  // Remove extra whitespace and special characters
  cleanText = cleanText.replace(/\s+/g, " ").trim();

  return cleanText;
}

export class XtratusRepo implements XtratusPort {
  private rows: XtratusModel[] = [];
  private configuration: YamlRepo;

  constructor(configuration: YamlRepo) {
    this.configuration = configuration;
  }

  validateOffers(products: ProductModel[]): Promise<ProductModel[]> {
    throw new Error('Method not implemented.');
  }

  async getAll(): Promise<XtratusModel[]> {
    return new Promise(async (resolve, reject) => {
      try {
        const loginCredentials = this.configuration.get('conexions', 'connector', 'xtratus');
        const apiUrl = loginCredentials.api_url;
        const token = loginCredentials.x_shopify_access_token;
        const productsAmount = 100

        let nextPageUrl: string | null = `${apiUrl}/admin/api/2024-07/products.json?limit=${productsAmount}`;

        let pageNr = 1
        while (nextPageUrl) {
          //console.log(`Fetching: Page ${pageNr} <-> ${pageNr * productsAmount} products`);
          const response = await got<{ products: XtratusModel[] }>(nextPageUrl, {
            responseType: 'json',
            headers: {
              'X-Shopify-Access-Token': token,
            },
          });

          pageNr++
          const products = response.body.products;

          if (!products || products.length === 0) {
            //console.log('No more products to fetch.');
            break;
          }

          products.forEach(product => this.processLine(product));

          const linkHeaderRaw = response.headers.link || response.headers.Link;
          const linkHeader = Array.isArray(linkHeaderRaw) ? linkHeaderRaw.join(', ') : linkHeaderRaw;

          nextPageUrl = this.extractNextPageUrl(linkHeader);
        }

        resolve(this.rows);
      } catch (error) {
        reject(error);
      }
    });
  }

  private extractNextPageUrl(linkHeader: string | undefined): string | null {
    if (!linkHeader) return null;
    const match = linkHeader.match(/<(.*?)>; rel="next"/);
    return match ? match[1] : null;
  }

  private processLine(product: XtratusModel) {
    const imagePlaceholder = 'https://cdn.bighub.store/image/product-placeholder.png';

    // Extract first variant (assuming at least one exists)
    const variant = product.variants?.[0];

    // Ensure product.image is properly typed
    const imageSrc = (product.image && typeof product.image === 'object' && 'src' in product.image)
      ? (product.image as { src: string }).src
      : imagePlaceholder;

    //logToFile(product, 'products-xtratus.json', 'logs/xtratus');

    const newRow: XtratusModel = {
      ...product,
      ean: variant?.barcode !== undefined && variant?.barcode !== null ? ((variant?.barcode.length === 12 ? `0${variant?.barcode}` : variant?.barcode) || '') : '',
      sku: variant?.sku || '',
      title: product.title || '',
      description: '', // Keeping empty as in original code
      brand: product.vendor || '',
      price: parseFloat(variant?.price || '0'),
      stock: variant?.inventory_quantity, // stockQuantity, // Using our calculated stock value
      image: imageSrc,
      weight: variant?.weight ?? 0,
    };

    this.rows.push(newRow);
  }
}
