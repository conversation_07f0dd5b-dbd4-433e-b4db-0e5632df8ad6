import { Offer, OfferProps } from "../../../../domain/entities/offer"
import { UseCase } from "../use-case.interface"

export interface UpdateOfferStockByEanUseCaseInterface
  extends UseCase<UpdateOfferStockByEanUseCaseInterface.Request, UpdateOfferStockByEanUseCaseInterface.Response> {
  execute: (request: UpdateOfferStockByEanUseCaseInterface.Request) => Promise<UpdateOfferStockByEanUseCaseInterface.Response>
}

export namespace UpdateOfferStockByEanUseCaseInterface {
  export type Request = Pick<OfferProps, 'ean' | 'user_id' | 'stock' | 'transaction_id'>
  export type Response = boolean | void
}