import { Request, Response } from 'express';
import { CerdaService } from '../../services/cerda.service';
import { BigHubService } from '../../../bighub/services/bighub.service';
import fs from 'fs'

export class CerdaController {

  cerdaService: CerdaService;
  bigHubService: BigHubService;
  
  constructor(bigHubService: BigHubService, cerdaService: CerdaService) {
    // cerda service
    this.cerdaService = cerdaService;
    // bighub service
    this.bigHubService = bigHubService;
  }
  
  async getProducts(req: Request, res: Response) {
    let response = {
      status: 200,
      message: 'Products found'
    }

    try {
      const products = await this.cerdaService.getProducts();
      res.status(response.status).json(products)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubCsv(req: Request, res: Response) {
    try {
      const filename = 'bighub-cerda.csv';
      res.setHeader('Content-disposition', 'attachment; filename=' + filename);
      res.setHeader('Content-type', 'text/csv');
      fs.createReadStream(filename).pipe(res)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubExportCsv(req: Request, res: Response) {
    try {
      const filename = 'bighub-cerda.csv';
      const products = await this.cerdaService.getProductsBighub();
      console.log(products.length)
      await this.bigHubService.supplierCreateCsv(filename, products, 1405);
      
      res.status(200).send('ok');
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }
}