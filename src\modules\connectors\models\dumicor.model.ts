export interface DumicorModel {
    // Core product identification
    id?: number | string
    product_id?: number | string
    
    // Provider and identification data
    provider?: string
    ean: string
    sku: string
    
    // Product information
    title: string
    description: string
    brand: string
    vendor?: string
    body_html?: string
    
    // Pricing and inventory
    price: number
    stock: number
    condition?: string
    
    // Physical attributes
    weight: number
    image: string
    
    // Variant specific fields
    variant_id?: number | string
    variant_title?: string
    option1?: string
    option2?: string
    option3?: string
    
    // Collections and relationships
    variants?: VariantModel[]
    images?: ImageModel[]
    options?: OptionModel[]
}

// Additional interfaces to strongly type the nested objects
export interface VariantModel {
    id: number | string
    product_id: number | string
    title: string
    price: string
    sku: string
    position?: number
    inventory_quantity?: number
    barcode?: string
    weight?: number
    weight_unit?: string
    image_id?: number | string
    option1?: string
    option2?: string
    option3?: string
}

export interface ImageModel {
    id: number | string
    product_id?: number | string
    position?: number
    src: string
    variant_ids?: (number | string)[]
}

export interface OptionModel {
    id?: number | string
    product_id?: number | string
    name: string
    position?: number
    values?: string[]
}