import got from 'got';
import { YamlRepo } from '../../configurations/repos/yaml.repo';
import { ImperiodasmalasPort } from '../ports/imperiodasmalas.port';
import { ProductModel } from 'src/modules/bighub/models/product.model';
import { ImperiodasmalasModel } from '../models/imperiodasmalas.model';

export class ImperiodasmalasRepo implements ImperiodasmalasPort {
  private rows: ImperiodasmalasModel[] = [];
  private configuration: YamlRepo;

  constructor(configuration: YamlRepo) {
    this.configuration = configuration;
  }
  
  validateOffers(products: ProductModel[]): Promise<ProductModel[]> {
    throw new Error('Method not implemented.');
  }

  async getAll(): Promise<ImperiodasmalasModel[]> {
    return new Promise(async (resolve, reject) => {
      try {
        const loginCredentials = this.configuration.get('conexions', 'connector', 'imperiodasmalas');
        const apiUrl = loginCredentials.api_url;
        const apiKey = loginCredentials.api_key;
        const email = loginCredentials.email;
        
        // Default pagination settings based on the API response
        const itemsPerPage = 250; // Default from the response
        let page = 1;
        let totalItems = 0;

        console.log('Starting to fetch products from Imperio das Malas');
        
        do {
          console.log(`Fetching: Page ${page}`);
          
          try {
            const response = await got<{ 
              products: any[]; 
              params: { 
                total_items: string;
                items_per_page: number;
                page: number; 
              } 
            }>(
              `${apiUrl}/api/products?page=${page}&status=A&items_per_page=${itemsPerPage}`, 
              {
                responseType: 'json',
                headers: {
                  'Authorization': `Basic ${Buffer.from(`${email}:${apiKey}`).toString('base64')}`,
                },
                timeout: {
                  request: 30000 // 30 seconds timeout for the main request
                }
              }
            );

            // Check if we have valid products
            if (!response?.body?.products) {
              console.log('No products returned from API.');
              break;
            }

            const products = response.body.products;
            
            // Update total items and items per page from the response
            totalItems = parseInt(response.body.params?.total_items || '0', 10);
            const currentPageItemsPerPage = response.body.params?.items_per_page || itemsPerPage;
            
            console.log(`Found ${products.length} products on page ${page}. Total: ${totalItems}`);

            if (!products || products.length === 0) {
              console.log('No more products to fetch.');
              break;
            }

            // Process each product
            for (const product of products) {
              try {
                // Instead of trying to fetch detailed info which might not return complete data,
                // use the product data we already have from the list
                this.processLine(product);

              } catch (error) {
                if (error instanceof Error) {
                  console.error(`Error processing product ID ${product.product_id}:`, error.message);
                } else {
                  console.error(`Error processing product ID ${product.product_id}:`, error);
                }
              }
            }

            // Move to next page
            page++;
            
            // Check if we need to fetch more pages
            // We'll continue if we haven't fetched all items yet
            // Calculate based on current page, items per page, and total items
            if ((page - 1) * currentPageItemsPerPage >= totalItems) {
              console.log('Reached the end of available products');
              break;
            }

          } catch (error) {
            if (error instanceof Error) {
              console.error(`Error fetching page ${page}:`, error.message);
            } else {
              console.error(`Error fetching page ${page}:`, error);
            }
            // Try next page if current page fails
            page++;
            
            // If we've failed too many times, break the loop
            if (page > 5) {
              console.error('Too many failed page requests, stopping the process');
              break;
            }
          }
        } while (true); // We'll break out of the loop when needed

        console.log(`Total products processed: ${this.rows.length}`);
        resolve(this.rows);
      } catch (error) {
        console.error('Fatal error in getAll():', error);
        reject(error);
      }
    });
  }

  private processLine(product: any) {
    const imagePlaceholder = 'https://cdn.bighub.store/image/product-placeholder.png';

    // Extract main image URL
    let imageUrl = imagePlaceholder;
    if (product.main_pair && product.main_pair.detailed && product.main_pair.detailed.image_path) {
      imageUrl = product.main_pair.detailed.image_path;
    }

    // Handle price and discount calculations
    const basePrice = parseFloat(product.base_price || product.price || '0');
    const discountedPrice = parseFloat(product.price || '0');
    const discountPercent = product.discount_prc ? parseInt(product.discount_prc, 10) : 0;
    
    // Handle stock
    const stock = parseInt(product.amount || '0', 10);

    // Process main image and additional images
    const additionalImages = [];
    if (product.image_pairs) {
      for (const pairId in product.image_pairs) {
        if (product.image_pairs[pairId]?.detailed?.image_path) {
          additionalImages.push(product.image_pairs[pairId].detailed.image_path);
        }
      }
    }

    // Extract product features
    const features: Record<string, any> = {};
    if (product.product_features) {
      for (const featureId in product.product_features) {
        const feature = product.product_features[featureId];
        if (feature.description && (feature.value || feature.value_int)) {
          features[feature.description] = feature.value || feature.value_int;
        }
      }
    }

    // Extract variation data
    const variations: Record<string, any> = {};
    if (product.variation_features) {
      for (const featureId in product.variation_features) {
        const variation = product.variation_features[featureId];
        if (variation.description && variation.variant) {
          variations[variation.description] = variation.variant;
        }
      }
    }

    // Get product details
    const newRow: ImperiodasmalasModel = {
      product_id: product.product_id,
      ean: product.product_features.GTIN,
      sku: product.product_code || '',
      title: product.product || '',
      description: product.full_description ? product.full_description.replace(/\n/g, '').replace(/\r/g, '') : '',
      brand: features['Marca'] || '', // Try to extract brand from features
      price: discountedPrice,
      original_price: basePrice,
      discount_percent: discountPercent,
      stock: stock,
      image: imageUrl,
      additional_images: additionalImages,
      weight: parseFloat(product.weight || '0'),
      
      // Imperio das Malas specific fields
      product_type: product.product_type || '',
      category_ids: Array.isArray(product.category_ids) ? product.category_ids : [],
      product_features: features,
      status: product.status || '',
      list_price: parseFloat(product.list_price || '0'),
      timestamp: parseInt(product.timestamp || '0', 10),
      updated_timestamp: parseInt(product.updated_timestamp || '0', 10),
      seo_name: product.seo_name || '',
      main_category: product.main_category || null,
      
      // Variation data
      variation_group_id: product.variation_group_id || null,
      variation_group_code: product.variation_group_code || '',
      variation_features: variations,
      variation_name: product.variation_name || '',
      
      // Shipping and dimensions
      shipping_freight: parseFloat(product.shipping_freight || '0'),
      length: parseFloat(product.length || '0'),
      width: parseFloat(product.width || '0'),
      height: parseFloat(product.height || '0'),
      
      // Promotions
      has_promotions: product.promotions && Object.keys(product.promotions).length > 0,
    };
    //console.log(newRow)
    this.rows.push(newRow);
  }
}