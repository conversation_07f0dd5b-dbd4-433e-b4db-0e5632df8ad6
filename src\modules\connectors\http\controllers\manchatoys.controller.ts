import { Request, Response } from 'express';
import { BigHubService } from '../../../bighub/services/bighub.service';
import { ManchatoysService } from '../../services/manchatoys.service';
import fs from 'fs'

export class ManchatoysController {

  manchatoysService: ManchatoysService;
  bigHubService: BigHubService;
  
  constructor(bigHubService: BigHubService, manchatoysService: ManchatoysService) {
    // coolaccesorios service
    this.manchatoysService = manchatoysService;
    // bighub service
    this.bigHubService = bigHubService;
  }
  
  async getProducts(req: Request, res: Response) {
    let response = {
      status: 200,
      message: 'Products found'
    }

    try {
      const products = await this.manchatoysService.getProducts();
      res.status(response.status).json(products)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubCsv(req: Request, res: Response) {
    try {
      const filename = '1393-bighub-manchatoys.csv';
      res.setHeader('Content-disposition', 'attachment; filename=' + filename);
      res.setHeader('Content-type', 'text/csv');
      fs.createReadStream(filename).pipe(res)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubExportCsv(req: Request, res: Response) {
    try {
      const filename = '1393-bighub-manchatoys.csv';
      const products = await this.manchatoysService.getProductsBighub();
      this.bigHubService.supplierCreateCsv(filename, products, 1393); //1393

      res.status(200).send('ok');
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }
}