import { BigbuyPriceModel } from '../models/bigbuy-price.model';
import { BigbuyPricePort } from '../ports/bigbuy-price.port';
import got from 'got';
import * as yaml from 'js-yaml';
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join } from 'path';

interface BigBuyConfig {
    supplier: string;
    api_token: string;
    api_url: string;
    test_mode: boolean;
}

interface ConfigFile {
    conexions: BigBuyConfig[];
}

interface RequestState {
    lastRequest: number;
    totalRequests: number;
}

export class BigbuyPriceRepo implements BigbuyPricePort {
    private baseUrl: string;
    private accessToken: string;
    private testMode: boolean;
    private prices: BigbuyPriceModel[] = [];
    private requestState: RequestState;
    private readonly REQUEST_INTERVAL = 30000; // 30 seconds between requests (in ms)
    
    // Optimized page sizes to maximize data per request
    private readonly PRODUCTS_PAGE_SIZE = 50000; // Maximum allowed
    private readonly VARIATIONS_PAGE_SIZE = 50000; // Maximum allowed

    constructor(configPath: string = './config.yml') {
        const config = this.loadConfig(configPath);
        this.baseUrl = `${config.api_url}/rest/catalog`;
        this.accessToken = config.api_token;
        this.testMode = config.test_mode;
        
        // Initialize request state
        this.requestState = this.loadRequestState();
        
        console.log(`BigBuy Price Repository initialized`);
        console.log(`- API URL: ${this.baseUrl}`);
        console.log(`- Test Mode: ${this.testMode}`);
        console.log(`- Request Interval: ${this.REQUEST_INTERVAL / 1000} seconds between requests`);
    }

    private loadConfig(configPath: string): BigBuyConfig {
        try {
            const fileContents = readFileSync(configPath, 'utf8');
            const config = yaml.load(fileContents) as ConfigFile;
            
            const bigbuyConfig = config.conexions.find(
                connection => connection.supplier === 'bigbuy-price'
            );
            
            if (!bigbuyConfig) {
                // Try with stock supplier if price not found (reuse existing config)
                const stockConfig = config.conexions.find(
                    connection => connection.supplier === 'bigbuy-stock'
                );
                
                if (stockConfig) {
                    console.log('BigBuy price configuration not found, using bigbuy-stock configuration');
                    return stockConfig;
                }
                
                throw new Error('BigBuy price configuration not found in YAML file');
            }
            
            // Validate required fields
            if (!bigbuyConfig.api_token || !bigbuyConfig.api_url) {
                throw new Error('Missing required BigBuy configuration fields (api_token, api_url)');
            }
            
            return bigbuyConfig;
        } catch (error) {
            console.error('Error loading BigBuy configuration:', error);
            throw error;
        }
    }

    private loadRequestState(): RequestState {
        try {
            const statePath = './data/bigbuy-price-request-state.json';
            if (existsSync(statePath)) {
                const state = JSON.parse(readFileSync(statePath, 'utf8'));
                return {
                    lastRequest: state.lastRequest || 0,
                    totalRequests: state.totalRequests || 0
                };
            }
        } catch (error) {
            console.warn('Could not load request state, starting fresh:', error);
        }
        
        return {
            lastRequest: 0,
            totalRequests: 0
        };
    }

    private saveRequestState(): void {
        try {
            const statePath = './data/bigbuy-price-request-state.json';
            writeFileSync(statePath, JSON.stringify(this.requestState, null, 2));
        } catch (error) {
            console.warn('Could not save request state:', error);
        }
    }

    private async waitForInterval(): Promise<void> {
        const now = Date.now();
        const timeSinceLastRequest = now - this.requestState.lastRequest;
        
        if (timeSinceLastRequest < this.REQUEST_INTERVAL) {
            const waitTime = this.REQUEST_INTERVAL - timeSinceLastRequest;
            console.log(`⏳ Waiting ${Math.ceil(waitTime / 1000)} seconds before next request...`);
            await this.delay(waitTime);
        }
    }

    private async makeApiRequest(url: string, params: any = {}): Promise<any> {
        // Wait for interval if needed
        await this.waitForInterval();
        
        try {
            console.log(`🔄 Making API request...`);
            const startTime = Date.now();
            
            const response = await got.get(url, {
                headers: {
                    'Authorization': `Bearer ${this.accessToken}`,
                    'Content-Type': 'application/json'
                },
                searchParams: params,
                responseType: 'json',
                timeout: {
                    request: 30000 // 30 second timeout
                }
            });

            // Update request state
            this.requestState.lastRequest = Date.now();
            this.requestState.totalRequests++;
            this.saveRequestState();

            const requestTime = (Date.now() - startTime) / 1000;
            console.log(`✅ Request completed in ${requestTime.toFixed(2)}s (Total requests: ${this.requestState.totalRequests})`);
            
            return response.body;
        } catch (error: any) {
            // Handle 429 (Too Many Requests) specifically
            if (error.response?.statusCode === 429) {
                console.log('⚠️  Received 429 Too Many Requests - waiting 60 seconds and retrying...');
                await this.delay(60000); // Wait 1 minute
                return await this.makeApiRequest(url, params); // Retry
            }
            
            console.error('Request failed:', error.message);
            throw error;
        }
    }

    clear(): void {
        this.prices = [];
    }

    async getAllProductsPrices(includeLargeQuantities: boolean = false): Promise<BigbuyPriceModel[]> {
        this.prices = [];
        let page = 0;
        let hasMoreData = true;
        let totalProcessed = 0;

        console.log(`\n=== Starting Products Prices Fetch ===`);
        console.log(`Target: ~350,000 products`);
        console.log(`Page size: ${this.PRODUCTS_PAGE_SIZE}`);
        console.log(`Include large quantities: ${includeLargeQuantities}`);
        console.log(`Estimated pages needed: ${Math.ceil(350000 / this.PRODUCTS_PAGE_SIZE)}`);
        console.log(`Request interval: ${this.REQUEST_INTERVAL / 1000 / 60} minutes\n`);

        try {
            while (hasMoreData) {
                console.log(`\n--- Fetching products prices page ${page} ---`);
                
                const params: any = {
                    page: page,
                    pageSize: this.PRODUCTS_PAGE_SIZE
                };
                
                if (includeLargeQuantities) {
                    params.includePriceLargeQuantities = true;
                }
                
                const priceData = await this.makeApiRequest(
                    `${this.baseUrl}/productprices.json`,
                    params
                );
                
                if (priceData && Array.isArray(priceData) && priceData.length > 0) {
                    // Process and add price data
                    priceData.forEach((item: any) => {
                        const priceModel: BigbuyPriceModel = {
                            id: item.id,
                            sku: item.sku,
                            wholesalePrice: item.wholesalePrice,
                            inShopsPrice: item.inShopsPrice
                        };
                        
                        if (includeLargeQuantities && item.priceLargeQuantities) {
                            priceModel.priceLargeQuantities = item.priceLargeQuantities.map((pq: any) => ({
                                id: pq.id,
                                quantity: pq.quantity,
                                price: pq.price
                            }));
                        }
                        
                        this.prices.push(priceModel);
                    });
                    
                    totalProcessed += priceData.length;
                    console.log(`✓ Processed ${priceData.length} products from page ${page}`);
                    console.log(`✓ Total products processed: ${totalProcessed}`);
                    console.log(`✓ Progress: ${((totalProcessed / 350000) * 100).toFixed(2)}%`);
                    
                    page++;
                    
                    // Check if we have less data than pageSize, meaning we've reached the end
                    if (priceData.length < this.PRODUCTS_PAGE_SIZE) {
                        hasMoreData = false;
                        console.log(`✓ Reached end of product price data`);
                    }
                } else {
                    hasMoreData = false;
                    console.log(`✓ No more product price data available`);
                }
            }

            console.log(`\n=== Products Price Fetch Complete ===`);
            console.log(`Total products with price data: ${this.prices.length}`);
            return this.prices;

        } catch (error) {
            console.error('Error fetching product price data:', error);
            throw error;
        }
    }

    async getProductVariationPrices(includeLargeQuantities: boolean = false): Promise<BigbuyPriceModel[]> {
        const variationPrices: BigbuyPriceModel[] = [];
        let page = 0;
        let hasMoreData = true;
        let totalProcessed = 0;

        console.log(`\n=== Starting Variations Price Fetch ===`);
        console.log(`Page size: ${this.VARIATIONS_PAGE_SIZE}`);
        console.log(`Include large quantities: ${includeLargeQuantities}`);

        try {
            while (hasMoreData) {
                console.log(`\n--- Fetching variations price page ${page} ---`);
                
                const params: any = {
                    page: page,
                    pageSize: this.VARIATIONS_PAGE_SIZE
                };
                
                if (includeLargeQuantities) {
                    params.includePriceLargeQuantities = true;
                }
                
                const priceData = await this.makeApiRequest(
                    `${this.baseUrl}/productvariationprices.json`,
                    params
                );
                
                if (priceData && Array.isArray(priceData) && priceData.length > 0) {
                    priceData.forEach((item: any) => {
                        const priceModel: BigbuyPriceModel = {
                            id: item.id,
                            sku: item.sku,
                            wholesalePrice: item.wholesalePrice,
                            inShopsPrice: item.inShopsPrice
                        };
                        
                        if (includeLargeQuantities && item.priceLargeQuantities) {
                            priceModel.priceLargeQuantities = item.priceLargeQuantities.map((pq: any) => ({
                                id: pq.id,
                                quantity: pq.quantity,
                                price: pq.price
                            }));
                        }
                        
                        variationPrices.push(priceModel);
                    });
                    
                    totalProcessed += priceData.length;
                    console.log(`✓ Processed ${priceData.length} variations from page ${page}`);
                    console.log(`✓ Total variations processed: ${totalProcessed}`);
                    
                    page++;
                    
                    if (priceData.length < this.VARIATIONS_PAGE_SIZE) {
                        hasMoreData = false;
                        console.log(`✓ Reached end of variation price data`);
                    }
                } else {
                    hasMoreData = false;
                    console.log(`✓ No more variation price data available`);
                }
            }

            console.log(`\n=== Variations Price Fetch Complete ===`);
            console.log(`Total variations with price data: ${variationPrices.length}`);
            return variationPrices;

        } catch (error) {
            console.error('Error fetching variation price data:', error);
            throw error;
        }
    }

    async getProductPriceById(productId: string, includeLargeQuantities: boolean = false): Promise<BigbuyPriceModel | null> {
        try {
            // Note: The BigBuy API doesn't have a direct endpoint for getting a single product price
            // We will need to fetch all prices and filter for the specific product

            // If prices are already cached, search in the cache
            if (this.prices.length > 0) {
                const found = this.prices.find(p => p.id.toString() === productId);
                if (found) return found;
            }

            // Otherwise, fetch all prices and search
            const allPrices = await this.getAllProductsPrices(includeLargeQuantities);
            return allPrices.find(p => p.id.toString() === productId) || null;

        } catch (error) {
            console.error(`Error fetching price for product ${productId}:`, error);
            throw error;
        }
    }

    async getProductVariationPriceById(variationId: string, includeLargeQuantities: boolean = false): Promise<BigbuyPriceModel | null> {
        try {
            // Note: The BigBuy API doesn't have a direct endpoint for getting a single variation price
            // We will need to fetch all variation prices and filter for the specific variation

            // Fetch all variation prices and search
            const variationPrices = await this.getProductVariationPrices(includeLargeQuantities);
            return variationPrices.find(p => p.id.toString() === variationId) || null;

        } catch (error) {
            console.error(`Error fetching price for variation ${variationId}:`, error);
            throw error;
        }
    }

    // Additional method for pagination with start page (useful for resuming)
    async getAllProductsPricesFromPage(startPage: number = 0, includeLargeQuantities: boolean = false): Promise<BigbuyPriceModel[]> {
        const products: BigbuyPriceModel[] = [];
        let page = startPage;
        let hasMoreData = true;
        let totalProcessed = 0;

        console.log(`\n=== Resuming Products Price Fetch from page ${startPage} ===`);

        try {
            while (hasMoreData) {
                console.log(`\n--- Fetching products price page ${page} ---`);
                
                const params: any = {
                    page: page,
                    pageSize: this.PRODUCTS_PAGE_SIZE
                };
                
                if (includeLargeQuantities) {
                    params.includePriceLargeQuantities = true;
                }
                
                const priceData = await this.makeApiRequest(
                    `${this.baseUrl}/productprices.json`,
                    params
                );
                
                if (priceData && Array.isArray(priceData) && priceData.length > 0) {
                    priceData.forEach((item: any) => {
                        const priceModel: BigbuyPriceModel = {
                            id: item.id,
                            sku: item.sku,
                            wholesalePrice: item.wholesalePrice,
                            inShopsPrice: item.inShopsPrice
                        };
                        
                        if (includeLargeQuantities && item.priceLargeQuantities) {
                            priceModel.priceLargeQuantities = item.priceLargeQuantities.map((pq: any) => ({
                                id: pq.id,
                                quantity: pq.quantity,
                                price: pq.price
                            }));
                        }
                        
                        products.push(priceModel);
                    });
                    
                    totalProcessed += priceData.length;
                    console.log(`✓ Processed ${priceData.length} products from page ${page}`);
                    console.log(`✓ Total products processed: ${totalProcessed}`);
                    
                    page++;
                    
                    if (priceData.length < this.PRODUCTS_PAGE_SIZE) {
                        hasMoreData = false;
                        console.log(`✓ Reached end of product price data`);
                    }
                } else {
                    hasMoreData = false;
                    console.log(`✓ No more product price data available`);
                }
            }

            console.log(`\n=== Products Price Fetch Complete ===`);
            console.log(`Total products with price data: ${products.length}`);
            return products;

        } catch (error) {
            console.error('Error fetching product price data:', error);
            throw error;
        }
    }

    // Method to get all prices (products + variations) in one call
    async getAllPrices(includeLargeQuantities: boolean = false): Promise<{ products: BigbuyPriceModel[]; variations: BigbuyPriceModel[] }> {
        console.log('\n=== Starting Complete Price Fetch ===');
        
        // First get products
        const products = await this.getAllProductsPrices(includeLargeQuantities);
        
        // Then get variations
        const variations = await this.getProductVariationPrices(includeLargeQuantities);
        
        console.log('\n=== Complete Price Fetch Finished ===');
        console.log(`Total products: ${products.length}`);
        console.log(`Total variations: ${variations.length}`);
        console.log(`Total items: ${products.length + variations.length}`);
        
        return {
            products,
            variations
        };
    }

    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Additional utility methods
    getConfig(): { baseUrl: string; testMode: boolean } {
        return {
            baseUrl: this.baseUrl,
            testMode: this.testMode
        };
    }

    isTestMode(): boolean {
        return this.testMode;
    }

    getRequestStatus(): RequestState & { timeSinceLastRequest: number } {
        const now = Date.now();
        const timeSinceLastRequest = now - this.requestState.lastRequest;
        
        return {
            ...this.requestState,
            timeSinceLastRequest
        };
    }

    async estimateFullSyncTime(): Promise<{ estimatedMinutes: number; estimatedRequests: number }> {
        const totalProducts = 350000;
        const totalPages = Math.ceil(totalProducts / this.PRODUCTS_PAGE_SIZE);
        const estimatedRequests = totalPages + 5; // Add buffer for variations
        const estimatedMinutes = Math.ceil((estimatedRequests * this.REQUEST_INTERVAL) / 1000 / 60);
        
        return {
            estimatedMinutes,
            estimatedRequests
        };
    }

    // Reset request state (useful for testing)
    resetRequestState(): void {
        this.requestState = {
            lastRequest: 0,
            totalRequests: 0
        };
        this.saveRequestState();
        console.log('Request state reset');
    }

    // Check if we can make a request right now
    canMakeRequestNow(): boolean {
        const now = Date.now();
        const timeSinceLastRequest = now - this.requestState.lastRequest;
        return timeSinceLastRequest >= this.REQUEST_INTERVAL;
    }

    // Get seconds until next request is allowed
    getSecondsUntilNextRequest(): number {
        const now = Date.now();
        const timeSinceLastRequest = now - this.requestState.lastRequest;
        const remainingWait = this.REQUEST_INTERVAL - timeSinceLastRequest;
        return Math.max(0, Math.ceil(remainingWait / 1000));
    }
}