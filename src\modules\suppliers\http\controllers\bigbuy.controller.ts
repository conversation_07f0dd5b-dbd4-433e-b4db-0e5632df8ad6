import { Request, Response } from 'express';
import { BigHubService } from '../../../bighub/services/bighub.service';
import { BigbuyService } from '../../services/bigbuy.service';
import { BigbuyStockService } from '../../services/bigbuy-stock.service';
import { BigbuyPriceService } from '../../services/bigbuy-price.service';
import { Logger } from '@shared/utils/logger.util';
import { parse, format } from 'fast-csv';
import fs from 'fs';
import path from 'path';
import { createReadStream, createWriteStream } from 'fs';
import { delay } from '@shared/utils/misc.util';
import { unlink } from 'node:fs/promises';
import { extractFilesFromFTP } from '@shared/utils/file.util';
import { combineMultipleFilesToSingleFileFromRoot } from '@shared/utils/file.util';

export class BigbuyController {
  bigbuyService: BigbuyService;
  bigHubService: BigHubService;
  bigbuyStockService: BigbuyStockService;
  bigbuyPriceService: BigbuyPriceService;

  constructor(
    bigHubService: BigHubService,
    bigbuyService: BigbuyService,
    bigbuyStockService: BigbuyStockService,
    bigbuyPriceService: BigbuyPriceService
  ) {
    this.bigbuyService = bigbuyService;
    this.bigHubService = bigHubService;
    this.bigbuyStockService = bigbuyStockService;
    this.bigbuyPriceService = bigbuyPriceService;
  }

  /**
   * Get products from BigBuy
   */
  async getProducts(req: Request, res: Response) {
    const response = {
      status: 200,
      message: 'Products found'
    };

    try {
      const products = await this.bigbuyService.getProducts();
      res.status(response.status).json(products);
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message);
    }
  }

  /**
   * Download BigHub CSV file
   */
  async getProductsBigHubCsv(req: Request, res: Response) {
    try {
      const filename = 'bighub-bigbuy.csv';
      res.setHeader('Content-disposition', 'attachment; filename=' + filename);
      res.setHeader('Content-type', 'text/csv');
      fs.createReadStream(filename).pipe(res);
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message);
    }
  }

  /**
   * Export BigHub CSV with latest stock and price data
   */
  async getProductsBigHubExportCsv(req: Request, res: Response) {
    const logger = new Logger('BigbuyController');

    try {
      // FTP connection settings
      const ftpOptions = {
        host: 'www.dropshippers.com.es',
        port: 21,
        user: 'bbehA3I6Z0qE',
        password: '5CVTVWt7ji',
        passive: true,
      };

      // Files to download
      const ftpFilePath = '/files/products/csv/standard/';
      const filenames = [
        'product_2202_pt.csv',
        'product_2399_pt.csv',
        'product_2403_pt.csv',
        'product_2491_pt.csv',
        'product_2501_pt.csv',
        'product_2507_pt.csv',
        'product_2570_pt.csv',
        'product_2571_pt.csv',
        'product_2609_pt.csv',
        'product_2662_pt.csv',
        'product_2672_pt.csv',
        'product_2678_pt.csv',
        'product_3046_pt.csv'
      ];
      const destinationPath = './data/downloads/bigbuy';

      logger.info('Starting BigBuy file download process', {
        fileCount: filenames.length,
        destination: destinationPath
      });

      // 1. Download files from FTP
      const downloadedFiles = await extractFilesFromFTP(ftpOptions, ftpFilePath, filenames, destinationPath);
      logger.info('Files downloaded successfully', { count: downloadedFiles.length });

      // 2. Combine downloaded files into one
      const outputFilePath = path.join(destinationPath, 'bigbuy_combined.csv');
      await combineMultipleFilesToSingleFileFromRoot(downloadedFiles, outputFilePath);
      logger.info('Files combined successfully', { outputPath: outputFilePath });

      // 3. Update stock data by fetching from BigBuy API
      logger.info('Fetching latest stock and price data from BigBuy...');
      try {
        
        // 3.1. Fetch stock data
        logger.info('Starting to fetch stock data...');
        await this.bigbuyStockService.getBigbuyStocks();
        // Ensure stock data is in simplified format
        await this.updateStockJsonToSimplified();
        logger.info('Stock data updated successfully and saved to bigbuy-stock.json');

        // 3.2. Fetch price data
        logger.info('Starting to fetch price data...');
        const includeLargeQuantities = true; // Set to true to include volume discounts
        const priceData = await this.bigbuyPriceService.getBigbuyPrices(includeLargeQuantities);
        logger.info('Price data updated successfully and saved to bigbuy-price.json');
        /*
        // 3.3. Generate price summary for reporting
        try {
          // Create summary without making another API call
          const priceSummary = {
            totalProducts: priceData.filter(p => !p.sku.includes('-')).length,
            totalVariations: priceData.filter(p => p.sku.includes('-')).length,
            averageWholesalePrice: parseFloat((priceData.reduce((sum, p) => sum + p.wholesalePrice, 0) / priceData.length).toFixed(2)),
            minWholesalePrice: Math.min(...priceData.map(p => p.wholesalePrice)),
            maxWholesalePrice: Math.max(...priceData.map(p => p.wholesalePrice)),
            productsWithVolumeDiscounts: priceData.filter(p => p.priceLargeQuantities && p.priceLargeQuantities.length > 0).length
          };
          
          logger.info('Price summary generated', {
            totalProducts: priceSummary.totalProducts,
            totalVariations: priceSummary.totalVariations,
            averageWholesalePrice: priceSummary.averageWholesalePrice,
            productsWithVolumeDiscounts: priceSummary.productsWithVolumeDiscounts
          });
        } catch (summaryError) {
          logger.warn('Could not generate price summary, but price data was fetched successfully:', summaryError);
        }
        */
        // 3.4. Simplify price data to match stock format
        await this.updatePriceJsonToSimplified();
        logger.info('Price data simplified for easier CSV integration');

      } catch (dataFetchError) {
        logger.error('Error updating stock and/or price data:', dataFetchError);
      }

      // 4. Update CSV with the latest stock data
      await this.combineStocksInProducts(outputFilePath, logger);

      // 5. Update CSV with the latest price data
      await this.combinePricesInProducts(outputFilePath, logger);

      // 6. Process the updated CSV for BigHub
      const files = await this.bigbuyService.getProductsBighub();
      let count = 0;

      for await (let filename of files) {
        count++;
        const products = await this.bigbuyService.readCsvFile(filename);
        const dest = `${count}-bighub-bigbuy.csv`;
        await this.bigHubService.supplierCreateCsv(dest, products, 1451);
        console.log(`${dest} uploaded successfully`);
      }

      // 7. Clean up temporary files
      for await (let filename of files) {
        await unlink(filename);
      }

      // 8. Send success response
      res.status(200).json({
        status: 'success',
        message: 'Files downloaded, combined, and processed successfully',
        downloadedFiles: downloadedFiles.length,
        combinedFilePath: outputFilePath,
        dataUpdated: {
          stock: true,
          price: true
        }
      });

    } catch (error: any) {
      logger.error('Error in BigBuy export process', error);
      console.error(error);
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Simplify stock data from complex to {sku, stock} format
   */
  async updateStockJsonToSimplified(): Promise<void> {
    const logger = new Logger('BigbuyController');
    logger.info('⏳ Starting stock file simplification');

    try {
      // Path to the stock file
      const stockPath = path.join(process.cwd(), 'bigbuy-stock.json');

      if (!fs.existsSync(stockPath)) {
        logger.error('❌ Stock file not found');
        return;
      }

      // Read the existing file
      const startTime = Date.now();
      const rawData = fs.readFileSync(stockPath, 'utf8');
      let stockData;

      try {
        stockData = JSON.parse(rawData);
        logger.info(`📊 Parsed stock file with ${stockData.length} items in ${Date.now() - startTime}ms`);
      } catch (parseError) {
        if (parseError instanceof Error) {
          logger.error(`❌ Failed to parse stock data: ${parseError.message}`);
        } else {
          logger.error(`❌ Failed to parse stock data: ${String(parseError)}`);
        }
        return;
      }

      // Check if it's already simplified
      if (stockData.length > 0) {
        // Log a sample of the data for debugging
        logger.info(`📋 Sample data format: ${JSON.stringify(stockData[0])}`);

        if ('stock' in stockData[0] &&
          'sku' in stockData[0] &&
          Object.keys(stockData[0]).length === 2) {
          logger.info('✅ Stock data is already in simplified format');
          return;
        }
      }

      // Transform to simplified format
      logger.info('🔄 Creating simplified stock data...');
      const transformStartTime = Date.now();
      const simplifiedStocks = stockData.map((item: any) => {
        // Check format and extract total stock
        let totalStock = 0;

        if (item.stocks && Array.isArray(item.stocks)) {
          // Original format with stocks array
          totalStock = item.stocks.reduce((sum: number, stock: any) => sum + (stock.quantity || 0), 0);
        } else if ('stock' in item) {
          // Already simplified
          totalStock = item.stock;
        }

        return {
          sku: item.sku,
          stock: totalStock
        };
      });

      logger.info(`✅ Transformed ${simplifiedStocks.length} items in ${Date.now() - transformStartTime}ms`);

      // Save sample data
      if (simplifiedStocks.length > 0) {
        logger.info(`📋 Sample simplified data: ${JSON.stringify(simplifiedStocks[0])}`);
      }

      // Save back to the same file
      logger.info(`💾 Saving simplified stock data back to: ${stockPath}`);
      const saveStartTime = Date.now();
      fs.writeFileSync(stockPath, JSON.stringify(simplifiedStocks, null, 2), 'utf8');

      const fileStats = fs.statSync(stockPath);
      logger.info(`✅ Simplified stock data saved in ${Date.now() - saveStartTime}ms:`);
      logger.info(`   - Size: ${Math.round(fileStats.size / 1024)} KB`);
      logger.info(`   - Items: ${simplifiedStocks.length}`);
      logger.info(`   - Format: { sku, stock }`);
    } catch (error: any) {
      logger.error(`❌ Error simplifying stock data: ${error.message || error}`);
    }
  }

  /**
   * Simplify price data from complex to {sku, wholesalePrice} format
   */
  async updatePriceJsonToSimplified(): Promise<void> {
    const logger = new Logger('BigbuyController');
    logger.info('⏳ Starting price file simplification');

    try {
      // Path to the price file
      const pricePath = path.join(process.cwd(), 'bigbuy-price.json');

      if (!fs.existsSync(pricePath)) {
        logger.error('❌ Price file not found');
        return;
      }

      // Read the existing file
      const startTime = Date.now();
      const rawData = fs.readFileSync(pricePath, 'utf8');
      let priceData;

      try {
        priceData = JSON.parse(rawData);
        logger.info(`📊 Parsed price file with ${priceData.length} items in ${Date.now() - startTime}ms`);
      } catch (parseError) {
        if (parseError instanceof Error) {
          logger.error(`❌ Failed to parse price data: ${parseError.message}`);
        } else {
          logger.error(`❌ Failed to parse price data: ${String(parseError)}`);
        }
        return;
      }

      // Check if it's already simplified
      if (priceData.length > 0) {
        // Log a sample of the data for debugging
        logger.info(`📋 Sample price data format: ${JSON.stringify(priceData[0])}`);

        if ('wholesalePrice' in priceData[0] &&
          'sku' in priceData[0] &&
          Object.keys(priceData[0]).length <= 3) {
          logger.info('✅ Price data is already in simplified format');
          return;
        }
      }

      // Transform to simplified format
      logger.info('🔄 Creating simplified price data...');
      const transformStartTime = Date.now();
      const simplifiedPrices = priceData.map((item: any) => {
        return {
          sku: item.sku,
          wholesalePrice: item.wholesalePrice
        };
      });

      logger.info(`✅ Transformed ${simplifiedPrices.length} price items in ${Date.now() - transformStartTime}ms`);

      // Save sample data
      if (simplifiedPrices.length > 0) {
        logger.info(`📋 Sample simplified price data: ${JSON.stringify(simplifiedPrices[0])}`);
      }

      // Save back to the same file
      logger.info(`💾 Saving simplified price data back to: ${pricePath}`);
      const saveStartTime = Date.now();
      fs.writeFileSync(pricePath, JSON.stringify(simplifiedPrices, null, 2), 'utf8');

      const fileStats = fs.statSync(pricePath);
      logger.info(`✅ Simplified price data saved in ${Date.now() - saveStartTime}ms:`);
      logger.info(`   - Size: ${Math.round(fileStats.size / 1024)} KB`);
      logger.info(`   - Items: ${simplifiedPrices.length}`);
      logger.info(`   - Format: { sku, wholesalePrice }`);
    } catch (error: any) {
      logger.error(`❌ Error simplifying price data: ${error.message || error}`);
    }
  }

  /**
   * Combine stock data with CSV product data
   */
  async combineStocksInProducts(csvFilePath: string, logger: Logger): Promise<void> {
    try {
      logger.info('Starting stock combination process');

      // 1. First, ensure stock file is in simplified format
      await this.updateStockJsonToSimplified();

      // 2. Load simplified stock data from JSON file
      const stockJsonPath = path.join(process.cwd(), 'bigbuy-stock.json');
      let stockMap = new Map();

      // 3. Check if stock file exists and load it
      if (fs.existsSync(stockJsonPath)) {
        logger.info(`Found stock file at: ${stockJsonPath}`);

        try {
          const stockData = JSON.parse(fs.readFileSync(stockJsonPath, 'utf8'));
          logger.info(`Loaded ${stockData.length} stock records from JSON`);

          // Create map from simplified data: { sku: "X", stock: Y }
          stockData.forEach((item: any) => {
            if (item.sku && item.stock !== undefined) {
              stockMap.set(item.sku, item.stock);
            }
          });

          logger.info(`Created stock map with ${stockMap.size} entries`);
        } catch (parseError) {
          logger.error(`Error parsing stock data: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
          return;
        }
      } else {
        logger.warn('Stock JSON file not found. Attempting to fetch stock data now...');

        // 4. If no stock file exists, try to fetch it
        if (this.bigbuyStockService) {
          try {
            logger.info('Fetching stock data from BigBuy API...');
            await this.bigbuyStockService.getBigbuyStocks();
            await this.updateStockJsonToSimplified();

            // Check again for the file
            if (fs.existsSync(stockJsonPath)) {
              const stockData = JSON.parse(fs.readFileSync(stockJsonPath, 'utf8'));

              // Create map from data
              stockData.forEach((item: any) => {
                if (item.sku && item.stock !== undefined) {
                  stockMap.set(item.sku, item.stock);
                } else if (item.sku && item.stocks && Array.isArray(item.stocks)) {
                  // Handle non-simplified format as fallback
                  const totalStock = item.stocks.reduce((sum: number, stock: any) =>
                    sum + (stock.quantity || 0), 0);
                  stockMap.set(item.sku, totalStock);
                }
              });

              logger.info(`Used freshly fetched stock data with ${stockData.length} items`);
            } else {
              logger.error('Failed to create stock file even after fetching');
              return;
            }
          } catch (fetchError) {
            logger.error('Error fetching stock data:', fetchError);
            return;
          }
        } else {
          logger.error('Stock service not available and stock file not found');
          return;
        }
      }

      // 5. Verify the CSV file exists
      if (!fs.existsSync(csvFilePath)) {
        logger.error(`❌ CSV file not found at: ${csvFilePath}`);
        return;
      }

      // Get original file stats for comparison
      const originalFileStats = fs.statSync(csvFilePath);
      logger.info(`📂 Original CSV file: ${csvFilePath}`);
      logger.info(`   - Size: ${Math.round(originalFileStats.size / 1024)} KB`);

      // 6. Process the CSV file
      const tempFilePath = csvFilePath + '.temp';
      const updatedRows: any[] = [];
      let processedCount = 0;
      let updatedCount = 0;

      // 7. Read and process the CSV
      logger.info(`🔄 Starting CSV processing...`);
      const csvStartTime = Date.now();

      await new Promise<void>((resolve, reject) => {
        try {
          const readStream = createReadStream(csvFilePath);

          // First, detect the CSV structure and header format
          let firstLine = '';
          let headerSample = [];

          logger.info(`Examining CSV structure...`);

          try {
            const fileContent = fs.readFileSync(csvFilePath, 'utf8');
            const lines = fileContent.split('\n');

            if (lines.length > 0) {
              firstLine = lines[0];
              logger.info(`CSV header line: ${firstLine}`);

              // Try to parse header columns
              const headerColumns = firstLine.split(';');
              logger.info(`Detected ${headerColumns.length} columns in header`);

              // Log some header columns for debugging
              if (headerColumns.length > 0) {
                const sampleSize = Math.min(10, headerColumns.length);
                headerSample = headerColumns.slice(0, sampleSize);
                logger.info(`Header sample: ${headerSample.join(', ')}`);
              }

              // Check if first column might be ID/SKU
              if (headerColumns.length > 0) {
                logger.info(`First column header: "${headerColumns[0]}"`);
              }
            } else {
              logger.error(`CSV file appears to be empty!`);
              return;
            }

            // Check for stock column
            const stockColumnIndex = firstLine.toLowerCase().split(';').findIndex(col =>
              col.includes('stock') || col.includes('inventory'));

            if (stockColumnIndex >= 0) {
              logger.info(`Found stock column at index ${stockColumnIndex}`);
            } else {
              logger.warn(`Could not find stock column in header!`);
            }
          } catch (examError) {
            logger.error(`Error examining CSV: ${examError}`);
          }

          // Now we have the header line - log it for troubleshooting
          logger.info(`📋 CSV Header: ${firstLine}`);

          // Count columns from the header line
          const headerColumns = firstLine.split(';');
          const columnCount = headerColumns.length;
          logger.info(`📊 Detected ${columnCount} columns in CSV header`);

          // Find critical column indices
          const idColumnIndex = headerColumns.findIndex(col =>
            col.toUpperCase() === 'ID' || col.toUpperCase() === 'SKU');

          const stockColumnIndex = headerColumns.findIndex(col =>
            col.toUpperCase() === 'STOCK' || col.toUpperCase().includes('INVENTORY'));

          logger.info(`ID column index: ${idColumnIndex}, Stock column index: ${stockColumnIndex}`);

          if (idColumnIndex === -1) {
            logger.warn(`Could not find ID/SKU column in header! Will use first column.`);
          }

          if (stockColumnIndex === -1) {
            logger.warn(`Could not find STOCK column in header! Will create one if needed.`);
          }

          const idColumnName = idColumnIndex >= 0 ? headerColumns[idColumnIndex] : headerColumns[0];
          const stockColumnName = stockColumnIndex >= 0 ? headerColumns[stockColumnIndex] : 'STOCK';

          logger.info(`Using ID column: "${idColumnName}", Stock column: "${stockColumnName}"`);

          // Process the full file with column name knowledge
          try {
            logger.info(`🔄 Starting full CSV processing...`);
            const readStream = createReadStream(csvFilePath);
            const parser = parse({
              headers: true,
              delimiter: ';',
              strictColumnHandling: false, // Don't throw error on column count mismatch
              ignoreEmpty: true,
              discardUnmappedColumns: true // Ignore extra columns
            });

            parser.on('data', (row: any) => {
              processedCount++;

              // Get the ID from the first column (assuming it matches SKU in JSON)
              const productId = row.ID || Object.values(row)[0];

              /*
              // Log progress more frequently - every 50000 rows processed
              if (processedCount % 50000 === 0) {
                logger.info(`Progress: Processed ${processedCount} rows so far...`);
              }
              */
              if (productId && stockMap.has(productId)) {
                // Log details about the stock update
                const oldStock = row.STOCK || 'N/A';
                const newStock = stockMap.get(productId);

                // Update the STOCK column with new value
                row.STOCK = newStock;
                updatedCount++;

                // Log more detailed information about updates - every 50000 updates
                if (updatedCount % 50000 === 0) {
                  logger.info(`✓ Updated ${updatedCount} stocks so far (${((updatedCount / processedCount) * 100).toFixed(2)}% hit rate)`);
                }

                /*
                // Log sample updates occasionally for verification
                if (updatedCount % 10000 === 0 || (updatedCount <= 10 && updatedCount > 0)) {
                  logger.info(`Sample update: SKU ${productId}, Stock changed from ${oldStock} to ${newStock}`);
                }
                */
              }

              updatedRows.push(row);

              // Log progress percentage
              if (processedCount % 50000 === 0) {
                logger.info(`📊 Progress: ${processedCount} rows (${updatedCount} stocks updated) - ${Math.min(100, Number(((processedCount / 350000) * 100).toFixed(1)))}% complete`);
              }
            });

            parser.on('end', () => {
              const processingTime = (Date.now() - csvStartTime) / 1000;
              logger.info(`✅ Finished reading CSV in ${processingTime.toFixed(1)}s`);
              logger.info(`📊 Final processing stats:`);
              logger.info(`   - Rows processed: ${processedCount}`);
              logger.info(`   - Stocks updated: ${updatedCount} (${((updatedCount / processedCount) * 100).toFixed(2)}% of rows)`);
              logger.info(`   - Processing speed: ${Math.round(processedCount / processingTime)} rows/second`);
              resolve();
            });

            parser.on('error', (error) => {
              logger.error(`Error parsing CSV: ${error.message || error}`);
              reject(error);
            });

            readStream.pipe(parser);
          } catch (parseError) {
            logger.error(`Error in CSV parsing: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
            reject(parseError);
          }

        } catch (error) {
          logger.error(`Error setting up CSV parsing: ${error instanceof Error ? error.message : String(error)}`);
          reject(error);
        }
      });

      // 8. Write the updated data back to a temporary file
      await new Promise<void>((resolve, reject) => {
        try {
          logger.info(`Starting to write updated data to temporary file: ${tempFilePath}`);
          const writeStream = createWriteStream(tempFilePath);

          // Use the same format options that worked for reading
          const formatter = format({
            headers: true,
            delimiter: ';',
            quoteColumns: true,
            quoteHeaders: true
          });

          formatter.pipe(writeStream);

          // Monitor write progress
          let writtenCount = 0;
          const totalToWrite = updatedRows.length;

          // Write all rows
          updatedRows.forEach((row, index) => {
            formatter.write(row);
            writtenCount++;

            // Log write progress
            if (writtenCount % 25000 === 0 || writtenCount === totalToWrite) {
              const percent = ((writtenCount / totalToWrite) * 100).toFixed(1);
              logger.info(`✍️ Writing progress: ${writtenCount}/${totalToWrite} rows (${percent}%)`);
            }
          });

          formatter.end();

          writeStream.on('finish', () => {
            logger.info(`✅ Successfully wrote all ${writtenCount} rows to temporary file`);
            resolve();
          });

          writeStream.on('error', (error) => {
            logger.error(`Error writing CSV: ${error.message || error}`);
            reject(error);
          });
        } catch (error) {
          logger.error(`Error setting up CSV writing: ${error instanceof Error ? error.message : String(error)}`);
          reject(error);
        }
      });

      // 9. Replace the original file with the updated one using streams
      logger.info(`Using stream-based file replacement strategy...`);

      try {
        // Define filenames
        const finalBackupPath = csvFilePath + '.backup';

        // First, try to create a backup of the original file
        if (fs.existsSync(csvFilePath)) {
          try {
            logger.info(`Creating backup of original file...`);
            fs.copyFileSync(csvFilePath, finalBackupPath);
            logger.info(`Backup created at: ${finalBackupPath}`);
          } catch (backupError: any) {
            logger.warn(`Could not create backup: ${backupError.message}`);
          }
        }

        // Now use a completely different stream-based approach
        logger.info(`Starting stream-based file replacement...`);

        await new Promise<void>((resolve, reject) => {
          try {
            // First check if temp file exists
            if (!fs.existsSync(tempFilePath)) {
              return reject(new Error(`Temporary file doesn't exist: ${tempFilePath}`));
            }

            // Create read stream from temp file
            const readStream = fs.createReadStream(tempFilePath);

            // Create write stream to target file
            const writeStream = fs.createWriteStream(csvFilePath, {
              flags: 'w',  // 'w' flag will overwrite the existing file
              autoClose: true
            });

            // Track progress
            let bytesRead = 0;
            const tempFileSize = fs.statSync(tempFilePath).size;

            readStream.on('data', (chunk) => {
              bytesRead += chunk.length;

              // Log progress periodically
              if (bytesRead % (10 * 1024 * 1024) === 0) { // Every 10MB
                const percentComplete = ((bytesRead / tempFileSize) * 100).toFixed(2);
                logger.info(`Stream progress: ${Math.round(bytesRead / (1024 * 1024))}MB / ${Math.round(tempFileSize / (1024 * 1024))}MB (${percentComplete}%)`);
              }
            });

            // Handle completion
            writeStream.on('finish', () => {
              logger.info(`✅ Stream completed. Verifying file sizes...`);

              // Verify file sizes match
              const originalSize = fs.statSync(tempFilePath).size;
              const newSize = fs.statSync(csvFilePath).size;

              if (originalSize === newSize) {
                logger.info(`✅ File sizes match (${originalSize} bytes). Replacement successful.`);

                // Try to clean up temporary file
                try {
                  fs.unlinkSync(tempFilePath);
                  logger.info(`Temporary file deleted`);
                } catch (unlinkError) {
                  logger.warn(`Could not delete temp file, but replacement was successful`);
                }

                resolve();
              } else {
                reject(new Error(`File size mismatch after streaming: ${originalSize} vs ${newSize}`));
              }
            });

            // Handle errors
            readStream.on('error', (error) => {
              logger.error(`Error reading from temp file: ${error.message}`);
              reject(error);
            });

            writeStream.on('error', (error) => {
              logger.error(`Error writing to destination file: ${error.message}`);
              reject(error);
            });

            // Start the streaming process
            logger.info(`Piping temporary file to destination...`);
            readStream.pipe(writeStream);

          } catch (streamError) {
            reject(streamError);
          }
        });

        // Get file sizes for comparison
        const finalFileStats = fs.statSync(csvFilePath);
        const finalFileSizeKB = Math.round(finalFileStats.size / 1024);

        logger.info(`🎉 Stock combination completed successfully with stream approach!`);
        logger.info(`📈 Final statistics:`);
        logger.info(`- Total rows processed: ${processedCount}`);
        logger.info(`- Stocks updated: ${updatedCount} (${((updatedCount / processedCount) * 100).toFixed(2)}% of rows)`);
        logger.info(`- Updated file: ${csvFilePath}`);
        logger.info(`- File size: ${finalFileSizeKB} KB`);

        // Optionally clean up backup if everything went well
        if (fs.existsSync(finalBackupPath)) {
          try {
            fs.unlinkSync(finalBackupPath);
            logger.info(`Backup file deleted as replacement was successful`);
          } catch (cleanupError) {
            logger.warn(`Note: Could not delete backup file, but operation was successful`);
          }
        }

      } catch (streamError: any) {
        logger.error(`❌ Stream-based replacement failed: ${streamError.message}`);

        // Check if our backup file exists and try to restore from it
        const backupPath = csvFilePath + '.backup';

        if (fs.existsSync(backupPath)) {
          try {
            logger.info(`Attempting to restore from backup...`);
            fs.copyFileSync(backupPath, csvFilePath);
            logger.info(`✅ Restored original file from backup`);
          } catch (restoreError: any) {
            logger.error(`Failed to restore from backup: ${restoreError.message}`);
          }
        }

        // Try the temporary file as a last resort
        if (fs.existsSync(tempFilePath)) {
          const recoverPath = path.join(path.dirname(csvFilePath), 'recovered_' + path.basename(csvFilePath));

          try {
            fs.copyFileSync(tempFilePath, recoverPath);
            logger.info(`✅ Created recovery file at: ${recoverPath}`);
          } catch (recoverError) {
            if (recoverError instanceof Error) {
              logger.error(`Failed to create recovery file: ${recoverError.message}`);
            } else {
              logger.error(`Failed to create recovery file: ${String(recoverError)}`);
            }
          }
        }

        // Since this is a critical step, we should throw the error
        throw new Error(`File replacement failed: ${streamError.message}`);
      }

    } catch (error: any) {
      logger.error(`❌ Error in combineStocksInProducts: ${error.message || error}`);
      throw error;
    }
  }

  /**
   * Combine price data with CSV product data
   */
  async combinePricesInProducts(csvFilePath: string, logger: Logger): Promise<void> {
    try {
      logger.info('Starting price combination process');

      // 1. First, ensure price file is in simplified format
      await this.updatePriceJsonToSimplified();

      // 2. Load simplified price data from JSON file
      const priceJsonPath = path.join(process.cwd(), 'bigbuy-price.json');
      let priceMap = new Map();

      // 3. Check if price file exists and load it
      if (fs.existsSync(priceJsonPath)) {
        logger.info(`Found price file at: ${priceJsonPath}`);

        try {
          const priceData = JSON.parse(fs.readFileSync(priceJsonPath, 'utf8'));
          logger.info(`Loaded ${priceData.length} price records from JSON`);

          // Create map from simplified data: { sku: "X", wholesalePrice: Y}
          priceData.forEach((item: any) => {
            if (item.sku && item.wholesalePrice !== undefined) {
              priceMap.set(item.sku, {
                wholesalePrice: item.wholesalePrice,
              });
            }
          });

          logger.info(`Created price map with ${priceMap.size} entries`);
        } catch (parseError) {
          logger.error(`Error parsing price data: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
          return;
        }
      } else {
        logger.warn('Price JSON file not found. Attempting to fetch price data now...');

        // 4. If no price file exists, try to fetch it
        if (this.bigbuyPriceService) {
          try {
            logger.info('Fetching price data from BigBuy API...');
            await this.bigbuyPriceService.getBigbuyPrices(true);
            await this.updatePriceJsonToSimplified();

            // Check again for the file
            if (fs.existsSync(priceJsonPath)) {
              const priceData = JSON.parse(fs.readFileSync(priceJsonPath, 'utf8'));

              // Create map from data
              priceData.forEach((item: any) => {
                if (item.sku && item.wholesalePrice !== undefined) {
                  priceMap.set(item.sku, {
                    wholesalePrice: item.wholesalePrice
                  });
                }
              });

              logger.info(`Used freshly fetched price data with ${priceData.length} items`);
            } else {
              logger.error('Failed to create price file even after fetching');
              return;
            }
          } catch (fetchError) {
            logger.error('Error fetching price data:', fetchError);
            return;
          }
        } else {
          logger.error('Price service not available and price file not found');
          return;
        }
      }

      // 5. Verify the CSV file exists
      if (!fs.existsSync(csvFilePath)) {
        logger.error(`❌ CSV file not found at: ${csvFilePath}`);
        return;
      }

      // Get original file stats for comparison
      const originalFileStats = fs.statSync(csvFilePath);
      logger.info(`📂 Original CSV file: ${csvFilePath}`);
      logger.info(`   - Size: ${Math.round(originalFileStats.size / 1024)} KB`);

      // 6. Process the CSV file
      const tempFilePath = csvFilePath + '.price_temp';
      const updatedRows: any[] = [];
      let processedCount = 0;
      let updatedCount = 0;

      // 7. Read and process the CSV
      logger.info(`🔄 Starting CSV processing for price updates...`);
      const csvStartTime = Date.now();

      await new Promise<void>((resolve, reject) => {
        try {
          // First, detect the CSV structure and header format - specifically looking for price columns
          let firstLine = '';
          
          try {
            const fileContent = fs.readFileSync(csvFilePath, 'utf8');
            const lines = fileContent.split('\n');

            if (lines.length > 0) {
              firstLine = lines[0];
              logger.info(`CSV header line: ${firstLine}`);

              // Try to parse header columns
              const headerColumns = firstLine.split(';');
              logger.info(`Detected ${headerColumns.length} columns in header`);

              // Check for price-related columns
              const wholesalePriceIndex = headerColumns.findIndex(col => 
                col.toUpperCase().includes('PRICE'));
                

              if (wholesalePriceIndex >= 0) {
                logger.info(`Found wholesale price column at index ${wholesalePriceIndex}: "${headerColumns[wholesalePriceIndex]}"`);
              } else {
                logger.warn(`Could not find wholesale price column in header!`);
              }
            }
          } catch (examError) {
            logger.error(`Error examining CSV: ${examError}`);
          }

          // Now process the CSV with found header information
          try {
            logger.info(`🔄 Starting full CSV processing for price updates...`);
            const readStream = createReadStream(csvFilePath);
            const parser = parse({
              headers: true,
              delimiter: ';',
              strictColumnHandling: false,
              ignoreEmpty: true,
              discardUnmappedColumns: true
            });

            parser.on('data', (row: any) => {
              processedCount++;

              // Get the ID/SKU from the row (try multiple common column names)
              const productId = row.ID || row.SKU || row.EAN || Object.values(row)[0];

              if (productId && priceMap.has(productId)) {
                const priceData = priceMap.get(productId);
                let updated = false;

                // Only update the PRICE column with wholesale price
                if ('PRICE' in row) {
                  row.PRICE = priceData.wholesalePrice; // Use wholesale price for PRICE
                  updated = true;
                  updatedCount++;
                }

                // Don't update any other price columns
                // Don't create new columns if they don't exist

                // Log progress periodically
                if (updatedCount % 50000 === 0) {
                  logger.info(`✓ Updated ${updatedCount} prices so far (${((updatedCount / processedCount) * 100).toFixed(2)}% hit rate)`);
                }
              }

              updatedRows.push(row);

              // Log progress percentage
              if (processedCount % 50000 === 0) {
                logger.info(`📊 Progress: ${processedCount} rows (${updatedCount} prices updated) - ${Math.min(100, Number(((processedCount / 350000) * 100).toFixed(1)))}% complete`);
              }
            });

            parser.on('end', () => {
              const processingTime = (Date.now() - csvStartTime) / 1000;
              logger.info(`✅ Finished reading CSV for price updates in ${processingTime.toFixed(1)}s`);
              logger.info(`📊 Final processing stats:`);
              logger.info(`   - Rows processed: ${processedCount}`);
              logger.info(`   - Prices updated: ${updatedCount} (${((updatedCount / processedCount) * 100).toFixed(2)}% of rows)`);
              logger.info(`   - Processing speed: ${Math.round(processedCount / processingTime)} rows/second`);
              resolve();
            });

            parser.on('error', (error) => {
              logger.error(`Error parsing CSV for price updates: ${error.message || error}`);
              reject(error);
            });

            readStream.pipe(parser);
          } catch (parseError) {
            logger.error(`Error in CSV parsing for price updates: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
            reject(parseError);
          }

        } catch (error) {
          logger.error(`Error setting up CSV parsing for price updates: ${error instanceof Error ? error.message : String(error)}`);
          reject(error);
        }
      });

      // 8. Write the updated data back to a temporary file
      await new Promise<void>((resolve, reject) => {
        try {
          logger.info(`Starting to write price-updated data to temporary file: ${tempFilePath}`);
          const writeStream = createWriteStream(tempFilePath);

          // Use the same format options that worked for reading
          const formatter = format({
            headers: true,
            delimiter: ';',
            quoteColumns: true,
            quoteHeaders: true
          });

          formatter.pipe(writeStream);

          // Write all rows
          let writtenCount = 0;
          const totalToWrite = updatedRows.length;

          updatedRows.forEach((row) => {
            formatter.write(row);
            writtenCount++;

            // Log write progress
            if (writtenCount % 25000 === 0 || writtenCount === totalToWrite) {
              const percent = ((writtenCount / totalToWrite) * 100).toFixed(1);
              logger.info(`✍️ Writing progress: ${writtenCount}/${totalToWrite} rows (${percent}%)`);
            }
          });

          formatter.end();

          writeStream.on('finish', () => {
            logger.info(`✅ Successfully wrote all ${writtenCount} rows with price updates to temporary file`);
            resolve();
          });

          writeStream.on('error', (error) => {
            logger.error(`Error writing CSV with price updates: ${error.message || error}`);
            reject(error);
          });
        } catch (error) {
          logger.error(`Error setting up CSV writing for price updates: ${error instanceof Error ? error.message : String(error)}`);
          reject(error);
        }
      });

      // 9. Replace the original file with the updated one using streams
      logger.info(`Using stream-based file replacement for price updates...`);

      try {
        // Define filenames
        const finalBackupPath = csvFilePath + '.price_backup';

        // First, try to create a backup of the original file
        if (fs.existsSync(csvFilePath)) {
          try {
            logger.info(`Creating backup of original file...`);
            fs.copyFileSync(csvFilePath, finalBackupPath);
            logger.info(`Backup created at: ${finalBackupPath}`);
          } catch (backupError: any) {
            logger.warn(`Could not create backup: ${backupError.message}`);
          }
        }

        // Replace the file using streams
        await new Promise<void>((resolve, reject) => {
          try {
            // Check if temp file exists
            if (!fs.existsSync(tempFilePath)) {
              return reject(new Error(`Temporary price file doesn't exist: ${tempFilePath}`));
            }

            // Create read stream from temp file
            const readStream = fs.createReadStream(tempFilePath);

            // Create write stream to target file
            const writeStream = fs.createWriteStream(csvFilePath, {
              flags: 'w',  // 'w' flag will overwrite the existing file
              autoClose: true
            });

            // Track progress
            let bytesRead = 0;
            const tempFileSize = fs.statSync(tempFilePath).size;

            readStream.on('data', (chunk) => {
              bytesRead += chunk.length;

              // Log progress periodically
              if (bytesRead % (10 * 1024 * 1024) === 0) { // Every 10MB
                const percentComplete = ((bytesRead / tempFileSize) * 100).toFixed(2);
                logger.info(`Stream progress: ${Math.round(bytesRead / (1024 * 1024))}MB / ${Math.round(tempFileSize / (1024 * 1024))}MB (${percentComplete}%)`);
              }
            });

            // Handle completion
            writeStream.on('finish', () => {
              logger.info(`✅ Stream completed. Verifying file sizes...`);

              // Verify file sizes match
              const originalSize = fs.statSync(tempFilePath).size;
              const newSize = fs.statSync(csvFilePath).size;

              if (originalSize === newSize) {
                logger.info(`✅ File sizes match (${originalSize} bytes). Replacement successful.`);

                // Try to clean up temporary file
                try {
                  fs.unlinkSync(tempFilePath);
                  logger.info(`Temporary price file deleted`);
                } catch (unlinkError) {
                  logger.warn(`Could not delete temp price file, but replacement was successful`);
                }

                resolve();
              } else {
                reject(new Error(`File size mismatch after streaming price update: ${originalSize} vs ${newSize}`));
              }
            });

            // Handle errors
            readStream.on('error', (error) => {
              logger.error(`Error reading from temp price file: ${error.message}`);
              reject(error);
            });

            writeStream.on('error', (error) => {
              logger.error(`Error writing to destination file: ${error.message}`);
              reject(error);
            });

            // Start the streaming process
            logger.info(`Piping temporary price file to destination...`);
            readStream.pipe(writeStream);

          } catch (streamError) {
            reject(streamError);
          }
        });

        // Get file sizes for comparison
        const finalFileStats = fs.statSync(csvFilePath);
        const finalFileSizeKB = Math.round(finalFileStats.size / 1024);

        logger.info(`🎉 Price combination completed successfully with stream approach!`);
        logger.info(`📈 Final statistics:`);
        logger.info(`- Total rows processed: ${processedCount}`);
        logger.info(`- Prices updated: ${updatedCount} (${((updatedCount / processedCount) * 100).toFixed(2)}% of rows)`);
        logger.info(`- Updated file: ${csvFilePath}`);
        logger.info(`- File size: ${finalFileSizeKB} KB`);

        // Clean up backup if everything went well
        if (fs.existsSync(finalBackupPath)) {
          try {
            fs.unlinkSync(finalBackupPath);
            logger.info(`Price backup file deleted as replacement was successful`);
          } catch (cleanupError) {
            logger.warn(`Note: Could not delete price backup file, but operation was successful`);
          }
        }

      } catch (streamError: any) {
        logger.error(`❌ Stream-based price file replacement failed: ${streamError.message}`);

        // Check if our backup file exists and try to restore from it
        const backupPath = csvFilePath + '.price_backup';

        if (fs.existsSync(backupPath)) {
          try {
            logger.info(`Attempting to restore from price backup...`);
            fs.copyFileSync(backupPath, csvFilePath);
            logger.info(`✅ Restored original file from price backup`);
          } catch (restoreError: any) {
            logger.error(`Failed to restore from price backup: ${restoreError.message}`);
          }
        }

        // Try the temporary file as a last resort
        if (fs.existsSync(tempFilePath)) {
          const recoverPath = path.join(path.dirname(csvFilePath), 'price_recovered_' + path.basename(csvFilePath));

          try {
            fs.copyFileSync(tempFilePath, recoverPath);
            logger.info(`✅ Created price recovery file at: ${recoverPath}`);
          } catch (recoverError) {
            if (recoverError instanceof Error) {
              logger.error(`Failed to create price recovery file: ${recoverError.message}`);
            } else {
              logger.error(`Failed to create price recovery file: ${String(recoverError)}`);
            }
          }
        }

        // Since this is a critical step, we should throw the error
        throw new Error(`Price file replacement failed: ${streamError.message}`);
      }

    } catch (error: any) {
      logger.error(`❌ Error in combinePricesInProducts: ${error.message || error}`);
      throw error;
    }
  }
}