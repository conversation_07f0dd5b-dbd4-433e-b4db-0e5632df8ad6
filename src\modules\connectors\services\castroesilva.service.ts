import { ProductModel as BighubProductModel } from '../../bighub/models/product.model';
import { BIGHubCsvPort } from '../../bighub/ports/bighub-csv.port';
import { ProductModel } from '../../catalogue/models/product.model';
import { YamlRepo } from '../../configurations/repos/yaml.repo';
import { CastroesilvaPort } from '../ports/castroesilva.port';
import { CastroesilvaModel } from '../models/castroesilva.model';
import { ConnectorCode } from '../enums/connector-code.enum';
import { CastroesilvaMapper } from '../mappers/castroesilva.mapper';

export class CastroesilvaService {
  castroesilvaRepo: CastroesilvaPort;
  bighubRepo: BIGHubCsvPort
  configuration: YamlRepo

  constructor(
    repo: CastroesilvaPort,
    bighubRepo: BIGHubCsvPort,
    configuration: YamlRepo
  ) {
    this.castroesilvaRepo = repo
    this.bighubRepo = bighubRepo
    this.configuration = configuration
  }

  async getProducts(): Promise<ProductModel[]> {
    const castroesilva = await this.castroesilvaRepo.getAll();
    return castroesilva.map((item: CastroesilvaModel) => CastroesilvaMapper
      .toProduct(item))
      .filter(product => product.stock > 0)
  }

  async getProductsBighub(): Promise<BighubProductModel[]> {
    const BIGHubServiceProducts = await this.castroesilvaRepo.getAll();

    let products = BIGHubServiceProducts.map((item: CastroesilvaModel) => CastroesilvaMapper.toProductBigHub(item))

    // products = await this.castroesilvaRepo.validateOffers(products)

    console.log('Products:', products.length)
    products = products.filter(product => product.ean !== '')
    console.log('After ean filter:', products.length)
    // stock rules 
    products = products.filter(product => product.stock > 0)
    console.log('After stock filter:', products.length)
    // price rules
    products = products.filter(product => product.global_price > 0 && product.global_price <= 1000)
    console.log('After price filter:', products.length)
    // NaN's filter
    products = products.filter(product => !isNaN(product.global_price))
    console.log('After NaN filter:', products.length)
    // tax rules
    products = await this.bighubRepo.applyTaxes(products, ConnectorCode.CASTROESILVA)

    return products;
  }
}