import { BIGHubCsvPort } from '../../bighub/ports/bighub-csv.port';
import { ProductModel } from '../../catalogue/models/product.model';
import { SupplierCode } from '../enums/supplier-code.enum';
import { BigbuyPriceModel } from '../models/bigbuy-price.model';
import { BigbuyPricePort } from '../ports/bigbuy-price.port';
import * as fs from 'fs';
import * as path from 'path';

export class BigbuyPriceService {
  bigbuyPriceRepo: BigbuyPricePort;
  bighubRepo: BIGHubCsvPort;

  constructor(
    repo: BigbuyPricePort,
    bighubRepo: BIGHubCsvPort,
  ) {
    this.bigbuyPriceRepo = repo;
    this.bighubRepo = bighubRepo;
  }

  async getBigbuyPrices(includeLargeQuantities: boolean = false): Promise<BigbuyPriceModel[]> {
    try {
      console.log('Starting BigBuy price synchronization...');
      console.log(`Include large quantities: ${includeLargeQuantities}`);
      
      // Get all products prices from BigBuy
      const priceData = await this.bigbuyPriceRepo.getAllProductsPrices(includeLargeQuantities);
      console.log(`Retrieved price data for ${priceData.length} products`);

      // Get product variations prices
      const variationPriceData = await this.bigbuyPriceRepo.getProductVariationPrices(includeLargeQuantities);
      console.log(`Retrieved price data for ${variationPriceData.length} product variations`);

      // Combine all price data
      const allPriceData = [...priceData, ...variationPriceData];
      console.log(`Total combined price data: ${allPriceData.length} items`);

      // Save raw price data to JSON file in project root with verification
      try {
        // Save with both filenames to ensure compatibility with all code
        const jsonFilePath = path.join(process.cwd(), 'bigbuy_prices.json');
        const altJsonFilePath = path.join(process.cwd(), 'bigbuy-price.json');
        console.log(`💾 Starting to save price data to: ${jsonFilePath} and ${altJsonFilePath}`);
        
        // Prepare JSON string (do this once for efficiency)
        const jsonData = JSON.stringify(allPriceData, null, 2);
        
        // Write files synchronously (blocks until complete)
        fs.writeFileSync(jsonFilePath, jsonData, 'utf8');
        fs.writeFileSync(altJsonFilePath, jsonData, 'utf8');
        
        // Verify the files were written correctly
        if (fs.existsSync(jsonFilePath) && fs.existsSync(altJsonFilePath)) {
          const fileStats = fs.statSync(jsonFilePath);
          const fileSizeKB = Math.round(fileStats.size / 1024);
          console.log(`✅ File verification successful:`);
          console.log(`   - Files exist: ${jsonFilePath}, ${altJsonFilePath}`);
          console.log(`   - File size: ${fileSizeKB} KB`);
          console.log(`   - Items saved: ${allPriceData.length}`);
          
          console.log(`🎉 Price data completely saved and verified!`);
        } else {
          throw new Error('Files were not created successfully');
        }
        
      } catch (saveError) {
        console.error('❌ Error saving raw price data to JSON files:', saveError);
        throw saveError; // Re-throw to prevent proceeding with corrupted data
      }
      
      return allPriceData;

    } catch (error) {
      console.error('Error in getBigbuyPrices:', error);
      throw error;
    }
  }

  /**
   * Clear the repository cache
   */
  clearCache(): void {
    this.bigbuyPriceRepo.clear();
  }

  /**
   * Get price summary information without making another API call
   * Uses the cached price data if available
   */
  async getPriceSummary(includeLargeQuantities: boolean = false): Promise<{
    totalProducts: number;
    totalVariations: number;
    averageWholesalePrice: number;
    minWholesalePrice: number;
    maxWholesalePrice: number;
    productsWithVolumeDiscounts: number;
  }> {
    try {
      // Check if we already have price data in memory or in a file
      let allPriceData: BigbuyPriceModel[] = [];
      const pricePath = path.join(process.cwd(), 'bigbuy-price.json');
      
      // Try to read from file first
      if (fs.existsSync(pricePath)) {
        try {
          console.log('Reading price data from existing file to avoid redundant API calls');
          allPriceData = JSON.parse(fs.readFileSync(pricePath, 'utf8'));
          console.log(`Loaded ${allPriceData.length} price records from file`);
        } catch (fileError) {
          console.warn('Could not load price data from file, will make API call:', fileError);
        }
      }
      
      // If no data found in file, make API call
      if (allPriceData.length === 0) {
        console.log('No cached price data found, fetching from API...');
        allPriceData = await this.getBigbuyPrices(includeLargeQuantities);
      }
      
      // Calculate summary statistics
      const wholesalePrices = allPriceData.map(item => item.wholesalePrice);
      
      const avgWholesale = wholesalePrices.reduce((sum, price) => sum + price, 0) / wholesalePrices.length;
      
      const minWholesale = Math.min(...wholesalePrices);
      const maxWholesale = Math.max(...wholesalePrices);
      
      const productsWithVolumeDiscounts = allPriceData.filter(
        item => item.priceLargeQuantities && item.priceLargeQuantities.length > 0
      ).length;

      // Count products vs variations (variations typically have a dash in the SKU)
      const products = allPriceData.filter(item => !item.sku.includes('-'));
      const variations = allPriceData.filter(item => item.sku.includes('-'));

      return {
        totalProducts: products.length,
        totalVariations: variations.length,
        averageWholesalePrice: parseFloat(avgWholesale.toFixed(2)),
        minWholesalePrice: minWholesale,
        maxWholesalePrice: maxWholesale,
        productsWithVolumeDiscounts
      };
    } catch (error) {
      console.error('Error getting price summary:', error);
      throw error;
    }
  }

  /**
   * Sync price data and save to local files with verification
   */
  async syncAndSavePrice(outputPath: string = './data/price', includeLargeQuantities: boolean = false): Promise<void> {
    try {
      // Ensure output directory exists
      if (!fs.existsSync(outputPath)) {
        fs.mkdirSync(outputPath, { recursive: true });
      }

      // Get all price data
      const priceData = await this.bigbuyPriceRepo.getAllProductsPrices(includeLargeQuantities);
      const variationPriceData = await this.bigbuyPriceRepo.getProductVariationPrices(includeLargeQuantities);

      // Combine all price data
      const allPriceData = [...priceData, ...variationPriceData];
      console.log(`Total combined price data: ${allPriceData.length} items`);

      // Save combined price to project root with verification
      try {
        const rootJsonFilePath = path.join(process.cwd(), 'bigbuy-price.json');
        console.log(`💾 Saving combined price data to: ${rootJsonFilePath}`);
        
        const jsonData = JSON.stringify(allPriceData, null, 2);
        fs.writeFileSync(rootJsonFilePath, jsonData, 'utf8');
        
        // Verify file was saved correctly
        if (fs.existsSync(rootJsonFilePath)) {
          const fileStats = fs.statSync(rootJsonFilePath);
          const fileSizeKB = Math.round(fileStats.size / 1024);
          console.log(`✅ Combined file saved and verified:`);
          console.log(`   - File: ${rootJsonFilePath}`);
          console.log(`   - Size: ${fileSizeKB} KB`);
          console.log(`   - Items: ${allPriceData.length}`);
        } else {
          throw new Error('Combined file was not created successfully');
        }
      } catch (saveError) {
        console.error('❌ Error saving combined price data to JSON file:', saveError);
        throw saveError;
      }

      // Save products price (separate file for backup)
      const productsFile = path.join(outputPath, `bigbuy-products-price-${Date.now()}.json`);
      fs.writeFileSync(productsFile, JSON.stringify(priceData, null, 2), 'utf8');
      console.log(`Saved products price to: ${productsFile}`);

      // Save variations price (separate file for backup)
      const variationsFile = path.join(outputPath, `bigbuy-variations-price-${Date.now()}.json`);
      fs.writeFileSync(variationsFile, JSON.stringify(variationPriceData, null, 2), 'utf8');
      console.log(`Saved variations price to: ${variationsFile}`);

      // Generate summary report
      const summary = await this.getPriceSummary(includeLargeQuantities);
      const summaryFile = path.join(outputPath, `bigbuy-price-summary-${Date.now()}.json`);
      fs.writeFileSync(summaryFile, JSON.stringify(summary, null, 2), 'utf8');
      console.log(`Saved price summary to: ${summaryFile}`);

      console.log(`🎉 All files saved and verified successfully!`);

    } catch (error) {
      console.error('Error syncing and saving price:', error);
      throw error;
    }
  }

  /**
   * Helper method to safely save JSON with verification
   */
  private async saveJsonWithVerification(filePath: string, data: any, description: string): Promise<void> {
    try {
      console.log(`💾 Saving ${description} to: ${filePath}`);
      
      // Convert to JSON string
      const jsonData = JSON.stringify(data, null, 2);
      
      // Write file synchronously (blocks until complete)
      fs.writeFileSync(filePath, jsonData, 'utf8');
      
      // Verify the file exists and has content
      if (fs.existsSync(filePath)) {
        const fileStats = fs.statSync(filePath);
        const fileSizeKB = Math.round(fileStats.size / 1024);
        
        // Verify content can be parsed back
        const savedData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        
        console.log(`✅ ${description} saved and verified:`);
        console.log(`   - File: ${filePath}`);
        console.log(`   - Size: ${fileSizeKB} KB`);
        console.log(`   - Records: ${Array.isArray(savedData) ? savedData.length : 'N/A'}`);
        
        return;
      } else {
        throw new Error(`File was not created: ${filePath}`);
      }
      
    } catch (error) {
      console.error(`❌ Error saving ${description}:`, error);
      throw error;
    }
  }

  /**
   * Find products with volume discounts
   */
  async getProductsWithVolumeDiscounts(): Promise<BigbuyPriceModel[]> {
    try {
      const allProducts = await this.getBigbuyPrices(true);
      return allProducts.filter(product => 
        product.priceLargeQuantities && 
        product.priceLargeQuantities.length > 0
      );
    } catch (error) {
      console.error('Error finding products with volume discounts:', error);
      throw error;
    }
  }

  /**
   * Compare prices with stock levels (requires stock data)
   */
  async analyzePricesByStock(stockFilePath: string = './bigbuy-stock.json'): Promise<{
    inStockAvgPrice: number;
    outOfStockAvgPrice: number;
    priceRanges: { range: string; count: number }[];
  }> {
    try {
      // Load stock data
      if (!fs.existsSync(stockFilePath)) {
        throw new Error(`Stock file not found: ${stockFilePath}`);
      }

      const stockData = JSON.parse(fs.readFileSync(stockFilePath, 'utf8'));
      const priceData = await this.getBigbuyPrices(false);
      
      // Create maps for faster lookup
      const stockMap = new Map();
      stockData.forEach((item: any) => {
        stockMap.set(item.id, item);
      });

      // Analyze prices by stock
      const inStockProducts: BigbuyPriceModel[] = [];
      const outOfStockProducts: BigbuyPriceModel[] = [];
      
      priceData.forEach(priceItem => {
        const stockItem = stockMap.get(priceItem.id);
        if (stockItem) {
          const hasStock = stockItem.stocks.some((s: any) => s.quantity > 0);
          if (hasStock) {
            inStockProducts.push(priceItem);
          } else {
            outOfStockProducts.push(priceItem);
          }
        }
      });
      
      // Calculate average prices
      const inStockAvg = inStockProducts.length > 0 
        ? inStockProducts.reduce((sum, p) => sum + p.wholesalePrice, 0) / inStockProducts.length 
        : 0;
        
      const outOfStockAvg = outOfStockProducts.length > 0 
        ? outOfStockProducts.reduce((sum, p) => sum + p.wholesalePrice, 0) / outOfStockProducts.length 
        : 0;
      
      // Analyze price ranges
      const ranges = [
        { min: 0, max: 10, label: '0-10', count: 0 },
        { min: 10, max: 50, label: '10-50', count: 0 },
        { min: 50, max: 100, label: '50-100', count: 0 },
        { min: 100, max: 500, label: '100-500', count: 0 },
        { min: 500, max: Infinity, label: '500+', count: 0 }
      ];
      
      priceData.forEach(item => {
        const price = item.wholesalePrice;
        for (const range of ranges) {
          if (price >= range.min && price < range.max) {
            range.count++;
            break;
          }
        }
      });
      
      return {
        inStockAvgPrice: parseFloat(inStockAvg.toFixed(2)),
        outOfStockAvgPrice: parseFloat(outOfStockAvg.toFixed(2)),
        priceRanges: ranges.map(r => ({ range: r.label, count: r.count }))
      };
      
    } catch (error) {
      console.error('Error analyzing prices by stock:', error);
      throw error;
    }
  }

  /**
   * Get price history for a specific product (mock implementation - would need actual historical data)
   */
  async getPriceHistory(productId: string): Promise<{ date: string; price: number }[]> {
    try {
      // This is a mock implementation since the API doesn't provide historical data
      // In a real implementation, you would need to store historical price data in your database
      console.warn('Price history is a mock implementation - requires historical data storage');
      
      // Get current price
      const product = await this.bigbuyPriceRepo.getProductPriceById(productId);
      if (!product) {
        throw new Error(`Product not found: ${productId}`);
      }
      
      const currentPrice = product.wholesalePrice;
      
      // Create mock data
      const today = new Date();
      const history = [];
      
      // Add 10 days of mock history
      for (let i = 0; i < 10; i++) {
        const date = new Date();
        date.setDate(today.getDate() - (i * 30)); // Every 30 days back
        
        // Random price variation (±10%)
        const variation = Math.random() * 0.2 - 0.1;
        const historicalPrice = currentPrice * (1 + variation);
        
        history.push({
          date: date.toISOString().split('T')[0],
          price: parseFloat(historicalPrice.toFixed(2))
        });
      }
      
      return history.reverse(); // Chronological order
      
    } catch (error) {
      console.error(`Error getting price history for product ${productId}:`, error);
      throw error;
    }
  }

  /**
   * Export price data to CSV format
   */
  async exportPricesToCsv(outputFilePath: string = './bigbuy-prices.csv'): Promise<string> {
    try {
      const allProducts = await this.getBigbuyPrices(true);
      
      // Create CSV header
      let csvContent = 'id,sku,wholesalePrice,inShopsPrice,hasVolumeDiscounts\n';
      
      // Add rows
      allProducts.forEach(product => {
        const hasVolumeDiscounts = product.priceLargeQuantities && product.priceLargeQuantities.length > 0;
        
        csvContent += `${product.id},${product.sku},${product.wholesalePrice},${product.inShopsPrice},${hasVolumeDiscounts}\n`;
      });
      
      // Write to file
      fs.writeFileSync(outputFilePath, csvContent, 'utf8');
      
      console.log(`✅ Exported ${allProducts.length} price records to CSV: ${outputFilePath}`);
      return outputFilePath;
      
    } catch (error) {
      console.error('Error exporting prices to CSV:', error);
      throw error;
    }
  }

  /**
   * Compare prices between current data and a previous dataset
   */
  async comparePrices(previousPricesPath: string): Promise<{
    increases: number;
    decreases: number;
    unchanged: number;
    averageChange: number;
    changedProducts: { sku: string; oldPrice: number; newPrice: number; changePercent: number }[];
  }> {
    try {
      // Verify previous prices file exists
      if (!fs.existsSync(previousPricesPath)) {
        throw new Error(`Previous prices file not found: ${previousPricesPath}`);
      }
      
      // Load previous prices
      const previousPrices = JSON.parse(fs.readFileSync(previousPricesPath, 'utf8'));
      
      // Get current prices
      const currentPrices = await this.getBigbuyPrices(false);
      
      // Create map of previous prices by ID for fast lookup
      const prevPriceMap = new Map();
      previousPrices.forEach((item: any) => {
        prevPriceMap.set(item.id, item);
      });
      
      // Compare prices
      let increases = 0;
      let decreases = 0;
      let unchanged = 0;
      let totalChangePercent = 0;
      const changedProducts: { sku: string; oldPrice: number; newPrice: number; changePercent: number }[] = [];
      
      currentPrices.forEach(current => {
        const previous = prevPriceMap.get(current.id);
        
        if (previous) {
          if (current.wholesalePrice > previous.wholesalePrice) {
            increases++;
            const changePercent = ((current.wholesalePrice - previous.wholesalePrice) / previous.wholesalePrice) * 100;
            totalChangePercent += changePercent;
            
            changedProducts.push({
              sku: current.sku,
              oldPrice: previous.wholesalePrice,
              newPrice: current.wholesalePrice,
              changePercent: parseFloat(changePercent.toFixed(2))
            });
            
          } else if (current.wholesalePrice < previous.wholesalePrice) {
            decreases++;
            const changePercent = ((previous.wholesalePrice - current.wholesalePrice) / previous.wholesalePrice) * 100;
            totalChangePercent -= changePercent;
            
            changedProducts.push({
              sku: current.sku,
              oldPrice: previous.wholesalePrice,
              newPrice: current.wholesalePrice,
              changePercent: parseFloat((-changePercent).toFixed(2))
            });
            
          } else {
            unchanged++;
          }
        }
      });
      
      const totalChanged = increases + decreases;
      const averageChange = totalChanged > 0 ? totalChangePercent / totalChanged : 0;
      
      // Sort by absolute change percentage (descending)
      changedProducts.sort((a, b) => Math.abs(b.changePercent) - Math.abs(a.changePercent));
      
      return {
        increases,
        decreases,
        unchanged,
        averageChange: parseFloat(averageChange.toFixed(2)),
        changedProducts: changedProducts.slice(0, 100) // Limit to top 100 changes
      };
      
    } catch (error) {
      console.error('Error comparing prices:', error);
      throw error;
    }
  }
}