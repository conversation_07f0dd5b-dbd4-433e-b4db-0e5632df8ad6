import { ProductKnexRepository } from '../../../infra/db/knex/repositories/product-knex.repository'
import { CreateProductBatchUseCaseInterface } from '../../interfaces/use-cases/products/create-product-batch-use-case.interface'
import { CreateProductUseCaseInterface } from '../../interfaces/use-cases/products/create-product-use-case.interface'
import { CreateProductBatchUseCase } from '../../uses-cases/products/create-product-batch.use-case'

export const makeCreateProductBatchFactory = (): CreateProductBatchUseCaseInterface => {
  const repository = new ProductKnexRepository()
  const useCase = new CreateProductBatchUseCase(repository)
  return useCase
}