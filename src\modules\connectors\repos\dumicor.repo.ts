import { <PERSON><PERSON><PERSON> } from "jsdom";
import { DumicorModel } from '../models/dumicor.model';
import got from 'got';
import { YamlRepo } from '../../configurations/repos/yaml.repo';
import { DumicorPort } from '../ports/dumicor.port';
import { ProductModel } from 'src/modules/bighub/models/product.model';
import { logToFile } from "@shared/utils/logger.util";

/**
 * Cleans an HTML string by removing all tags and strange characters.
 * @param htmlString The raw HTML string.
 * @returns A clean text description enclosed in double quotes.
 */
export function cleanHTML(htmlString: string): string {
  // Parse the HTML
  const dom = new JSDOM(htmlString);

  // Extract text content without HTML tags
  let cleanText = dom.window.document.body.textContent || "";

  // Remove all existing double quotes
  cleanText = cleanText.replace(/"/g, "");

  // Remove extra whitespace and special characters
  cleanText = cleanText.replace(/\s+/g, " ").trim();

  return cleanText;
}

/**
 * Removes specific emotion icons from text
 * Targets: 🩵💗😍🌸 and other common emotional emojis
 * @param text The text string that may contain emojis
 * @returns Clean text without emotion icons
 */
export function removeEmojis(text: string): string {
  if (!text) return text;

  // Remove specific emotion icons and related emojis
  return text
    // Remove the specific emojis you mentioned
    .replace(/🩵/g, '') // Light blue heart
    .replace(/💗/g, '') // Growing heart  
    .replace(/😍/g, '') // Heart eyes
    .replace(/🌸/g, '') // Cherry blossom
    // Remove other common heart and love emojis
    .replace(/❤️/g, '') // Red heart
    .replace(/💛/g, '') // Yellow heart
    .replace(/💚/g, '') // Green heart
    .replace(/💙/g, '') // Blue heart
    .replace(/💜/g, '') // Purple heart
    .replace(/🖤/g, '') // Black heart
    .replace(/🤍/g, '') // White heart
    .replace(/🧡/g, '') // Orange heart
    .replace(/🤎/g, '') // Brown heart
    .replace(/💔/g, '') // Broken heart
    .replace(/❣️/g, '') // Heart exclamation
    .replace(/💕/g, '') // Two hearts
    .replace(/💖/g, '') // Sparkling heart
    .replace(/💘/g, '') // Heart with arrow
    .replace(/💝/g, '') // Heart with ribbon
    .replace(/💞/g, '') // Revolving hearts
    .replace(/💟/g, '') // Heart decoration
    // Remove common facial expression emojis with hearts/emotion
    .replace(/🥰/g, '') // Smiling face with hearts
    .replace(/😘/g, '') // Face blowing a kiss
    .replace(/😗/g, '') // Kissing face
    .replace(/☺️/g, '') // Smiling face
    .replace(/😊/g, '') // Smiling face with smiling eyes
    .replace(/🤗/g, '') // Hugging face
    // Remove flower/nature emotion symbols
    .replace(/🌹/g, '') // Rose
    .replace(/🌺/g, '') // Hibiscus
    .replace(/🌻/g, '') // Sunflower
    .replace(/🌷/g, '') // Tulip
    .replace(/💐/g, '') // Bouquet
    // Remove star/sparkle emotion symbols
    .replace(/⭐/g, '') // Star
    .replace(/🌟/g, '') // Glowing star
    .replace(/✨/g, '') // Sparkles
    .replace(/💫/g, '') // Dizzy
    // Clean up variation selectors and zero-width joiners that might be left
    .replace(/[\u{FE0F}\u{200D}]/gu, '')
    // Clean up multiple spaces
    .replace(/\s+/g, ' ')
    .trim();
}

export class DumicorRepo implements DumicorPort {
  private rows: DumicorModel[] = [];
  private configuration: YamlRepo;

  constructor(configuration: YamlRepo) {
    this.configuration = configuration;
  }

  validateOffers(products: ProductModel[]): Promise<ProductModel[]> {
    throw new Error('Method not implemented.');
  }

  async getAll(): Promise<DumicorModel[]> {
    return new Promise(async (resolve, reject) => {
      try {
        // Reset the rows array at the beginning of each call to getAll()
        this.rows = [];
        
        const loginCredentials = this.configuration.get('conexions', 'connector', 'dumicor');
        const apiUrl = loginCredentials.api_url;
        const token = loginCredentials.x_shopify_access_token;
        const productsAmount = 100

        let nextPageUrl: string | null = `${apiUrl}/admin/api/2024-07/products.json?limit=${productsAmount}`;

        let pageNr = 1
        while (nextPageUrl) {
          //console.log(`Fetching: Page ${pageNr} <-> ${pageNr * productsAmount} products`);
          const response = await got<{ products: DumicorModel[] }>(nextPageUrl, {
            responseType: 'json',
            headers: {
              'X-Shopify-Access-Token': token,
            },
          });

          pageNr++
          const products = response.body.products;

          if (!products || products.length === 0) {
            //console.log('No more products to fetch.');
            break;
          }

          // Process each product and all its variants
          products.forEach(product => this.processProductWithVariants(product));

          const linkHeaderRaw = response.headers.link || response.headers.Link;
          const linkHeader = Array.isArray(linkHeaderRaw) ? linkHeaderRaw.join(', ') : linkHeaderRaw;

          nextPageUrl = this.extractNextPageUrl(linkHeader);
        }

        resolve(this.rows);
      } catch (error) {
        reject(error);
      }
    });
  }

  private extractNextPageUrl(linkHeader: string | undefined): string | null {
    if (!linkHeader) return null;
    const match = linkHeader.match(/<(.*?)>; rel="next"/);
    return match ? match[1] : null;
  }

  private processProductWithVariants(product: DumicorModel) {
    const imagePlaceholder = 'https://cdn.bighub.store/image/product-placeholder.png';
    
    logToFile(product, 'products-dumicor.json', 'logs/dumicor');
    
    // Process each variant instead of just the first one
    if (product.variants && product.variants.length > 0) {
      product.variants.forEach(variant => {
        // Find the image that corresponds to this variant if available
        let variantImage = imagePlaceholder;
        
        // If the variant has an image_id, find that image in the product images
        if (variant.image_id && product.images && product.images.length > 0) {
          const matchingImage = product.images.find(img => img.id === variant.image_id);
          if (matchingImage && 'src' in matchingImage) {
            variantImage = matchingImage.src as string;
          }
        } else if (product.image && typeof product.image === 'object' && 'src' in product.image) {
          // Fall back to the main product image if variant doesn't have a specific image
          variantImage = (product.image as { src: string }).src;
        }

        // Build variant title and clean it from emojis
        let variantTitle = product.title || '';
        
        // Append variant options to the title if they exist
        if (variant.title && variant.title !== 'Default Title') {
          variantTitle += ` - ${variant.title}`;
        } else {
          // For more complex cases, build the variant title from options
          const optionValues: string[] = [];
          
          if (product.options && product.options.length > 0) {
            product.options.forEach((option, idx) => {
              const optionKey = `option${idx + 1}` as keyof typeof variant;
              const optionValue = variant[optionKey];
              
              if (optionValue && optionValue !== 'Default Title') {
                optionValues.push(`${option.name}: ${optionValue}`);
              }
            });
          }
          
          if (optionValues.length > 0) {
            variantTitle += ` - ${optionValues.join(', ')}`;
          }
        }

        // Clean the title from emojis
        variantTitle = removeEmojis(variantTitle);

        // Process description - clean HTML and remove emojis
        let cleanDescription = '';
        if (product.body_html) {
          cleanDescription = cleanHTML(product.body_html);
          cleanDescription = removeEmojis(cleanDescription);
        }

        // Handle EAN/barcode formatting (add leading zero if length is 12)
        const barcode = variant.barcode || '';
        const formattedEan = barcode.length === 12 ? `0${barcode}` : barcode;

        const newRow: DumicorModel = {
          ...product,
          ean: formattedEan,
          sku: variant.sku || '',
          title: variantTitle,
          description: cleanDescription,
          brand: product.vendor || '',
          price: parseFloat(variant.price || '0'),
          stock: variant.inventory_quantity ?? 0,
          image: variantImage,
          weight: variant.weight ?? 0,
          variant_id: variant.id,
          product_id: product.id,
          variant_title: variant.title || '',
          option1: variant.option1 || '',
          option2: variant.option2 || '',
          option3: variant.option3 || '',
        };

        this.rows.push(newRow);
      });
    } else {
      // Fallback for products without variants (should be rare in Shopify)
      const imageSrc = (product.image && typeof product.image === 'object' && 'src' in product.image)
        ? (product.image as { src: string }).src
        : imagePlaceholder;

      // Clean title and description from emojis
      let cleanTitle = removeEmojis(product.title || '');
      let cleanDescription = '';
      if (product.body_html) {
        cleanDescription = cleanHTML(product.body_html);
        cleanDescription = removeEmojis(cleanDescription);
      }

      const newRow: DumicorModel = {
        ...product,
        ean: '',
        sku: '',
        title: cleanTitle,
        description: cleanDescription,
        brand: product.vendor || '',
        price: 0,
        stock: 0,
        image: imageSrc,
        weight: 0,
      };

      this.rows.push(newRow);
    }
  }
}