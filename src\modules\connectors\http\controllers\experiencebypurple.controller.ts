import { Request, Response } from 'express';
import { BigHubService } from '../../../bighub/services/bighub.service';
import { ExperiencebypurpleService } from '../../services/experiencebypurple.service';
import fs from 'fs'

export class ExperiencebypurpleController {

  experiencebypurpleService: ExperiencebypurpleService;
  bigHubService: BigHubService;
  
  constructor(bigHubService: BigHubService, experiencebypurpleService: ExperiencebypurpleService) {
    // coolaccesorios service
    this.experiencebypurpleService = experiencebypurpleService;
    // bighub service
    this.bigHubService = bigHubService;
  }
  
  async getProducts(req: Request, res: Response) {
    let response = {
      status: 200,
      message: 'Products found'
    }

    try {
      const products = await this.experiencebypurpleService.getProducts();
      res.status(response.status).json(products)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubCsv(req: Request, res: Response) {
    try {
      const filename = '878-bighub-experiencebypurple.csv';
      res.setHeader('Content-disposition', 'attachment; filename=' + filename);
      res.setHeader('Content-type', 'text/csv');
      fs.createReadStream(filename).pipe(res)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubExportCsv(req: Request, res: Response) {
    try {
      const filename = '878-bighub-experiencebypurple.csv';
      const products = await this.experiencebypurpleService.getProductsBighub();
      this.bigHubService.supplierCreateCsv(filename, products, 878); //878

      res.status(200).send('ok');
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }
}