import { Offer, OfferProps } from "../../../../domain/entities/offer"
import { UseCase } from "../use-case.interface"

export interface GetOffersByEanUseCaseInterface
  extends UseCase<GetOffersByEanUseCaseInterface.Request, GetOffersByEanUseCaseInterface.Response> {
  execute: (request: GetOffersByEanUseCaseInterface.Request) => Promise<GetOffersByEanUseCaseInterface.Response>
}

export namespace GetOffersByEanUseCaseInterface {
  export type Request = Pick<OfferProps, 'ean' | 'user_id'>
  export type Response = Array<Offer> | void
}