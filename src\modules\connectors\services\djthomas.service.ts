import { ProductModel as BighubProductModel } from '../../bighub/models/product.model';
import { BIGHubCsvPort } from '../../bighub/ports/bighub-csv.port';
import { ProductModel } from '../../catalogue/models/product.model';
import { YamlRepo } from '../../configurations/repos/yaml.repo';
import { DjthomasPort } from '../ports/djthomas.port';
import { DjthomasModel } from '../models/djthomas.model';
import { ConnectorCode } from '../enums/connector-code.enum';
import { DjthomasMapper } from '../mappers/djthomas.mapper';

export class DjthomasService {
  djthomasRepo: DjthomasPort;
  bighubRepo: BIGHubCsvPort
  configuration: YamlRepo
  private mpnToEanMap!: Map<string, string>;

  constructor(
    repo: DjthomasPort,
    bighubRepo: BIGHubCsvPort,
    configuration: YamlRepo
  ) {
    this.djthomasRepo = repo
    this.bighubRepo = bighubRepo
    this.configuration = configuration
    this.initializeMpnToEanMap();
  }

  private initializeMpnToEanMap(): void {
    this.mpnToEanMap = new Map<string, string>([
      ['P0XN-G11G015', '5602715400202'],
      ['P7PP-VVC2055', '0560271540037'],
      ['PJ6D-GAC20XS', '0560271540013'], // 6
      ['PUT2-VP7V0NE', '5602715400097'],
      ['P5JJ-909V055', '5602715400011'],
      ['P4SK-EWJ2011', '5602715400134'], // 2
      ['PS5B-J4480NE', '5602715400059'], // 3
      ['P0VP-TVCG0PH', '5602715400141'], // 13
      ['PV88-TNEV018', '5602715400158'],
      ['PX60-F4C1025', '5602715400165'],
      ['P3PS-LNK2020', '5602715400172']
    ]);
  }

  private applyEanMapping(products: BighubProductModel[]): BighubProductModel[] {
    return products.map(product => {
      if (this.mpnToEanMap.has(product.sku)) {
        product.ean = this.mpnToEanMap.get(product.sku) || '';
      }
      return product;
    });
  }

  async getProducts(): Promise<ProductModel[]> {
    const djthomas = await this.djthomasRepo.getAll();
    return djthomas.map((item: DjthomasModel) => DjthomasMapper
      .toProduct(item))
      .filter(product => product.stock > 0)
  }

  async getProductsBighub(): Promise<BighubProductModel[]> {
    const BIGHubServiceProducts = await this.djthomasRepo.getAll();

    let products = BIGHubServiceProducts.map((item: DjthomasModel) => DjthomasMapper.toProductBigHub(item))
    
    // Apply EAN mapping for specific MPNs
    products = this.applyEanMapping(products);

    console.log('Djthomas products', products.length)
   
    products = products.filter(product => product.ean !== '')
    console.log('Djthomas products with EAN filter', products.length)
    // stock rules
    products = products.filter(product => product.stock > 0)
    console.log('Djthomas products with stock filter', products.length)
    // price rules
    products = products.filter(product => product.global_price > 0)
    // NaN's filter
    products = products.filter(product => !isNaN(product.global_price))
    console.log('Djthomas products with price filter', products.length)
    // tax rules
    // 10 % discount in all products
   
    products = await this.bighubRepo.applyTaxes(products, ConnectorCode.DJTHOMAS)

    return products;
  }
}