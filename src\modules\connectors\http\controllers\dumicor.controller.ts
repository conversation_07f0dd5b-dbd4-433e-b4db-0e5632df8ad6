import { Request, Response } from 'express';
import { BigHubService } from '../../../bighub/services/bighub.service';
import { DumicorService } from '../../services/dumicor.service';
import fs from 'fs'

export class DumicorController {

  dumicorService: DumicorService;
  bigHubService: BigHubService;
  
  constructor(bigHubService: BigHubService, dumicorService: DumicorService) {
    // coolaccesorios service
    this.dumicorService = dumicorService;
    // bighub service
    this.bigHubService = bigHubService;
  }
  
  async getProducts(req: Request, res: Response) {
    let response = {
      status: 200,
      message: 'Products found'
    }

    try {
      const products = await this.dumicorService.getProducts();
      res.status(response.status).json(products)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubCsv(req: Request, res: Response) {
    try {
      const filename = '888-bighub-dumicor.csv';
      res.setHeader('Content-disposition', 'attachment; filename=' + filename);
      res.setHeader('Content-type', 'text/csv');
      fs.createReadStream(filename).pipe(res)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubExportCsv(req: Request, res: Response) {
    try {
      const filename = '888-bighub-dumicor.csv';
      const products = await this.dumicorService.getProductsBighub();
      this.bigHubService.supplierCreateCsv(filename, products, 888); //888

      res.status(200).send('ok');
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }
}