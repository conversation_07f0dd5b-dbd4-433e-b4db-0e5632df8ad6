[B2b1-GLOBOMATIK] [INFO] 2025-05-22T15:25:39.418Z - Starting B2b1 product catalog sync 
[B2b1-GLOBOMATIK] [DEBUG] 2025-05-22T15:25:39.420Z - Fetching B2b1 product descriptions {"skus":"all","supplier":"b2b1"}
[B2b1-GLOBOMATIK] [DEBUG] 2025-05-22T15:25:39.424Z - Fetching B2b1 product prices {"skus":"all","supplier":"b2b1"}
[B2b1-GLOBOMATIK] [DEBUG] 2025-05-22T15:25:39.425Z - Fetching B2b1 product stock {"skus":"all","supplier":"b2b1"}
[B2b1-GLOBOMATIK] [DEBUG] 2025-05-22T15:25:39.427Z - Fetching B2b1 product categories {"supplier":"b2b1"}
[B2b1-TEST] Making request to: https://webservice.globomatik.com/api/v2/products/descriptions
[B2b1-TEST] Headers: Object [AxiosHeaders] {
  Accept: 'application/json',
  'Content-Type': 'application/json',
  'Accept-Language': 'es,en,pt',
  'x-globomatik-api-key': 'TESTj4O3o2T4VOzGNs48'
}
[B2b1-TEST] Making request to: https://webservice.globomatik.com/api/v2/products/price
}
[B2b1-TEST] Making request to: https://webservice.globomatik.com/api/v2/products/price
[B2b1-TEST] Headers: Object [AxiosHeaders] {
  Accept: 'application/json',
  'Content-Type': 'application/json',
  'Accept-Language': 'es,en,pt',
  'x-globomatik-api-key': 'TESTj4O3o2T4VOzGNs48'
}
[B2b1-TEST] Making request to: https://webservice.globomatik.com/api/v2/products/stock
[B2b1-TEST] Headers: Object [AxiosHeaders] {
  Accept: 'application/json',
  'Content-Type': 'application/json',
  'Accept-Language': 'es,en,pt',
  'x-globomatik-api-key': 'TESTj4O3o2T4VOzGNs48'
}
[B2b1-TEST] Making request to: https://webservice.globomatik.com/api/v2/products/categories
[B2b1-TEST] Headers: Object [AxiosHeaders] {
  Accept: 'application/json',
  'Content-Type': 'application/json',
  'Accept-Language': 'es,en,pt',
  'x-globomatik-api-key': 'TESTj4O3o2T4VOzGNs48'
}
[B2b1-HTTP] Response error: {
  message: 'Connection timeout to B2b1 API (https://webservice.globomatik.com)',
  code: 'ETIMEDOUT',
  status: undefined,
  url: '/api/v2/products/descriptions'
}
[B2b1-HTTP] Attempt 1 failed, retrying in 1000ms...
[B2b1-HTTP] Response error: {
  message: 'Connection timeout to B2b1 API (https://webservice.globomatik.com)',
  code: 'ETIMEDOUT',
  status: undefined,
  url: '/api/v2/products/price'
}
[B2b1-HTTP] Attempt 1 failed, retrying in 1000ms...
[B2b1-HTTP] Response error: {
  message: 'Connection timeout to B2b1 API (https://webservice.globomatik.com)',
  code: 'ETIMEDOUT',
  status: undefined,
  url: '/api/v2/products/stock'
}
[B2b1-HTTP] Attempt 1 failed, retrying in 1000ms...
[B2b1-HTTP] Response error: {
  message: 'Connection timeout to B2b1 API (https://webservice.globomatik.com)',
  code: 'ETIMEDOUT',
  status: undefined,
  url: '/api/v2/products/categories'
}
[B2b1-HTTP] Attempt 1 failed, retrying in 1000ms...
[B2b1-TEST] Retry attempt 2/3
[B2b1-TEST] Making request to: https://webservice.globomatik.com/api/v2/products/descriptions
[B2b1-TEST] Headers: Object [AxiosHeaders] {
  Accept: 'application/json',
  'Content-Type': 'application/json',
  'Accept-Language': 'es,en,pt',
  'x-globomatik-api-key': 'TESTj4O3o2T4VOzGNs48'
}
[B2b1-TEST] Retry attempt 2/3
[B2b1-TEST] Making request to: https://webservice.globomatik.com/api/v2/products/price
[B2b1-TEST] Headers: Object [AxiosHeaders] {
  Accept: 'application/json',
  'Content-Type': 'application/json',
  'Accept-Language': 'es,en,pt',
  'x-globomatik-api-key': 'TESTj4O3o2T4VOzGNs48'
}
[B2b1-TEST] Retry attempt 2/3
[B2b1-TEST] Making request to: https://webservice.globomatik.com/api/v2/products/stock
[B2b1-TEST] Headers: Object [AxiosHeaders] {
  Accept: 'application/json',
  'Content-Type': 'application/json',
  'Accept-Language': 'es,en,pt',
  'x-globomatik-api-key': 'TESTj4O3o2T4VOzGNs48'
}
[B2b1-TEST] Retry attempt 2/3
[B2b1-TEST] Making request to: https://webservice.globomatik.com/api/v2/products/categories
[B2b1-TEST] Headers: Object [AxiosHeaders] {
  Accept: 'application/json',
  'Content-Type': 'application/json',
  'Accept-Language': 'es,en,pt',
  'x-globomatik-api-key': 'TESTj4O3o2T4VOzGNs48'
}
[B2b1-HTTP] Response error: {
  message: 'Connection timeout to B2b1 API (https://webservice.globomatik.com)',
  code: 'ETIMEDOUT',
  status: undefined,
  url: '/api/v2/products/descriptions'
}
[B2b1-HTTP] Attempt 2 failed, retrying in 1000ms...
[B2b1-HTTP] Response error: {
  message: 'Connection timeout to B2b1 API (https://webservice.globomatik.com)',
  code: 'ETIMEDOUT',
  status: undefined,
  url: '/api/v2/products/price'
}
[B2b1-HTTP] Attempt 2 failed, retrying in 1000ms...
[B2b1-HTTP] Response error: {
  message: 'Connection timeout to B2b1 API (https://webservice.globomatik.com)',
  code: 'ETIMEDOUT',
  status: undefined,
  url: '/api/v2/products/stock'
}
[B2b1-HTTP] Attempt 2 failed, retrying in 1000ms...
[B2b1-HTTP] Response error: {
  message: 'Connection timeout to B2b1 API (https://webservice.globomatik.com)',
  code: 'ETIMEDOUT',
  status: undefined,
  url: '/api/v2/products/categories'
}
[B2b1-HTTP] Attempt 2 failed, retrying in 1000ms...
[B2b1-TEST] Retry attempt 3/3
[B2b1-TEST] Making request to: https://webservice.globomatik.com/api/v2/products/descriptions
[B2b1-TEST] Headers: Object [AxiosHeaders] {
  Accept: 'application/json',
  'Content-Type': 'application/json',
  'Accept-Language': 'es,en,pt',
  'x-globomatik-api-key': 'TESTj4O3o2T4VOzGNs48'
}
[B2b1-TEST] Retry attempt 3/3
[B2b1-TEST] Making request to: https://webservice.globomatik.com/api/v2/products/price
[B2b1-TEST] Headers: Object [AxiosHeaders] {
  Accept: 'application/json',
  'Content-Type': 'application/json',
  'Accept-Language': 'es,en,pt',
  'x-globomatik-api-key': 'TESTj4O3o2T4VOzGNs48'
}
[B2b1-TEST] Retry attempt 3/3
[B2b1-TEST] Making request to: https://webservice.globomatik.com/api/v2/products/stock
[B2b1-TEST] Headers: Object [AxiosHeaders] {
  Accept: 'application/json',
  'Content-Type': 'application/json',
  'Accept-Language': 'es,en,pt',
  'x-globomatik-api-key': 'TESTj4O3o2T4VOzGNs48'
}
[B2b1-TEST] Retry attempt 3/3
[B2b1-TEST] Making request to: https://webservice.globomatik.com/api/v2/products/categories
[B2b1-TEST] Headers: Object [AxiosHeaders] {
  Accept: 'application/json',
  'Content-Type': 'application/json',
  'Accept-Language': 'es,en,pt',
  'x-globomatik-api-key': 'TESTj4O3o2T4VOzGNs48'
}
[B2b1-HTTP] Response error: {
  message: 'Connection timeout to B2b1 API (https://webservice.globomatik.com)',
  code: 'ETIMEDOUT',
  status: undefined,
  url: '/api/v2/products/descriptions'
}
[B2b1-HTTP] All 3 attempts failed
[B2b1-GLOBOMATIK] [ERROR] 2025-05-22T15:26:45.884Z - Error fetching B2b1 product descriptions {
  error: 'Connection timeout to B2b1 API (https://webservice.globomatik.com)',
  stack: 'B2b1ApiError: Connection timeout to B2b1 API (https://webservice.globomatik.com)\n' +
    '    at B2b1HttpClient.enhanceError (C:\\Users\\<USER>\\Documents\\dev\\feedmaster-pro\\src\\modules\\suppliers\\repos\\b2b1.repo.ts:193:12)\n' +
    '    at C:\\Users\\<USER>\\Documents\\dev\\feedmaster-pro\\src\\modules\\suppliers\\repos\\b2b1.repo.ts:166:36\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n' +
    '    at async Axios.request (C:\\Users\\<USER>\\Documents\\dev\\feedmaster-pro\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n' +
    '    at Axios.request (C:\\Users\\<USER>\\Documents\\dev\\feedmaster-pro\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:95:5)',
  meta: { skus: undefined, supplier: 'b2b1' }
}
[B2b1-GLOBOMATIK] [ERROR] 2025-05-22T15:26:45.885Z - Error fetching B2b1 product catalog {
  error: 'Connection timeout to B2b1 API (https://webservice.globomatik.com)',
  stack: 'B2b1ApiError: Connection timeout to B2b1 API (https://webservice.globomatik.com)\n' +
    '    at B2b1HttpClient.enhanceError (C:\\Users\\<USER>\\Documents\\dev\\feedmaster-pro\\src\\modules\\suppliers\\repos\\b2b1.repo.ts:193:12)\n' +
    '    at C:\\Users\\<USER>\\Documents\\dev\\feedmaster-pro\\src\\modules\\suppliers\\repos\\b2b1.repo.ts:166:36\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n' +
    '    at async Axios.request (C:\\Users\\<USER>\\Documents\\dev\\feedmaster-pro\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n' +
    '    at Axios.request (C:\\Users\\<USER>\\Documents\\dev\\feedmaster-pro\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:95:5)',
  meta: { supplier: 'b2b1', testMode: true }
}
[B2b1Service] Error fetching BIGHub products: B2b1ApiError: Connection timeout to B2b1 API (https://webservice.globomatik.com)
    at B2b1HttpClient.enhanceError (C:\Users\<USER>\Documents\dev\feedmaster-pro\src\modules\suppliers\repos\b2b1.repo.ts:193:12)
    at C:\Users\<USER>\Documents\dev\feedmaster-pro\src\modules\suppliers\repos\b2b1.repo.ts:166:36
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Axios.request (C:\Users\<USER>\Documents\dev\feedmaster-pro\node_modules\axios\lib\core\Axios.js:40:14)
    at Axios.request (C:\Users\<USER>\Documents\dev\feedmaster-pro\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:95:5) {
  statusCode: undefined,
  response: undefined
}
[B2b1-HTTP] Response error: {
  message: 'Connection timeout to B2b1 API (https://webservice.globomatik.com)',
  code: 'ETIMEDOUT',
  status: undefined,
  url: '/api/v2/products/price'
}
[B2b1-HTTP] All 3 attempts failed
[B2b1-GLOBOMATIK] [ERROR] 2025-05-22T15:26:45.895Z - Error fetching B2b1 product prices {
  error: 'Connection timeout to B2b1 API (https://webservice.globomatik.com)',
  stack: 'B2b1ApiError: Connection timeout to B2b1 API (https://webservice.globomatik.com)\n' +
    '    at B2b1HttpClient.enhanceError (C:\\Users\\<USER>\\Documents\\dev\\feedmaster-pro\\src\\modules\\suppliers\\repos\\b2b1.repo.ts:193:12)\n' +
    '    at C:\\Users\\<USER>\\Documents\\dev\\feedmaster-pro\\src\\modules\\suppliers\\repos\\b2b1.repo.ts:166:36\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n' +
    '    at async Axios.request (C:\\Users\\<USER>\\Documents\\dev\\feedmaster-pro\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n' +
    '    at Axios.request (C:\\Users\\<USER>\\Documents\\dev\\feedmaster-pro\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:95:5)',
  meta: { skus: undefined, supplier: 'b2b1' }
}
[B2b1-HTTP] Response error: {
  message: 'Connection timeout to B2b1 API (https://webservice.globomatik.com)',
  code: 'ETIMEDOUT',
  status: undefined,
  url: '/api/v2/products/stock'
}
[B2b1-HTTP] All 3 attempts failed
[B2b1-GLOBOMATIK] [ERROR] 2025-05-22T15:26:45.902Z - Error fetching B2b1 product stock {
  error: 'Connection timeout to B2b1 API (https://webservice.globomatik.com)',
  stack: 'B2b1ApiError: Connection timeout to B2b1 API (https://webservice.globomatik.com)\n' +
    '    at B2b1HttpClient.enhanceError (C:\\Users\\<USER>\\Documents\\dev\\feedmaster-pro\\src\\modules\\suppliers\\repos\\b2b1.repo.ts:193:12)\n' +
    '    at C:\\Users\\<USER>\\Documents\\dev\\feedmaster-pro\\src\\modules\\suppliers\\repos\\b2b1.repo.ts:166:36\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n' +
    '    at async Axios.request (C:\\Users\\<USER>\\Documents\\dev\\feedmaster-pro\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n' +
    '    at Axios.request (C:\\Users\\<USER>\\Documents\\dev\\feedmaster-pro\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:95:5)',
  meta: { skus: undefined, supplier: 'b2b1' }
}
[B2b1-HTTP] Response error: {
  message: 'Connection timeout to B2b1 API (https://webservice.globomatik.com)',
  code: 'ETIMEDOUT',
  status: undefined,
  url: '/api/v2/products/categories'
}
[B2b1-HTTP] All 3 attempts failed
[B2b1-GLOBOMATIK] [ERROR] 2025-05-22T15:26:45.912Z - Error fetching B2b1 product categories {
  error: 'Connection timeout to B2b1 API (https://webservice.globomatik.com)',
  stack: 'B2b1ApiError: Connection timeout to B2b1 API (https://webservice.globomatik.com)\n' +
    '    at B2b1HttpClient.enhanceError (C:\\Users\\<USER>\\Documents\\dev\\feedmaster-pro\\src\\modules\\suppliers\\repos\\b2b1.repo.ts:193:12)\n' +
    '    at C:\\Users\\<USER>\\Documents\\dev\\feedmaster-pro\\src\\modules\\suppliers\\repos\\b2b1.repo.ts:166:36\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n' +
    '    at async Axios.request (C:\\Users\\<USER>\\Documents\\dev\\feedmaster-pro\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n' +
    '    at Axios.request (C:\\Users\\<USER>\\Documents\\dev\\feedmaster-pro\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:95:5)',
  meta: { supplier: 'b2b1' }