import { Request, Response } from 'express';
import { BigHubService } from '../../../bighub/services/bighub.service';
import { CastroesilvaService } from '../../services/castroesilva.service';
import fs from 'fs'

export class CastroesilvaController {

  castroesilvaService: CastroesilvaService;
  bigHubService: BigHubService;
  
  constructor(bigHubService: BigHubService, castroesilvaService: CastroesilvaService) {
    // coolaccesorios service
    this.castroesilvaService = castroesilvaService;
    // bighub service
    this.bigHubService = bigHubService;
  }
  
  async getProducts(req: Request, res: Response) {
    let response = {
      status: 200,
      message: 'Products found'
    }

    try {
      const products = await this.castroesilvaService.getProducts();
      res.status(response.status).json(products)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubCsv(req: Request, res: Response) {
    try {
      const filename = '1266-bighub-castroesilva.csv';
      res.setHeader('Content-disposition', 'attachment; filename=' + filename);
      res.setHeader('Content-type', 'text/csv');
      fs.createReadStream(filename).pipe(res)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubExportCsv(req: Request, res: Response) {
    try {
      const filename = '1266-bighub-castroesilva.csv';
      const products = await this.castroesilvaService.getProductsBighub();
      this.bigHubService.supplierCreateCsv(filename, products, 1266); //1266

      res.status(200).send('ok');
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }
}