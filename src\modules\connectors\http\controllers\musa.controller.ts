import { Request, Response } from 'express';
import { BigHubService } from '../../../bighub/services/bighub.service';
import { MusaService } from '../../services/musa.service';
import fs from 'fs'

export class MusaController {

  musaService: MusaService;
  bigHubService: BigHubService;
  
  constructor(bigHubService: BigHubService, musaService: MusaService) {
    // coolaccesorios service
    this.musaService = musaService;
    // bighub service
    this.bigHubService = bigHubService;
  }
  
  async getProducts(req: Request, res: Response) {
    let response = {
      status: 200,
      message: 'Products found'
    }

    try {
      const products = await this.musaService.getProducts();
      res.status(response.status).json(products)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubCsv(req: Request, res: Response) {
    try {
      const filename = '1274-bighub-musa.csv';
      res.setHeader('Content-disposition', 'attachment; filename=' + filename);
      res.setHeader('Content-type', 'text/csv');
      fs.createReadStream(filename).pipe(res)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubExportCsv(req: Request, res: Response) {
    try {
      const filename = '1274-bighub-musa.csv';
      const products = await this.musaService.getProductsBighub();
      this.bigHubService.supplierCreateCsv(filename, products, 1274); //1274

      res.status(200).send('ok');
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }
}