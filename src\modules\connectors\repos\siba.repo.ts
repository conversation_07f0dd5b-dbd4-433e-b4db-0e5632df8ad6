import { SibaModel } from '../models/siba.model';
import got from 'got';
import { YamlRepo } from '../../configurations/repos/yaml.repo';
import { ProductModel } from 'src/modules/bighub/models/product.model';
import { logToFile } from '@shared/utils/logger.util';

export class SibaRepo {
  private rows: SibaModel[] = [];
  private configuration: YamlRepo;
  private baseUrl: string;
  private consumerKey: string;
  private consumerSecret: string;

  constructor(configuration: YamlRepo) {
    this.configuration = configuration;

    // Load Siba configuration
    const wooConfig = this.configuration.get('conexions', 'connector', 'siba');

    // Fix URL if needed - ensure no www prefix is used due to certificate issues
    let baseUrl = wooConfig.api_url;
    if (baseUrl.includes('www.sibanaturalshop.com')) {
      baseUrl = baseUrl.replace('www.sibanaturalshop.com', 'sibanaturalshop.com');
      console.log('URL modified to match SSL certificate: ', baseUrl);
    }

    this.baseUrl = baseUrl;
    this.consumerKey = wooConfig.consumer_key;
    this.consumerSecret = wooConfig.consumer_secret;
  }

  validateOffers(products: ProductModel[]): Promise<ProductModel[]> {
    throw new Error('Method not implemented.');
  }

  /**
   * Get all products from Siba
   */
  async getAll(): Promise<SibaModel[]> {
    try {
      console.log('Fetching products from Siba');
      this.rows = []; // Reset products array

      // Siba API pagination
      let page = 1;
      const perPage = 100;
      let hasMoreProducts = true;

      while (hasMoreProducts) {
        // Construct endpoint with pagination and authentication
        const endpoint = `/wp-json/wc/v3/products?page=${page}&per_page=${perPage}&status=publish&consumer_key=${this.consumerKey}&consumer_secret=${this.consumerSecret}`;

        // Make API request with certificate validation disabled
        const response = await got(`${this.baseUrl}${endpoint}`, {
          responseType: 'json',
          https: {
            rejectUnauthorized: false
          }
        });

        // Process products
        const products = response.body as any[];

        // logToFile(products, 'product-siba.json', 'logs/siba');

        if (products.length === 0) {
          // No more products to fetch
          hasMoreProducts = false;
        } else {
          // Process each product
          for (const product of products) {
            this.rows.push(this.mapToModel(product));
          }

          // Check if we need to fetch more pages
          if (products.length < perPage) {
            hasMoreProducts = false;
          } else {
            page++;
          }
        }
      }

      console.log(`Fetched ${this.rows.length} products from Siba`);
      return this.rows;
    } catch (error) {
      console.error('Error fetching products from Siba:', error);
      throw error;
    }
  }

  /**
   * Map Siba API product to internal model
   */
  private mapToModel(product: any): SibaModel {
    const imagePlaceholder = 'https://cdn.bighub.store/image/product-placeholder.png';

    // Extract the first image if available
    let imageUrl = imagePlaceholder;
    if (product.images && Array.isArray(product.images) && product.images.length > 0) {
      // The image is in the src property of the first image object
      imageUrl = product.images[0].src || imagePlaceholder;
    }

    // Extract category information
    const category = product.categories && product.categories.length > 0 ? product.categories[0].name : '';

    // Extract brand from category or determine from tags/name for iPhone products
    let brand = '';

    // Get tags
    const tags = product.tags && Array.isArray(product.tags)
      ? product.tags.map((tag: any) => tag.name).join(', ')
      : '';

    // Get attributes if available (for variable products)
    const attributes = product.attributes && Array.isArray(product.attributes)
      ? product.attributes.map((attr: any) => ({
        name: attr.name,
        options: Array.isArray(attr.options) ? attr.options.join(', ') : ''
      }))
      : [];

    // Extract EAN from global_unique_id field
    let ean = '';

    // First, try to get it from the global_unique_id field directly
    if (product.global_unique_id) {
      ean = String(product.global_unique_id);
    } else {
      // If not found directly, check in meta_data for global_unique_id
      const globalUniqueIdMeta = product.meta_data?.find((meta: any) => meta.key === 'global_unique_id');
      if (globalUniqueIdMeta && globalUniqueIdMeta.value) {
        ean = String(globalUniqueIdMeta.value);
      }
    }

    return {
      sku: product.sku || String(product.id),  // Prioritize SKU, fall back to ID
      name: product.name || '',
      description_short: product.short_description || product.description || '',
      ean: ean.length === 12 ? '0' + ean : ean,  // Use the extracted EAN from global_unique_id
      brand: brand,  // Use our extracted brand
      price: parseFloat(product.regular_price || product.price || '0'),
      quantity: 5, // Doesn't work with stocks in Siba
      condition: 'new',
      image: imageUrl,
      weight: parseFloat(product.weight || '0'),
      // Additional useful information
      permalink: product.permalink || '',
      category: category,
      tags: tags,
      product_type: product.type || 'simple',
      attributes: attributes
    };
  }
}