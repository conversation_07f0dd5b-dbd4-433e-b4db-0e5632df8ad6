import { MarketplaceKnexRepository } from "../../../infra/db/knex/repositories/marketplace.knex.repository"
import { GetMarketplacesUseCaseInterface } from "../../interfaces/use-cases/marketplaces/get-marketplaces-use-case.interface"
import { GetMarketplacesUseCase } from "../../uses-cases/marketplaces/get-marketplaces.use-case"

export const makeGetMarketplacesFactory = (): GetMarketplacesUseCaseInterface => {
  const marketplaceRepository = new MarketplaceKnexRepository()
  return new GetMarketplacesUseCase(marketplaceRepository)
}