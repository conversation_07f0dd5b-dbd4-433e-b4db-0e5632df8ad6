import { Offer } from "../../../../domain/entities/offer"

export interface CreateOffersBatchRepositoryInterface {
  createOffersBatch: (request: CreateOffersBatchRepositoryInterface.Request, chunkSize: number) => Promise<CreateOffersBatchRepositoryInterface.Response>
}

export namespace CreateOffersBatchRepositoryInterface {
  export type Request = Array<Omit<Offer, 'id' | 'created_at' | 'updated_at'>>
  export type Response = boolean
}