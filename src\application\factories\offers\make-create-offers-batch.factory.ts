import { OfferKnexRepository } from "../../../infra/db/knex/repositories/offer.knex.repository"
import { CreateOffersBatchUseCaseInterface } from "../../interfaces/use-cases/offers/create-offers-batch-use-case.interface"
import { CreateOffersBatchUseCase } from "../../uses-cases/offers/create-offers-batch.use-case"

export const makeCreateOffersBatchFactory = (): CreateOffersBatchUseCaseInterface => {
  const offerRepository = new OfferKnexRepository()
  return new CreateOffersBatchUseCase(offerRepository)
}