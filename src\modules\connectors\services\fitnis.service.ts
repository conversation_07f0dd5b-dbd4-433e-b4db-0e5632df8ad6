import { ProductModel as BighubProductModel } from '../../bighub/models/product.model';
import { BIGHubCsvPort } from '../../bighub/ports/bighub-csv.port';
import { ProductModel } from '../../catalogue/models/product.model';
import { YamlRepo } from '../../configurations/repos/yaml.repo';
import { FitnisPort } from '../ports/fitnis.port';
import { FitnisModel } from '../models/fitnis.model';
import { ConnectorCode } from '../enums/connector-code.enum';
import { FitnisMapper } from '../mappers/fitnis.mapper';

export class FitnisService {
  fitnisRepo: FitnisPort;
  bighubRepo: BIGHubCsvPort
  configuration: YamlRepo

  constructor(
    repo: FitnisPort,
    bighubRepo: BIGHubCsvPort,
    configuration: YamlRepo
  ) {
    this.fitnisRepo = repo
    this.bighubRepo = bighubRepo
    this.configuration = configuration
  }

  async getProducts(): Promise<ProductModel[]> {
    const fitnis = await this.fitnisRepo.getAll();
    return fitnis.map((item: FitnisModel) => FitnisMapper
      .toProduct(item))
      .filter(product => product.stock > 0)
  }

  async getProductsBighub(): Promise<BighubProductModel[]> {
    const BIGHubServiceProducts = await this.fitnisRepo.getAll();

    let products = BIGHubServiceProducts.map((item: FitnisModel) => FitnisMapper.toProductBigHub(item))

    // products = await this.fitnisRepo.validateOffers(products)

    console.log('Products:', products.length)
    products = products.filter(product => product.ean !== '')
    console.log('After ean filter:', products.length)
    // stock rules 
    products = products.filter(product => product.stock > 0)
    console.log('After stock filter:', products.length)
    // price rules
    products = products.filter(product => product.global_price > 0)
    console.log('After price filter:', products.length)
    // NaN's filter
    products = products.filter(product => !isNaN(product.global_price))
    console.log('After NaN filter:', products.length)
    // tax rules
    products = await this.bighubRepo.applyTaxes(products, ConnectorCode.FITNIS)

    return products;
  }
}