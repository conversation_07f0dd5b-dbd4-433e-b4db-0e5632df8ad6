import { Request, Response } from 'express';
import { BigHubService } from '../../../bighub/services/bighub.service';
import { RosmarinusService } from '../../services/rosmarinus.service';
import fs from 'fs'

export class RosmarinusController {

  rosmarinusService: RosmarinusService;
  bigHubService: BigHubService;
  
  constructor(bigHubService: BigHubService, rosmarinusService: RosmarinusService) {
    // coolaccesorios service
    this.rosmarinusService = rosmarinusService;
    // bighub service
    this.bigHubService = bigHubService;
  }
  
  async getProducts(req: Request, res: Response) {
    let response = {
      status: 200,
      message: 'Products found'
    }

    try {
      const products = await this.rosmarinusService.getProducts();
      res.status(response.status).json(products)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubCsv(req: Request, res: Response) {
    try {
      const filename = '1276-bighub-rosmarinus.csv';
      res.setHeader('Content-disposition', 'attachment; filename=' + filename);
      res.setHeader('Content-type', 'text/csv');
      fs.createReadStream(filename).pipe(res)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubExportCsv(req: Request, res: Response) {
    try {
      const filename = '1276-bighub-rosmarinus.csv';
      const products = await this.rosmarinusService.getProductsBighub();
      this.bigHubService.supplierCreateCsv(filename, products, 1276); //1276

      res.status(200).send('ok');
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }
}