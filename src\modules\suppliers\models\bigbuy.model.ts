/**
 * Bigbuy API credentials model
 */
export interface BigbuyCredentials {
    api_token: string;
    api_url?: string;
    test_mode?: boolean;
}

export interface BigbuyModel {
    ID: string
    CATEGORY: string
   	NAME: string
   	ATTRIBUTE1: string
	ATTRIBUTE2: string
	VALUE1: string
	VALUE2: string
	DESCRIPTION: string
	BRAND: string
	FEATURE: string
	PRICE: number
	PVP_BIGBUY: number
	PVD: number
	IVA: number
	VIDEO: string
	EAN13: string
	WIDTH: number
	HEIGHT: number
	DEPTH: number
	WEIGHT: number
	STOCK: number
	DATE_ADD: string
	DATE_UPD: string
	IMAGE1: string
	IMAGE2: string
	IMAGE3: string
	IMAGE4: string
	IMAGE5: string
	IMAGE6: string
	IMAGE7: string
	IMAGE8: string
	CONDITION: string		
}