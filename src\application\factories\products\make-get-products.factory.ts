import { ProductKnexRepository } from '../../../infra/db/knex/repositories/product-knex.repository'
import { GetProductsUseCaseInterface } from '../../interfaces/use-cases/products/get-products-use-case.interface'
import { GetProductsUseCase } from '../../uses-cases/products/get-products.use-case'

export const makeGetProductsFactory = (): GetProductsUseCaseInterface => {
  const repository = new ProductKnexRepository()
  const useCase = new GetProductsUseCase(repository)
  return useCase
}