import { ProductModel as BighubProductModel } from '../../bighub/models/product.model';
import { BIGHubCsvPort } from '../../bighub/ports/bighub-csv.port';
import { ProductModel } from '../../catalogue/models/product.model';
import { YamlRepo } from '../../configurations/repos/yaml.repo';
import { ImperiodasmalasPort } from '../ports/imperiodasmalas.port';
import { ConnectorCode } from '../enums/connector-code.enum';
import { ImperiodasmalasMapper } from '../mappers/imperiodasmalas.mapper';
import { ImperiodasmalasModel } from '../models/imperiodasmalas.model';

export class ImperiodasmalasService {
  imperiodasmalasRepo: ImperiodasmalasPort;
  bighubRepo: BIGHubCsvPort
  configuration: YamlRepo

  constructor(
    repo: ImperiodasmalasPort,
    bighubRepo: BIGHubCsvPort,
    configuration: YamlRepo
  ) {
    this.imperiodasmalasRepo = repo
    this.bighubRepo = bighubRepo
    this.configuration = configuration
  }

  async getProducts(): Promise<ProductModel[]> {
    const imperiodasmalas = await this.imperiodasmalasRepo.getAll();
    return imperiodasmalas.map((item: ImperiodasmalasModel) => ImperiodasmalasMapper
      .toProduct(item))
      .filter(product => product.stock > 0)
  }

  async getProductsBighub(): Promise<BighubProductModel[]> {
    const BIGHubServiceProducts = await this.imperiodasmalasRepo.getAll();

    let products = BIGHubServiceProducts.map((item: ImperiodasmalasModel) => ImperiodasmalasMapper.toProductBigHub(item))

    console.log('Imperio das Malas products', products.length)
    //console.log(products[0])
    products = products.filter(product => product.ean !== '')
    console.log('After ean filter:', products.length)
    // stock rules
    products = products.filter(product => product.stock > 0)
    console.log('After stock filter:', products.length)
    // price rules
    products = products.filter(product => product.global_price > 0)
    console.log('After price filter:', products.length)
    // NaN's filter
    products = products.filter(product => !isNaN(product.global_price))
    console.log('After NaN filter:', products.length)
    // tax rules
    products = await this.bighubRepo.applyTaxes(products, ConnectorCode.IMPERIODASMALAS)

    return products;
  }
}