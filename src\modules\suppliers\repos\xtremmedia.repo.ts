import FtpClient, { ListingElement } from 'ftp'
import { extname } from 'path';
import { FtpCredentials, StorageModel } from '../../storages/models/storage.model';
import { XtremmediaModel } from '../models/xtremmedia.model';
import * as csv from 'fast-csv'
import { XtremmediaPort } from '../ports/xtremmedia.port';
import iconv from 'iconv-lite';
import { defer } from '../../../shared/utils/promise.util';
import * as fs from 'fs';
import { BigHubService } from 'src/modules/bighub/services/bighub.service';

type ItemType = {
  name: string,
  type: string
}

type ResponseType = {
  folder: string
  items: ItemType[]
}

export class XtremmediaRepo implements XtremmediaPort {
  // Remove the class-level ftp instance since we're creating it per operation
  private headers: string[] = [];
  private rows: XtremmediaModel[] = [];
  private credentials: FtpCredentials;
  private connectionTimeout = 30000; // 30 seconds timeout for FTP connections
  private operationTimeout = 120000; // 2 minutes timeout for operations

  constructor(credentials: FtpCredentials) {
    this.credentials = credentials;
  }

  /**
   * Creates and connects a new FTP client with proper timeout handling
   */
  private createFtpClient(): Promise<FtpClient> {
    return new Promise((resolve, reject) => {
      const ftp = new FtpClient();
      
      // Set up timeout for connection
      const connectionTimeoutId = setTimeout(() => {
        ftp.destroy();
        reject(new Error(`FTP connection timed out after ${this.connectionTimeout}ms`));
      }, this.connectionTimeout);
      
      ftp.on('ready', () => {
        clearTimeout(connectionTimeoutId);
        resolve(ftp);
      });
      
      ftp.on('error', (err) => {
        clearTimeout(connectionTimeoutId);
        reject(err);
      });
      
      // Connect with proper credentials
      try {
        ftp.connect(this.credentials);
      } catch (err) {
        clearTimeout(connectionTimeoutId);
        reject(err);
      }
    });
  }

  /**
   * Safely disconnects an FTP client
   */
  private disconnectFtp(ftp: FtpClient | null): void {
    try {
      if (ftp) {
        ftp.end();
      }
    } catch (err) {
      console.error('Error disconnecting from FTP:', err);
    }
  }

  async put(filename: string, content: XtremmediaModel[]): Promise<boolean> {
    throw new Error("Method not implemented.");
  }

  async list(folder: string): Promise<StorageModel> {
    let ftp: FtpClient | null = null;
    
    try {
      // Create and connect FTP client
      ftp = await this.createFtpClient();
      
      return await new Promise((resolve, reject) => {
        // Set operation timeout
        const operationTimeoutId = setTimeout(() => {
          if (ftp) this.disconnectFtp(ftp);
          reject(new Error(`FTP list operation timed out after ${this.operationTimeout}ms`));
        }, this.operationTimeout);
        
        // Make sure ftp is not null before calling methods on it
        if (!ftp) {
          clearTimeout(operationTimeoutId);
          reject(new Error('FTP client is null'));
          return;
        }
        
        ftp.list(folder, (err: Error, list: ListingElement[]) => {
          clearTimeout(operationTimeoutId);
          
          if (err) {
            reject(err);
            return;
          }
          
          const items = list
            .filter((file: any) => file.name.endsWith('.csv'))
            .map(file => ({
              name: file.name,
              type: extname(file.name).replace('.', '')
            }));
            
          resolve({
            folder: folder,
            items
          });
        });
      });
    } catch (error) {
      console.error('FTP list error:', error);
      // Return empty result on error
      return {
        folder: folder,
        items: []
      };
    } finally {
      // Always disconnect when done
      if (ftp) this.disconnectFtp(ftp);
    }
  }

  async get(folder: string, filename: string): Promise<XtremmediaModel[]> {
    let ftp: FtpClient | null = null;
    let fileStream: fs.WriteStream | null = null;
    const tempFilePath = './xtremmedia-original.csv';
    this.rows = [];
    
    try {
      // Create and connect FTP client
      ftp = await this.createFtpClient();
      
      return await new Promise((resolve, reject) => {
        // Set operation timeout
        const operationTimeoutId = setTimeout(() => {
          if (fileStream) fileStream.end();
          if (ftp) this.disconnectFtp(ftp);
          reject(new Error(`FTP get operation timed out after ${this.operationTimeout}ms`));
        }, this.operationTimeout);
        
        // Make sure ftp is not null before calling methods on it
        if (!ftp) {
          clearTimeout(operationTimeoutId);
          reject(new Error('FTP client is null'));
          return;
        }
        
        ftp.get(`${folder}/${filename}`, (err: Error, stream: NodeJS.ReadableStream) => {
          if (err) {
            clearTimeout(operationTimeoutId);
            reject(err);
            return;
          }
          
          const convertedStream = stream.pipe(iconv.decodeStream('ISO-8859-1'));
          fileStream = fs.createWriteStream(tempFilePath);
          
          convertedStream.pipe(fileStream);
          
          convertedStream
            .pipe(csv.parse({ headers: false, delimiter: ',' }))
            .on('data', (row: any) => this.processLine(row))
            .on('end', (rowCount: number) => {
              console.log(`Parsed ${rowCount} rows`);
              clearTimeout(operationTimeoutId);
              resolve(this.rows);
            })
            .on('error', (err) => {
              console.error('CSV parsing error:', err);
              clearTimeout(operationTimeoutId);
              reject(err);
            });
            
          fileStream.on('finish', async () => {
            console.log('File write completed');
            // Upload to S3 after file writing is complete
            try {
              const s3Url = await BigHubService.uploadFileToS3(tempFilePath, 'xtremmedia-original', 'seller',212);
              console.log(`Uploaded to S3: ${s3Url}`);
            } catch (s3Error) {
              console.error('Error uploading to S3, but continuing with data processing:', s3Error);
            }
          });
          
          fileStream.on('error', (err) => {
            console.error('File write error:', err);
          });
        });
      });
    } catch (error) {
      console.error('FTP get error:', error);
      return this.rows; // Return whatever we have
    } finally {
      // Always disconnect when done
      if (ftp) this.disconnectFtp(ftp);
    }
  }

  private processLine(row: any) {
    if (this.headers.length === 0) {
      this.headers = row;
    } else {
      const newRow: XtremmediaModel = {
        ean: row[0],
        sku: row[1],
        title: row[2],
        category: row[3],
        brand: row[4],
        "price (ES)": row[5],
        "price (PT)": row[6],
        "price (IB)": row[7],
        stock: row[8],
        leadtime_to_ship: row[9],
        condition: row[10],
        weight: row[11],
      }
      this.rows.push(newRow);
    }
  }
}