import { ProductModel as BIGHubProductModel } from '../../bighub/models/product.model'
import { ProductModel } from '../../catalogue/models/product.model'
import { SibaModel } from '../models/siba.model'

export class SibaMapper {

  static toProduct(item: SibaModel): ProductModel {
    return {
      ean: item.ean || '',
      sku: item.sku,
      title: item.name,
      description: item.description_short || '',
      category: '',
      stock: item.quantity,
      brand: item.brand || '',
      price: Number(item.price),
      images: [item.image]
    }
  }

  static toProductBigHub(item: SibaModel): BIGHubProductModel {
    const price = Number(item.price)
    const stock = item.quantity
    const setContent = item.description_short !== null && item.description_short !== undefined ? item.description_short.replace(/\n/g, '').replace(/\r/g, '') : ''
    const weight = Number(item.weight)
    
    return {
      ean: item.ean || '',
      sku: item.sku,
      title: item.name,
      description: setContent,
      brand: item.brand || '',
      category: item.category,
      global_price: price,
      stock: stock ,
      condition: 'new',
      image: [item.image],
      min_quantity_alert: 1,
      package_quantity: 1,
      leadtime_to_ship: 5,
      amazon_fr_price: price,
      amazon_fr_quantity: stock,
      amazon_es_price: price,
      amazon_es_quantity: stock,
      amazon_de_price: price,
      amazon_de_quantity: stock,
      amazon_it_price: price,
      amazon_it_quantity: stock,
      amazon_nl_price: price,
      amazon_nl_quantity: stock,
      amazon_pl_price: price,
      amazon_pl_quantity: stock,
      amazon_be_price: price,
      amazon_be_quantity: stock,
      amazon_se_price: price,
      amazon_se_quantity: stock,
      conforama_fr_price: price,
      conforama_fr_quantity: stock,
      conforama_pt_price: price,
      conforama_pt_quantity: stock,
      conforama_es_price: price,
      conforama_es_quantity: stock,
      carrefour_es_price: price,
      carrefour_es_quantity: stock,
      carrefour_fr_price: price,
      carrefour_fr_quantity: stock,
      cdiscount_fr_price: price,
      cdiscount_fr_quantity: stock,
      eprice_it_price: price,
      eprice_it_quantity: stock,
      empik_pl_price: price,
      empik_pl_quantity: stock,
      fnac_es_price: price,
      fnac_es_quantity: stock,
      fnac_pt_price: price,
      fnac_pt_quantity: stock,
      fnac_fr_price: price,
      fnac_fr_quantity: stock,
      manomano_fr_price: price,
      manomano_fr_quantity: stock,
      manomano_es_price: price,
      manomano_es_quantity: stock,
      manomano_it_price: price,
      manomano_it_quantity: stock,
      mediamarkt_fr_price: price,
      mediamarkt_fr_quantity: stock,
      mediamarkt_es_price: price,
      mediamarkt_es_quantity: stock,
      mediamarkt_de_price: price,
      mediamarkt_de_quantity: stock,
      makro_es_price: price,
      makro_es_quantity: stock,
      makro_pt_price: price,
      makro_pt_quantity: stock,
      pccomp_es_price: price,
      pccomp_es_quantity: stock,
      pccomp_fr_price: price,
      pccomp_fr_quantity: stock,
      pccomp_pt_price: price,
      pccomp_pt_quantity: stock,
      worten_es_price: price,
      worten_es_quantity: stock,
      worten_pt_price: price,
      worten_pt_quantity: stock,
      kuantokusta_pt_price: price,
      kuantokusta_pt_quantity: stock,
      miravia_es_price: price, 
      miravia_es_quantity: stock, 
      bigbang_si_price: price,
      bigbang_si_quantity: stock,
      bulevip_es_price: price,
      bulevip_es_quantity: stock,
      elcorteingles_es_price: price,
      elcorteingles_es_quantity: stock,
      eleclerc_fr_price: price,
      eleclerc_fr_quantity: stock,
      leroymerlin_es_price: price,
      leroymerlin_es_quantity: stock,
      leroymerlin_pt_price: price,
      leroymerlin_pt_quantity: stock,
      leroymerlin_fr_price: price,
      leroymerlin_fr_quantity: stock,
      leroymerlin_it_price: price,
      leroymerlin_it_quantity: stock,
      leroymerlin_pl_price: price,
      leroymerlin_pl_quantity: stock,
      misterauto_fr_price: price,
      misterauto_fr_quantity: stock,
      bricodepot_es_price: price,
      bricodepot_es_quantity: stock,
      bricodepot_pt_price: price,
      bricodepot_pt_quantity: stock,
      castorama_fr_price: price,
      castorama_fr_quantity: stock,
      perfumesclub_pt_price: price,
      perfumesclub_pt_quantity: stock,
      phonehouse_es_price: price,
      phonehouse_es_quantity: stock,
      pixmania_es_price: price,
      pixmania_es_quantity: stock,
      pixmania_fr_price: price,
      pixmania_fr_quantity: stock,
      pccomp_it_price: price,
      pccomp_it_quantity: stock,
      rueducommerce_fr_price: price,
      rueducommerce_fr_quantity: stock,
      clubefashion_pt_price: price,
      clubefashion_pt_quantity: stock,
      planetahuerto_es_price: price,
      planetahuerto_es_quantity: stock,
      venteunique_es_price: price,
      venteunique_es_quantity: stock,
      venteunique_be_price: price,
      venteunique_be_quantity: stock,
      venteunique_de_price: price,
      venteunique_de_quantity: stock,
      venteunique_fr_price: price,
      venteunique_fr_quantity: stock,
      venteunique_it_price: price,
      venteunique_it_quantity: stock,
      venteunique_nl_price: price,
      venteunique_nl_quantity: stock,
      venteunique_pt_price: price,
      venteunique_pt_quantity: stock,
      kaufland_de_price: price,
      kaufland_de_quantity: stock,
      conrad_de_price: price,
      conrad_de_quantity: stock,
      quirumedes_es_price: price,
      quirumedes_es_quantity: stock,
      shopapotheke_de_price: price,
      shopapotheke_de_quantity: stock,
      shopapotheke_at_price: price,
      shopapotheke_at_quantity: stock,
      alltricks_es_price: price,
      alltricks_es_quantity: stock, 
      alltricks_fr_price: price,
      alltricks_fr_quantity: stock,     
      tiendanimal_es_price: price,
      tiendanimal_es_quantity: stock,
      truffaut_fr_price: price,
      truffaut_fr_quantity: stock,
      macway_fr_price: price,
      macway_fr_quantity: stock,
      ubaldi_fr_price: price,
      ubaldi_fr_quantity: stock,
      zooplus_fr_price: price,
      zooplus_fr_quantity: stock,
      weight: weight
    }
  }
}