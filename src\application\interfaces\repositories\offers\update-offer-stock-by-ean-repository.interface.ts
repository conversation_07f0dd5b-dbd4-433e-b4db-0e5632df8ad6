import { Offer } from "../../../../domain/entities/offer"

export interface UpdateOfferStockByEanRepositoryInterface {
  updateOfferStockByEan: (request: UpdateOfferStockByEanRepositoryInterface.Request) => Promise<UpdateOfferStockByEanRepositoryInterface.Response>
}

export namespace UpdateOfferStockByEanRepositoryInterface {
  export type Request = Pick<Offer, 'ean' | 'user_id' | 'stock' | 'transaction_id'>
  export type Response = boolean | void
}