import { OfferProps } from "../../../../domain/entities/offer"
import { UseCase } from "../use-case.interface"

export interface SendOfferToChannelUseCaseInterface
  extends UseCase<SendOfferToChannelUseCaseInterface.Request, SendOfferToChannelUseCaseInterface.Response> {
  execute: (request: SendOfferToChannelUseCaseInterface.Request) => Promise<SendOfferToChannelUseCaseInterface.Response>
}

export namespace SendOfferToChannelUseCaseInterface {
  export type Request = Pick<OfferProps, 'channel' | 'transaction_id'>
  export type Response = boolean | void
}