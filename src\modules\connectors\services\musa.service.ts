import { ProductModel as BighubProductModel } from '../../bighub/models/product.model';
import { BIGHubCsvPort } from '../../bighub/ports/bighub-csv.port';
import { ProductModel } from '../../catalogue/models/product.model';
import { YamlRepo } from '../../configurations/repos/yaml.repo';
import { MusaPort } from '../ports/musa.port';
import { MusaModel } from '../models/musa.model';
import { ConnectorCode } from '../enums/connector-code.enum';
import { MusaMapper } from '../mappers/musa.mapper';

export class MusaService {
  musaRepo: MusaPort;
  bighubRepo: BIGHubCsvPort
  configuration: YamlRepo

  constructor(
    repo: MusaPort,
    bighubRepo: BIGHubCsvPort,
    configuration: YamlRepo
  ) {
    this.musaRepo = repo
    this.bighubRepo = bighubRepo
    this.configuration = configuration
  }

  async getProducts(): Promise<ProductModel[]> {
    const musa = await this.musaRepo.getAll();
    return musa.map((item: MusaModel) => MusaMapper
      .toProduct(item))
      .filter(product => product.stock > 0)
  }

  async getProductsBighub(): Promise<BighubProductModel[]> {
    const BIGHubServiceProducts = await this.musaRepo.getAll();

    let products = BIGHubServiceProducts.map((item: MusaModel) => MusaMapper.toProductBigHub(item))

    // products = await this.musaRepo.validateOffers(products)

    products = products.filter(product => product.ean !== '')
    console.log('After ean filter:', products.length)
    
    // category filter - only include products with category "bighub"
    products = products.filter(product => product.category && product.category.toLowerCase() === 'bighub')
    console.log('After category filter:', products.length)
    
    // stock rules 
    products = products.filter(product => product.stock > 0)
    console.log('After stock filter:', products.length)
    // price rules
    products = products.filter(product => product.global_price > 0)
    console.log('After price filter:', products.length)
    // NaN's filter
    products = products.filter(product => !isNaN(product.global_price))
    console.log('After NaN filter:', products.length)
    // tax rules
    products = await this.bighubRepo.applyTaxes(products, ConnectorCode.MUSA)

    return products;
  }
}