import { ProductKnexRepository } from '../../../infra/db/knex/repositories/product-knex.repository'
import { UpdateProductStockBySupplierUseCaseInterface } from '../../interfaces/use-cases/products/update-product-stock-by-supplier-use-case.interface'
import { UpdateProductStockBySupplierUseCase } from '../../uses-cases/products/update-product-by-supplier.user-case'

export const makeUpdateProductStockBySupllierFactory = (): UpdateProductStockBySupplierUseCaseInterface => {
  const repository = new ProductKnexRepository()
  const useCase = new UpdateProductStockBySupplierUseCase(repository)
  return useCase
}