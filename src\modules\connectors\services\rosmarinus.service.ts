import { ProductModel as BighubProductModel } from '../../bighub/models/product.model';
import { BIGHubCsvPort } from '../../bighub/ports/bighub-csv.port';
import { ProductModel } from '../../catalogue/models/product.model';
import { YamlRepo } from '../../configurations/repos/yaml.repo';
import { RosmarinusPort } from '../ports/rosmarinus.port';
import { RosmarinusModel } from '../models/rosmarinus.model';
import { ConnectorCode } from '../enums/connector-code.enum';
import { RosmarinusMapper } from '../mappers/rosmarinus.mapper';

export class RosmarinusService {
  rosmarinusRepo: RosmarinusPort;
  bighubRepo: BIGHubCsvPort
  configuration: YamlRepo

  constructor(
    repo: RosmarinusPort,
    bighubRepo: BIGHubCsvPort,
    configuration: YamlRepo
  ) {
    this.rosmarinusRepo = repo
    this.bighubRepo = bighubRepo
    this.configuration = configuration
  }

  async getProducts(): Promise<ProductModel[]> {
    const rosmarinus = await this.rosmarinusRepo.getAll();
    return rosmarinus.map((item: RosmarinusModel) => RosmarinusMapper
      .toProduct(item))
      .filter(product => product.stock > 0)
  }

  async getProductsBighub(): Promise<BighubProductModel[]> {
    const BIGHubServiceProducts = await this.rosmarinusRepo.getAll();

    let products = BIGHubServiceProducts.map((item: RosmarinusModel) => RosmarinusMapper.toProductBigHub(item))

    // products = await this.rosmarinusRepo.validateOffers(products)
    console.log('Rosmarinus products', products.length)
    products = products.filter(product => product.ean !== '')
    console.log('Rosmarinus products with EAN filter', products.length)
    // stock rules
    
    products = products.filter(product => product.stock > 0)
    console.log('Rosmarinus products with stock filter', products.length)
    // price rules
    products = products.filter(product => product.global_price > 0)
    // NaN's filter
    products = products.filter(product => !isNaN(product.global_price))
    console.log('Rosmarinus products with price filter', products.length)
    // tax rules
    
    products = await this.bighubRepo.applyTaxes(products, ConnectorCode.ROSMARINUS)

    return products;
  }
}