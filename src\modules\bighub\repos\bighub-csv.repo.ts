import * as path from 'path'
import fs, { createWriteStream } from 'fs'
import * as csv from 'fast-csv'
import { ProductModel } from '../models/product.model';
import { BIGHubCsvPort } from '../ports/bighub-csv.port';
import { StorageModel } from '../../storages/models/storage.model';
import { ConfigurationPort } from '../../configurations/ports/configuration.port';
import { stat } from 'node:fs/promises';
import { format } from 'fast-csv';
import { defer } from '../../../shared/utils/promise.util';
import { google } from 'googleapis';
import { GoogleAuth, JWT } from 'google-auth-library';


export type Tax = {
  name: string;
  rate: number
}

interface Control {
  supplier: string,
  country: string,
  status: string, //ON/OFF/DEV/WAIT/EXT
  email: string,
  password: string,
  id_bighub: number,
  m1: number,
  m2: number,
  m3: number,
  //m4: number,
  integration_type_code: string,
  cat_prices: string, //Não/Sim
  custom_m1: number,
  custom_m2: number,
  custom_m3: number,
  stockly_m1: number
}

interface Solos {
  ean: string,
  supplier: string,
  m1: number,
  m2: number,
  m3: number,
  status: string //ON/OFF
}

interface MarkupCategories {
  category: string
  markup_1: number
  markup_2: number
}

interface SendCosts {
  send_from_country: string
  send_to_marketplace_country: string
  scope_1: number           // 1 a 5 kg
  scope_2: number           // 5 a 10 kg
  scope_3: number           // 10 a 50 kg
  scope_4: number           // 50 a 100 kg
  scope_5: number           // 100 a 200 kg
  scope_6: number           // 200 a 300 kg
  valor_base: number
}

export class BIGHubCsvRepo implements BIGHubCsvPort {

  constructor(private readonly configuration: ConfigurationPort) { }

  list(folder: string): Promise<StorageModel> {
    throw new Error('Method not implemented.');
  }

  get(filename: string): Promise<ProductModel[]> {
    throw new Error('Method not implemented.');
  }

  async put(filename: string, products: ProductModel[]): Promise<boolean> {

    return new Promise((resolve) => {
      const csvStream = csv.format({ headers: true })
      csvStream.pipe(
        fs.createWriteStream(filename, { encoding: 'utf8' })
      );

      for (let i = 0; i < products.length; i++) {
        const product = products[i]
        csvStream.write(product)

        if (i === products.length - 1) {
          // console.log(` ${ products.length } products have been exported to file`)
          //csvStream.write(null)
          resolve(true)
        }
      }

    })
  }

  async append(filename: string, products: ProductModel[]): Promise<boolean> {
    const deferred = defer()

    const csvOptions = { headers: false, includeEndRowDelimiter: true }

    try { await stat(filename) } catch (error) {
      csvOptions.headers = true
    }

    const stream = createWriteStream(filename, { flags: 'a+' })
    const formatCsv = format(csvOptions)

    formatCsv.on('end', () => {
      console.log('finished writing csv file')
      deferred.resolve()
    })

    formatCsv.pipe(stream)

    for (let i = 0; i < products.length; i++) {
      const product = products[i]
      formatCsv.write(product)

      if (i == products.length - 1) {
        formatCsv.end()
      }
    }

    await deferred.promise

    return true

  }

  streamCsv(products: ProductModel[]) {
    const csvStream = csv.format({ headers: true })
    //const encoding = 'windows-1252';
    //csvStream
    //  .pipe(iconv.decodeStream(encoding))
    //  .pipe(iconv.encodeStream(encoding))

    for (let i = 0; i < products.length; i++) {
      const product = products[i]
      csvStream.write(product)

      if (i === products.length) {
        // console.log(` ${ products.length } products have been exported to file`)
        csvStream.write(null)
      }
    }

    return csvStream;
  }

  async applyTaxes(products: ProductModel[], supplierCode: string): Promise<ProductModel[]> {
    // before doing the Mapper
    // check the original categories + subcategories fields according with each original CSV from the supplier


    // load respective JSON
    let categories: any = ''

    switch (supplierCode) {
      case 'aseuropa':
      case 'coolaccesorios':
      case 'depau':
      case 'hispamicro':
      case 'infortisa':
      case 'inpexopcion':
      case 'megasur':
      case 'next':
      case 'supercomp':
      case 'apokin':
      case 'dmi':
      case 'linku':
      case 'ribamundo':
      case 'compuspain':
      case 'recline':
      case 'eurovideo':
        // Read JSON file
        const jsonFilePath = path.join(`./src/modules/suppliers/jsons/markups-cats-${supplierCode}.json`);
        const jsonData = fs.readFileSync(jsonFilePath, 'utf-8');
        categories = JSON.parse(jsonData);
        break
      default:
        break
    }

    // load send costs by supplier country -------------------------------------

    let send_costs_data: any = ''
    // Read JSON file 
    const jsonFilePath = path.join(`./src/modules/suppliers/jsons/universal_bighug_send_costs.json`);
    const jsonData = fs.readFileSync(jsonFilePath, 'utf-8');
    send_costs_data = JSON.parse(jsonData);

    let debug = false
    let debugSync = false
    let debugEan = ''

    //debug ? console.log(send_costs_data) : ''

    let supplierName: string
    let supplierOrigin: string
    let supplierStatus: string
    let id_bighub: number
    let m1: number
    let m2: number
    let m3: number
    // let m4: number
    let integration_type_code: string
    let cat_prices: string
    let custom_m1: number
    let custom_m2: number
    let custom_m3: number
    let special_cat: boolean
    let stockly_m1: number

    // Uso da função
    console.log(`Supplier Name: ${supplierCode}`)

    const data = await getSupplierData(supplierCode)/*.then(data => {*/

    if (typeof data === 'string') {
      console.error('Error:', data);
      // What happens if the API fetch fails ???? 
      // Ideal is not to render the CSV
      return []
    }

    let soloEansExist: boolean = false;
    const eanData = await getEanSoloData(supplierCode);


    if (typeof eanData === 'string') {
      if (debugSync) {
        console.log('-------------------------------------------');
        console.error('Alert:', eanData);
      }
    } else {
      soloEansExist = true;

      if (debugSync) {
        console.log('-------------------------------------------');
        console.log(eanData);
      }
    }

    debugSync ? console.log('-------------------------------------------') : ''
    debugSync ? console.log('Sincronia de dados via GoogleSheets -------') : ''
    debugSync ? console.log('-------------------------------------------') : ''

    supplierName = data.supplier
    supplierOrigin = data.country
    supplierStatus = data.status //ON/OFF/DEV/WAIT
    id_bighub = data.id_bighub
    m1 = data.m1
    m2 = data.m2
    m3 = data.m3
    // m4 = data.m4
    integration_type_code = data.integration_type_code
    cat_prices = data.cat_prices //Não/Sim
    custom_m1 = data.custom_m1
    custom_m2 = data.custom_m2
    custom_m3 = data.custom_m3
    stockly_m1 = data.stockly_m1

    debugSync ? console.log(data) : ''

    let fm1: number = m1
    let fm2: number = m2
    let fm3: number = m3

    let newProducts = products.map(product => {

      const category: string = product.category !== undefined ? product.category : ''
      const ean = product.ean

      special_cat = false
      soloEansExist = false
      // debug ? console.log(`EAN: ${ean}`) : ''

      let weight: number = 0

      product.weight !== undefined ? weight = Number(product.weight) : weight = 0

      const costPrice = Number(product.global_price)
      let sendCostsMisterautoFR: number = 0
      let sendCostsBricodepotES: number = 0
      let sendCostsBricodepotPT: number = 0
      let sendCostsCastoramaFR: number = 0
      let sendCostsConforamaFR: number = 0
      let sendCostsConforamaPT: number = 0
      let sendCostsConforamaES: number = 0
      let sendCostsEmpikPL: number = 0
      let sendCostsFnacES: number = 0
      let sendCostsFnacPT: number = 0
      let sendCostsFnacFR: number = 0
      let sendCostsManoamanoES: number = 0
      let sendCostsCarrefourES: number = 0
      let sendCostsCarrefourFR: number = 0
      let sendCostsPccompES: number = 0
      let sendCostsPccompPT: number = 0
      let sendCostsPccompIT: number = 0
      let sendCostsPccompFR: number = 0
      let sendCostsMakroES: number = 0
      let sendCostsMakroPT: number = 0
      let sendCostsWortenES: number = 0
      let sendCostsWortenPT: number = 0
      let sendCostsMediamarktES: number = 0
      let sendCostsMediamarktDE: number = 0
      let sendCostsKuantokustaPT: number = 0
      let sendCostsCdiscountFR: number = 0
      let sendCostsMiraviaES: number = 0
      let sendCostsBigbangSI: number = 0
      let sendCostsBulevipES: number = 0
      let sendCostsElcorteinglesES: number = 0
      let sendCostsEleclercFR: number = 0
      let sendCostsEpriceIT: number = 0
      let sendCostsLeroymerlinES: number = 0
      let sendCostsLeroymerlinPT: number = 0
      let sendCostsLeroymerlinFR: number = 0
      let sendCostsLeroymerlinIT: number = 0
      let sendCostsLeroymerlinPL: number = 0
      let sendCostsPerfumesclubPT: number = 0
      let sendCostsPhonehouseES: number = 0
      let sendCostsPixmaniaES: number = 0
      let sendCostsPixmaniaFR: number = 0
      let sendCostsRueducommerceFR: number = 0
      let sendCostsClubefashionPT: number = 0
      let sendCostsPlanetahuertoES: number = 0
      let sendCostsVenteuniqueES: number = 0
      let sendCostsVenteuniqueBE: number = 0
      let sendCostsVenteuniqueDE: number = 0
      let sendCostsVenteuniqueFR: number = 0
      let sendCostsVenteuniqueIT: number = 0
      let sendCostsVenteuniqueNL: number = 0
      let sendCostsVenteuniquePT: number = 0
      let sendCostsKauflandDE: number = 0
      let sendCostsConradDE: number = 0
      let sendCostsQuirumedesES: number = 0
      let sendCostsShopapothekeDE: number = 0
      let sendCostsShopapothekeAT: number = 0
      let sendCostsAlltricksES: number = 0
      let sendCostsAlltricksFR: number = 0
      let sendCostsTiendanimalES: number = 0
      let sendCostsMacwayFR: number = 0
      let sendCostsZooplusFR: number = 0
      let sendCostsTruffautFR: number = 0
      let sendCostsUbaldiFR: number = 0

      // misterauto
      sendCostsMisterautoFR = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'França', send_costs_data) : 0

      // bricodepot
      sendCostsBricodepotES = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Espanha', send_costs_data) : 0
      sendCostsBricodepotPT = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Portugal', send_costs_data) : 0

      // castorama
      sendCostsCastoramaFR = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'França', send_costs_data) : 0

      // zooplus
      sendCostsZooplusFR = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'França', send_costs_data) : 0

      // truffaut
      sendCostsTruffautFR = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'França', send_costs_data) : 0

      // ubaldi
      sendCostsUbaldiFR = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'França', send_costs_data) : 0

      // conforama
      sendCostsConforamaFR = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'França', send_costs_data) : 0
      sendCostsConforamaPT = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Portugal', send_costs_data) : 0
      sendCostsConforamaES = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Espanha', send_costs_data) : 0

      // empik
      sendCostsEmpikPL = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Polónia', send_costs_data) : 0
      // fnac
      sendCostsFnacES = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Espanha', send_costs_data) : 0
      sendCostsFnacPT = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Portugal', send_costs_data) : 0
      sendCostsFnacFR = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'França', send_costs_data) : 0
      // manoamano
      sendCostsManoamanoES = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Espanha', send_costs_data) : 0
      // carrefour
      sendCostsCarrefourES = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Espanha', send_costs_data) : 0
      sendCostsCarrefourFR = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'França', send_costs_data) : 0

      // pccomp
      sendCostsPccompES = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Espanha', send_costs_data) : 0
      sendCostsPccompPT = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Portugal', send_costs_data) : 0
      sendCostsPccompIT = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Itália', send_costs_data) : 0
      sendCostsPccompFR = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'França', send_costs_data) : 0

      // makro
      sendCostsMakroES = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Espanha', send_costs_data) : 0
      sendCostsMakroPT = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Portugal', send_costs_data) : 0

      // worten
      sendCostsWortenES = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Espanha', send_costs_data) : 0
      sendCostsWortenPT = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Portugal', send_costs_data) : 0

      // mediamarkt
      sendCostsMediamarktES = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Espanha', send_costs_data) : 0
      sendCostsMediamarktDE = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Alemanha', send_costs_data) : 0

      // kuanto kusta
      sendCostsKuantokustaPT = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Portugal', send_costs_data) : 0

      // cdiscount
      sendCostsCdiscountFR = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'França', send_costs_data) : 0

      // miravia
      sendCostsMiraviaES = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Espanha', send_costs_data) : 0

      // bigbang
      sendCostsBigbangSI = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Eslovénia', send_costs_data) : 0

      // bulevip
      sendCostsBulevipES = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Espanha', send_costs_data) : 0

      // elcorteingles
      sendCostsElcorteinglesES = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Espanha', send_costs_data) : 0

      // eleclerc
      sendCostsEleclercFR = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'França', send_costs_data) : 0

      // eprice
      sendCostsEpriceIT = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Itália', send_costs_data) : 0

      // leroy merlin
      sendCostsLeroymerlinES = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Espanha', send_costs_data) : 0
      sendCostsLeroymerlinPT = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Portugal', send_costs_data) : 0
      sendCostsLeroymerlinFR = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'França', send_costs_data) : 0
      sendCostsLeroymerlinIT = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Itália', send_costs_data) : 0
      sendCostsLeroymerlinPL = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Polónia', send_costs_data) : 0

      // perfumes club
      sendCostsPerfumesclubPT = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Portugal', send_costs_data) : 0

      // phone house
      sendCostsPhonehouseES = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Espanha', send_costs_data) : 0

      // pixmania
      sendCostsPixmaniaES = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Espanha', send_costs_data) : 0
      sendCostsPixmaniaFR = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'França', send_costs_data) : 0

      // rueducomerce
      sendCostsRueducommerceFR = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'França', send_costs_data) : 0

      // clubefashion
      sendCostsClubefashionPT = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Portugal', send_costs_data) : 0

      // planethuerto
      sendCostsPlanetahuertoES = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Espanha', send_costs_data) : 0

      // venteunique
      sendCostsVenteuniqueES = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Espanha', send_costs_data) : 0
      sendCostsVenteuniqueBE = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Bélgica', send_costs_data) : 0
      sendCostsVenteuniqueDE = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Alemanha', send_costs_data) : 0
      sendCostsVenteuniqueFR = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'França', send_costs_data) : 0
      sendCostsVenteuniqueIT = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Itália', send_costs_data) : 0
      sendCostsVenteuniqueNL = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Holanda', send_costs_data) : 0
      sendCostsVenteuniquePT = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Portugal', send_costs_data) : 0

      // kaufland
      sendCostsKauflandDE = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Alemanha', send_costs_data) : 0

      // conrad
      sendCostsConradDE = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Alemanha', send_costs_data) : 0

      // quirumedes
      sendCostsQuirumedesES = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Espanha', send_costs_data) : 0

      // shopapotheke
      sendCostsShopapothekeDE = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Alemanha', send_costs_data) : 0
      sendCostsShopapothekeAT = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Áustria', send_costs_data) : 0

      // alltricks
      sendCostsAlltricksES = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Espanha', send_costs_data) : 0
      sendCostsAlltricksFR = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'França', send_costs_data) : 0

      // tiendanimal
      sendCostsTiendanimalES = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'Espanha', send_costs_data) : 0

      // macway
      sendCostsMacwayFR = supplierStatus !== "CONNECTOR" ? BIGHubCsvRepo.setSendCosts(weight, supplierOrigin, 'França', send_costs_data) : 0

      //console.log(categories)
      // calculation if specific categories exists
      if (categories !== '') {
        const prod: MarkupCategories | undefined = categories.find(
          (item: MarkupCategories) => item.category === category
        );
        //console.log(prod)

        if (prod) {
          fm1 = custom_m1
          fm2 = custom_m2
          fm3 = custom_m3
          debug && ean === debugEan ? console.log(`Produto em categoria especial: ${category}`) : ''
          // console.log(`Produto em categoria especial: ${category}`)
          special_cat = true
        } else {
          fm1 = m1
          fm2 = m2
          fm3 = m3
          special_cat = false
        }
      } else {
        fm1 = m1
        fm2 = m2
        fm3 = m3
        special_cat = false
      }

      let mergingExist = []

      if (typeof eanData !== 'string') {
        const eanItem = eanData.find((item) => item.ean === ean);
        //
        mergingExist = [eanItem?.m1, eanItem?.m2, eanItem?.m3]
        if (mergingExist.filter(v => v == 0).length >= 2) {
          // entra até 2 valores a 0
          if (special_cat === false) {
            fm1 = m1
            fm2 = m2
            fm3 = m3
          } else {
            fm1 = custom_m1
            fm2 = custom_m2
            fm3 = custom_m3
          }
          soloEansExist = false
        } else {
          if (eanItem) {
            console.log(`EAN ${ean} exists in the eanData array.`);
            console.log('Corresponding row data:', eanItem);
            if (eanItem.status === 'ON') {
              fm1 = eanItem.m1
              fm2 = eanItem.m2
              fm3 = eanItem.m3
              console.log(`M1: ${fm1}`)
              console.log(`M2: ${fm2}`)
              console.log(`M3: ${fm3}`)
              soloEansExist = true
            } else {
              if (special_cat === false) {
                fm1 = m1
                fm2 = m2
                fm3 = m3
              } else {
                fm1 = custom_m1
                fm2 = custom_m2
                fm3 = custom_m3
              }
              soloEansExist = false
            }
          } else {
            if (special_cat === false) {
              fm1 = m1
              fm2 = m2
              fm3 = m3
            } else {
              fm1 = custom_m1
              fm2 = custom_m2
              fm3 = custom_m3
            }
            soloEansExist = false
          }
        }
      } else {
        if (special_cat === false) {
          fm1 = m1
          fm2 = m2
          fm3 = m3
        } else {
          fm1 = custom_m1
          fm2 = custom_m2
          fm3 = custom_m3
        }
        soloEansExist = false
      }
      // global_price 
      // valor default de apresentação na BIGhub, 
      // valor puro sem particularidades de marketplace
      // sem portes ?? ou com portes padrão??
      // defenir em linguagem natural // passo a passo

      // misterauto FR
      const sendCostNoTaxMisterautoFR = sendCostsMisterautoFR - (sendCostsMisterautoFR * (fm3 / 100))
      const liquidPriceMisterautoFR = costPrice + sendCostNoTaxMisterautoFR
      const finalPvpMisterautoFR = Number((liquidPriceMisterautoFR / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueMisterautoFR = finalPvpMisterautoFR * (fm3 / 100)
      const marketplaceComissionMisterautoFR = Number((finalPvpMisterautoFR * (fm2 / 100)).toFixed(2))
      const profitMisterautoFR = Number((finalPvpMisterautoFR * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('MISTERAUTO - FR', ean, sendCostsMisterautoFR, sendCostNoTaxMisterautoFR, costPrice, fm1, liquidPriceMisterautoFR, fm2, fm3, finalPvpMisterautoFR, realTaxValueMisterautoFR, marketplaceComissionMisterautoFR, profitMisterautoFR, special_cat, category) : ''

      // bricodepot ES
      const sendCostNoTaxBricodepotES = sendCostsBricodepotES - (sendCostsBricodepotES * (fm3 / 100))
      const liquidPriceBricodepotES = costPrice + sendCostNoTaxBricodepotES
      const finalPvpBricodepotES = Number((liquidPriceBricodepotES / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueBricodepotES = finalPvpBricodepotES * (fm3 / 100)
      const marketplaceComissionBricodepotES = Number((finalPvpBricodepotES * (fm2 / 100)).toFixed(2))
      const profitBricodepotES = Number((finalPvpBricodepotES * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('BRICODEPOT - ES', ean, sendCostsBricodepotES, sendCostNoTaxBricodepotES, costPrice, fm1, liquidPriceBricodepotES, fm2, fm3, finalPvpBricodepotES, realTaxValueBricodepotES, marketplaceComissionBricodepotES, profitBricodepotES, special_cat, category) : ''

      // bricodepot PT
      const sendCostNoTaxBricodepotPT = sendCostsBricodepotPT - (sendCostsBricodepotPT * (fm3 / 100))
      const liquidPriceBricodepotPT = costPrice + sendCostNoTaxBricodepotPT
      const finalPvpBricodepotPT = Number((liquidPriceBricodepotPT / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueBricodepotPT = finalPvpBricodepotPT * (fm3 / 100)
      const marketplaceComissionBricodepotPT = Number((finalPvpBricodepotPT * (fm2 / 100)).toFixed(2))
      const profitBricodepotPT = Number((finalPvpBricodepotPT * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('BRICODEPOT - PT', ean, sendCostsBricodepotPT, sendCostNoTaxBricodepotPT, costPrice, fm1, liquidPriceBricodepotPT, fm2, fm3, finalPvpBricodepotPT, realTaxValueBricodepotPT, marketplaceComissionBricodepotPT, profitBricodepotPT, special_cat, category) : ''

      // castorama FR
      const sendCostNoTaxCastoramaFR = sendCostsCastoramaFR - (sendCostsCastoramaFR * (fm3 / 100))
      const liquidPriceCastoramaFR = costPrice + sendCostNoTaxCastoramaFR
      const finalPvpCastoramaFR = Number((liquidPriceCastoramaFR / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueCastoramaFR = finalPvpCastoramaFR * (fm3 / 100)
      const marketplaceComissionCastoramaFR = Number((finalPvpCastoramaFR * (fm2 / 100)).toFixed(2))
      const profitCastoramaFR = Number((finalPvpCastoramaFR * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('CASTORAMA - FR', ean, sendCostsCastoramaFR, sendCostNoTaxCastoramaFR, costPrice, fm1, liquidPriceCastoramaFR, fm2, fm3, finalPvpCastoramaFR, realTaxValueCastoramaFR, marketplaceComissionCastoramaFR, profitCastoramaFR, special_cat, category) : ''

      // conforama FR
      const sendCostNoTaxConforamaFR = sendCostsConforamaFR - (sendCostsConforamaFR * (fm3 / 100))
      const liquidPriceConforamaFR = costPrice + sendCostNoTaxConforamaFR
      const finalPvpConforamaFR = Number((liquidPriceConforamaFR / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueConforamaFR = finalPvpConforamaFR * (fm3 / 100)
      const marketplaceComissionConforamaFR = Number((finalPvpConforamaFR * (fm2 / 100)).toFixed(2))
      const profitConforamaFR = Number((finalPvpConforamaFR * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('CONFORAMA - FR', ean, sendCostsConforamaFR, sendCostNoTaxConforamaFR, costPrice, fm1, liquidPriceConforamaFR, fm2, fm3, finalPvpConforamaFR, realTaxValueConforamaFR, marketplaceComissionConforamaFR, profitConforamaFR, special_cat, category) : ''

      // conforama PT
      const sendCostNoTaxConforamaPT = sendCostsConforamaPT - (sendCostsConforamaPT * (fm3 / 100))
      const liquidPriceConforamaPT = costPrice + sendCostNoTaxConforamaPT
      const finalPvpConforamaPT = Number((liquidPriceConforamaPT / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueConforamaPT = finalPvpConforamaPT * (fm3 / 100)
      const marketplaceComissionConforamaPT = Number((finalPvpConforamaPT * (fm2 / 100)).toFixed(2))
      const profitConforamaPT = Number((finalPvpConforamaPT * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('CONFORAMA - PT', ean, sendCostsConforamaPT, sendCostNoTaxConforamaPT, costPrice, fm1, liquidPriceConforamaPT, fm2, fm3, finalPvpConforamaPT, realTaxValueConforamaPT, marketplaceComissionConforamaPT, profitConforamaPT, special_cat, category) : ''

      // conforama ES 
      const sendCostNoTaxConforamaES = sendCostsConforamaES - (sendCostsConforamaES * (fm3 / 100))
      const liquidPriceConforamaES = costPrice + sendCostNoTaxConforamaES
      const finalPvpConforamaES = Number((liquidPriceConforamaES / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueConforamaES = finalPvpConforamaES * (fm3 / 100)
      const marketplaceComissionConforamaES = Number((finalPvpConforamaES * (fm2 / 100)).toFixed(2))
      const profitConforamaES = Number((finalPvpConforamaES * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('CONFORAMA - ES', ean, sendCostsConforamaES, sendCostNoTaxConforamaES, costPrice, fm1, liquidPriceConforamaES, fm2, fm3, finalPvpConforamaES, realTaxValueConforamaES, marketplaceComissionConforamaES, profitConforamaES, special_cat, category) : ''

      // empik PL
      const sendCostNoTaxEmpikPL = sendCostsEmpikPL - (sendCostsEmpikPL * (fm3 / 100))
      const liquidPriceEmpikPL = costPrice + sendCostNoTaxEmpikPL
      const finalPvpEmpikPL = Number((liquidPriceEmpikPL / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueEmpikPL = finalPvpEmpikPL * (fm3 / 100)
      const marketplaceComissionEmpikPL = Number((finalPvpEmpikPL * (fm2 / 100)).toFixed(2))
      const profitEmpikPL = Number((finalPvpEmpikPL * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('EMPIK - PL', ean, sendCostsEmpikPL, sendCostNoTaxEmpikPL, costPrice, fm1, liquidPriceEmpikPL, fm2, fm3, finalPvpEmpikPL, realTaxValueEmpikPL, marketplaceComissionEmpikPL, profitEmpikPL, special_cat, category) : ''

      // fnac ES
      const sendCostNoTaxFnacES = sendCostsFnacES - (sendCostsFnacES * (fm3 / 100))
      const liquidPriceFnacES = costPrice + sendCostNoTaxFnacES
      const finalPvpFnacES = Number((liquidPriceFnacES / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueFnacES = finalPvpFnacES * (fm3 / 100)
      const marketplaceComissionFnacES = Number((finalPvpFnacES * (fm2 / 100)).toFixed(2))
      const profitFnacES = Number((finalPvpFnacES * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('FNAC - ES', ean, sendCostsFnacES, sendCostNoTaxFnacES, costPrice, fm1, liquidPriceFnacES, fm2, fm3, finalPvpFnacES, realTaxValueFnacES, marketplaceComissionFnacES, profitFnacES, special_cat, category) : ''

      // fnac PT
      const sendCostNoTaxFnacPT = sendCostsFnacPT - (sendCostsFnacPT * (fm3 / 100))
      const liquidPriceFnacPT = costPrice + sendCostNoTaxFnacPT
      const finalPvpFnacPT = Number((liquidPriceFnacPT / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueFnacPT = finalPvpFnacPT * (fm3 / 100)
      const marketplaceComissionFnacPT = Number((finalPvpFnacPT * (fm2 / 100)).toFixed(2))
      const profitFnacPT = Number((finalPvpFnacPT * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('FNAC - PT', ean, sendCostsFnacPT, sendCostNoTaxFnacPT, costPrice, fm1, liquidPriceFnacPT, fm2, fm3, finalPvpFnacPT, realTaxValueFnacPT, marketplaceComissionFnacPT, profitFnacPT, special_cat, category) : ''

      // fnac FR
      const sendCostNoTaxFnacFR = sendCostsFnacFR - (sendCostsFnacFR * (fm3 / 100))
      const liquidPriceFnacFR = costPrice + sendCostNoTaxFnacFR
      const finalPvpFnacFR = Number((liquidPriceFnacFR / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueFnacFR = finalPvpFnacFR * (fm3 / 100)
      const marketplaceComissionFnacFR = Number((finalPvpFnacFR * (fm2 / 100)).toFixed(2))
      const profitFnacFR = Number((finalPvpFnacFR * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('FNAC - FR', ean, sendCostsFnacFR, sendCostNoTaxFnacFR, costPrice, fm1, liquidPriceFnacFR, fm2, fm3, finalPvpFnacFR, realTaxValueFnacFR, marketplaceComissionFnacFR, profitFnacFR, special_cat, category) : ''

      // carrefour ES
      const sendCostNoTaxCarrefourES = sendCostsCarrefourES - (sendCostsCarrefourES * (fm3 / 100))
      const liquidPriceCarrefourES = costPrice + sendCostNoTaxCarrefourES
      const finalPvpCarrefourES = Number((liquidPriceCarrefourES / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueCarrefourES = finalPvpCarrefourES * (fm3 / 100)
      const marketplaceComissionCarrefourES = Number((finalPvpCarrefourES * (fm2 / 100)).toFixed(2))
      const profitCarrefourES = Number((finalPvpCarrefourES * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('CARREFOUR - ES', ean, sendCostsCarrefourES, sendCostNoTaxCarrefourES, costPrice, m1, liquidPriceCarrefourES, m2, m3, finalPvpCarrefourES, realTaxValueCarrefourES, marketplaceComissionCarrefourES, profitCarrefourES, special_cat, category) : ''

      // carrefour FR
      const sendCostNoTaxCarrefourFR = sendCostsCarrefourFR - (sendCostsCarrefourFR * (fm3 / 100))
      const liquidPriceCarrefourFR = costPrice + sendCostNoTaxCarrefourFR
      const finalPvpCarrefourFR = Number((liquidPriceCarrefourFR / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueCarrefourFR = finalPvpCarrefourFR * (fm3 / 100)
      const marketplaceComissionCarrefourFR = Number((finalPvpCarrefourFR * (fm2 / 100)).toFixed(2))
      const profitCarrefourFR = Number((finalPvpCarrefourFR * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('CARREFOUR - FR', ean, sendCostsCarrefourFR, sendCostNoTaxCarrefourFR, costPrice, m1, liquidPriceCarrefourFR, m2, m3, finalPvpCarrefourFR, realTaxValueCarrefourFR, marketplaceComissionCarrefourFR, profitCarrefourFR, special_cat, category) : ''


      // pccomp ES
      const sendCostNoTaxManoamanoES = sendCostsManoamanoES - (sendCostsManoamanoES * (fm3 / 100))
      const liquidPriceManoamanoES = costPrice + sendCostNoTaxManoamanoES
      const finalPvpManoamanoES = Number((liquidPriceManoamanoES / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueManoamanoES = finalPvpManoamanoES * (fm3 / 100)
      const marketplaceComissionManoamanoES = Number((finalPvpManoamanoES * (fm2 / 100)).toFixed(2))
      const profitManoamanoES = Number((finalPvpManoamanoES * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('MANOAMANO - ES', ean, sendCostsManoamanoES, sendCostNoTaxManoamanoES, costPrice, fm1, liquidPriceManoamanoES, fm2, fm3, finalPvpManoamanoES, realTaxValueManoamanoES, marketplaceComissionManoamanoES, profitManoamanoES, special_cat, category) : ''

      // pccomp ES
      const sendCostNoTaxPccompES = sendCostsPccompES - (sendCostsPccompES * (fm3 / 100))
      const liquidPricePccompES = costPrice + sendCostNoTaxPccompES
      const finalPvpPccompES = Number((liquidPricePccompES / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValuePccompES = finalPvpPccompES * (fm3 / 100)
      const marketplaceComissionPccompES = Number((finalPvpPccompES * (fm2 / 100)).toFixed(2))
      const profitPccompES = Number((finalPvpPccompES * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('PC COMPONENTES - ES', ean, sendCostsPccompES, sendCostNoTaxPccompES, costPrice, fm1, liquidPricePccompES, fm2, fm3, finalPvpPccompES, realTaxValuePccompES, marketplaceComissionPccompES, profitPccompES, special_cat, category) : ''

      // pccomp PT
      const sendCostNoTaxPccompPT = sendCostsPccompPT - (sendCostsPccompPT * (fm3 / 100))
      const liquidPricePccompPT = costPrice + sendCostNoTaxPccompPT
      const finalPvpPccompPT = Number((liquidPricePccompPT / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValuePccompPT = finalPvpPccompPT * (fm3 / 100)
      const marketplaceComissionPccompPT = Number((finalPvpPccompPT * (fm2 / 100)).toFixed(2))
      const profitPccompPT = Number((finalPvpPccompPT * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('PC COMPONENTES - PT', ean, sendCostsPccompPT, sendCostNoTaxPccompPT, costPrice, fm1, liquidPricePccompPT, fm2, fm3, finalPvpPccompPT, realTaxValuePccompPT, marketplaceComissionPccompPT, profitPccompPT, special_cat, category) : ''

      // pccomp IT
      const sendCostNoTaxPccompIT = sendCostsPccompIT - (sendCostsPccompIT * (fm3 / 100))
      const liquidPricePccompIT = costPrice + sendCostNoTaxPccompIT
      const finalPvpPccompIT = Number((liquidPricePccompIT / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValuePccompIT = finalPvpPccompIT * (fm3 / 100)
      const marketplaceComissionPccompIT = Number((finalPvpPccompIT * (fm2 / 100)).toFixed(2))
      const profitPccompIT = Number((finalPvpPccompIT * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('PC COMPONENTES - IT', ean, sendCostsPccompIT, sendCostNoTaxPccompIT, costPrice, fm1, liquidPricePccompIT, fm2, fm3, finalPvpPccompIT, realTaxValuePccompIT, marketplaceComissionPccompIT, profitPccompIT, special_cat, category) : ''

      // pccomp FR
      const sendCostNoTaxPccompFR = sendCostsPccompFR - (sendCostsPccompFR * (fm3 / 100))
      const liquidPricePccompFR = costPrice + sendCostNoTaxPccompFR
      const finalPvpPccompFR = Number((liquidPricePccompFR / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValuePccompFR = finalPvpPccompFR * (fm3 / 100)
      const marketplaceComissionPccompFR = Number((finalPvpPccompFR * (fm2 / 100)).toFixed(2))
      const profitPccompFR = Number((finalPvpPccompFR * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('PC COMPONENTES - FR', ean, sendCostsPccompFR, sendCostNoTaxPccompFR, costPrice, fm1, liquidPricePccompFR, fm2, fm3, finalPvpPccompFR, realTaxValuePccompFR, marketplaceComissionPccompFR, profitPccompFR, special_cat, category) : ''

      // metro ES
      const sendCostNoTaxMakroES = sendCostsMakroES - (sendCostsMakroES * (fm3 / 100))
      const liquidPriceMakroES = costPrice + sendCostNoTaxMakroES
      const finalPvpMakroES = Number((liquidPriceMakroES / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueMakroES = finalPvpMakroES * (fm3 / 100)
      const marketplaceComissionMakroES = Number((finalPvpMakroES * (fm2 / 100)).toFixed(2))
      const profitMakroES = Number((finalPvpMakroES * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('MAKRO - ES', ean, sendCostsMakroES, sendCostNoTaxMakroES, costPrice, fm1, liquidPriceMakroES, fm2, fm3, finalPvpMakroES, realTaxValueMakroES, marketplaceComissionMakroES, profitMakroES, special_cat, category) : ''

      // metro PT
      const sendCostNoTaxMakroPT = sendCostsMakroPT - (sendCostsMakroPT * (fm3 / 100))
      const liquidPriceMakroPT = costPrice + sendCostNoTaxMakroPT
      const finalPvpMakroPT = Number((liquidPriceMakroPT / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueMakroPT = finalPvpMakroPT * (fm3 / 100)
      const marketplaceComissionMakroPT = Number((finalPvpMakroPT * (fm2 / 100)).toFixed(2))
      const profitMakroPT = Number((finalPvpMakroPT * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('MAKRO - PT', ean, sendCostsMakroPT, sendCostNoTaxMakroPT, costPrice, fm1, liquidPriceMakroPT, fm2, fm3, finalPvpMakroPT, realTaxValueMakroPT, marketplaceComissionMakroPT, profitMakroPT, special_cat, category) : ''

      // worten ES
      const sendCostNoTaxWortenES = sendCostsWortenES - (sendCostsWortenES * (fm3 / 100))
      const liquidPriceWortenES = costPrice + sendCostNoTaxWortenES
      const finalPvpWortenES = Number((liquidPriceWortenES / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueWortenES = finalPvpWortenES * (fm3 / 100)
      const marketplaceComissionWortenES = Number((finalPvpWortenES * (fm2 / 100)).toFixed(2))
      const profitWortenES = Number((finalPvpWortenES * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('WORTEN - ES', ean, sendCostsWortenES, sendCostNoTaxWortenES, costPrice, fm1, liquidPriceWortenES, fm2, fm3, finalPvpWortenES, realTaxValueWortenES, marketplaceComissionWortenES, profitWortenES, special_cat, category) : ''

      // worten PT
      const sendCostNoTaxWortenPT = sendCostsWortenPT - (sendCostsWortenPT * (fm3 / 100))
      const liquidPriceWortenPT = costPrice + sendCostNoTaxWortenPT
      const finalPvpWortenPT = Number((liquidPriceWortenPT / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueWortenPT = finalPvpWortenPT * (fm3 / 100)
      const marketplaceComissionWortenPT = Number((finalPvpWortenPT * (fm2 / 100)).toFixed(2))
      const profitWortenPT = Number((finalPvpWortenPT * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('WORTEN - PT', ean, sendCostsWortenPT, sendCostNoTaxWortenPT, costPrice, fm1, liquidPriceWortenPT, fm2, fm3, finalPvpWortenPT, realTaxValueWortenPT, marketplaceComissionWortenPT, profitWortenPT, special_cat, category) : ''

      // mediamarkt ES
      const sendCostNoTaxMediamarktES = sendCostsMediamarktES - (sendCostsMediamarktES * (fm3 / 100))
      const liquidPriceMediamarktES = costPrice + sendCostNoTaxMediamarktES
      const finalPvpMediamarktES = Number((liquidPriceMediamarktES / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueMediamarktES = finalPvpMediamarktES * (fm3 / 100)
      const marketplaceComissionMediamarktES = Number((finalPvpMediamarktES * (fm2 / 100)).toFixed(2))
      const profitMediamarktES = Number((finalPvpMediamarktES * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('MEDIAMARKT - ES', ean, sendCostsMediamarktES, sendCostNoTaxMediamarktES, costPrice, fm1, liquidPriceMediamarktES, fm2, fm3, finalPvpMediamarktES, realTaxValueMediamarktES, marketplaceComissionMediamarktES, profitMediamarktES, special_cat, category) : ''

      // mediamarkt DE
      const sendCostNoTaxMediamarktDE = sendCostsMediamarktDE - (sendCostsMediamarktDE * (fm3 / 100))
      const liquidPriceMediamarktDE = costPrice + sendCostNoTaxMediamarktDE
      const finalPvpMediamarktDE = Number((liquidPriceMediamarktDE / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueMediamarktDE = finalPvpMediamarktDE * (fm3 / 100)
      const marketplaceComissionMediamarktDE = Number((finalPvpMediamarktDE * (fm2 / 100)).toFixed(2))
      const profitMediamarktDE = Number((finalPvpMediamarktDE * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('MEDIAMARKT - DE', ean, sendCostsMediamarktDE, sendCostNoTaxMediamarktDE, costPrice, fm1, liquidPriceMediamarktDE, fm2, fm3, finalPvpMediamarktDE, realTaxValueMediamarktDE, marketplaceComissionMediamarktDE, profitMediamarktDE, special_cat, category) : ''

      // kuanto kusta PT
      const sendCostNoTaxKuantokustaPT = sendCostsKuantokustaPT - (sendCostsKuantokustaPT * (fm3 / 100))
      const liquidPriceKuantokustaPT = costPrice + sendCostNoTaxKuantokustaPT
      const finalPvpKuantokustaPT = Number((liquidPriceKuantokustaPT / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueKuantokustaPT = finalPvpKuantokustaPT * (fm3 / 100)
      const marketplaceComissionKuantokustaPT = Number((finalPvpKuantokustaPT * (fm2 / 100)).toFixed(2))
      const profitKuantokustaPT = Number((finalPvpKuantokustaPT * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('KUANTOKUSTA - PT', ean, sendCostsKuantokustaPT, sendCostNoTaxKuantokustaPT, costPrice, fm1, liquidPriceKuantokustaPT, fm2, fm3, finalPvpKuantokustaPT, realTaxValueKuantokustaPT, marketplaceComissionKuantokustaPT, profitKuantokustaPT, special_cat, category) : ''

      // cdiscount FR
      const sendCostNoTaxCdiscountFR = sendCostsCdiscountFR - (sendCostsCdiscountFR * (fm3 / 100))
      const liquidPriceCdiscountFR = costPrice + sendCostNoTaxCdiscountFR
      const finalPvpCdiscountFR = Number((liquidPriceCdiscountFR / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueCdiscountFR = finalPvpCdiscountFR * (fm3 / 100)
      const marketplaceComissionCdiscountFR = Number((finalPvpCdiscountFR * (fm2 / 100)).toFixed(2))
      const profitCdiscountFR = Number((finalPvpCdiscountFR * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('CDISCOUNT - ES', ean, sendCostsCdiscountFR, sendCostNoTaxCdiscountFR, costPrice, fm1, liquidPriceCdiscountFR, fm2, fm3, finalPvpCdiscountFR, realTaxValueCdiscountFR, marketplaceComissionCdiscountFR, profitCdiscountFR, special_cat, category) : ''

      // miravia ES 
      const sendCostNoTaxMiraviaES = sendCostsMiraviaES - (sendCostsMiraviaES * (fm3 / 100))
      const liquidPriceMiraviaES = costPrice + sendCostNoTaxMiraviaES
      const finalPvpMiraviaES = Number((liquidPriceMiraviaES / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueMiraviaES = finalPvpMiraviaES * (fm3 / 100)
      const marketplaceComissionMiraviaES = Number((finalPvpMiraviaES * (fm2 / 100)).toFixed(2))
      const profitMiraviaES = Number((finalPvpMiraviaES * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('MIRAVIA - ES', ean, sendCostsMiraviaES, sendCostNoTaxMiraviaES, costPrice, fm1, liquidPriceMiraviaES, fm2, fm3, finalPvpMiraviaES, realTaxValueMiraviaES, marketplaceComissionMiraviaES, profitMiraviaES, special_cat, category) : ''

      // bigbang SI
      const sendCostNoTaxBigbangSI = sendCostsBigbangSI - (sendCostsBigbangSI * (fm3 / 100))
      const liquidPriceBigbangSI = costPrice + sendCostNoTaxBigbangSI
      const finalPvpBigbangSI = Number((liquidPriceBigbangSI / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueBigbangSI = finalPvpBigbangSI * (fm3 / 100)
      const marketplaceComissionBigbangSI = Number((finalPvpBigbangSI * (fm2 / 100)).toFixed(2))
      const profitBigbangSI = Number((finalPvpBigbangSI * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('BIGBANG - SI', ean, sendCostsBigbangSI, sendCostNoTaxBigbangSI, costPrice, fm1, liquidPriceBigbangSI, fm2, fm3, finalPvpBigbangSI, realTaxValueBigbangSI, marketplaceComissionBigbangSI, profitBigbangSI, special_cat, category) : ''

      // bulevip ES
      const sendCostNoTaxBulevipES = sendCostsBulevipES - (sendCostsBulevipES * (fm3 / 100))
      const liquidPriceBulevipES = costPrice + sendCostNoTaxBulevipES
      const finalPvpBulevipES = Number((liquidPriceBulevipES / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueBulevipES = finalPvpBulevipES * (fm3 / 100)
      const marketplaceComissionBulevipES = Number((finalPvpBulevipES * (fm2 / 100)).toFixed(2))
      const profitBulevipES = Number((finalPvpBulevipES * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('BULEVIP - ES', ean, sendCostsBulevipES, sendCostNoTaxBulevipES, costPrice, fm1, liquidPriceBulevipES, fm2, fm3, finalPvpBulevipES, realTaxValueBulevipES, marketplaceComissionBulevipES, profitBulevipES, special_cat, category) : ''

      // elcorteingles ES
      const sendCostNoTaxElcorteinglesES = sendCostsElcorteinglesES - (sendCostsElcorteinglesES * (fm3 / 100))
      const liquidPriceElcorteinglesES = costPrice + sendCostNoTaxElcorteinglesES
      const finalPvpElcorteinglesES = Number((liquidPriceElcorteinglesES / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueElcorteinglesES = finalPvpElcorteinglesES * (fm3 / 100)
      const marketplaceComissionElcorteinglesES = Number((finalPvpElcorteinglesES * (fm2 / 100)).toFixed(2))
      const profitElcorteinglesES = Number((finalPvpElcorteinglesES * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('EL CORTE INGLÊS - ES', ean, sendCostsElcorteinglesES, sendCostNoTaxElcorteinglesES, costPrice, fm1, liquidPriceElcorteinglesES, fm2, fm3, finalPvpElcorteinglesES, realTaxValueElcorteinglesES, marketplaceComissionElcorteinglesES, profitElcorteinglesES, special_cat, category) : ''

      // eleclerc FR
      const sendCostNoTaxEleclercFR = sendCostsEleclercFR - (sendCostsEleclercFR * (fm3 / 100))
      const liquidPriceEleclercFR = costPrice + sendCostNoTaxEleclercFR
      const finalPvpEleclercFR = Number((liquidPriceEleclercFR / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueEleclercFR = finalPvpEleclercFR * (fm3 / 100)
      const marketplaceComissionEleclercFR = Number((finalPvpEleclercFR * (fm2 / 100)).toFixed(2))
      const profitEleclercFR = Number((finalPvpEleclercFR * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('ELECLERC - FR', ean, sendCostsEleclercFR, sendCostNoTaxEleclercFR, costPrice, fm1, liquidPriceEleclercFR, fm2, fm3, finalPvpEleclercFR, realTaxValueEleclercFR, marketplaceComissionEleclercFR, profitEleclercFR, special_cat, category) : ''

      // eprice IT
      const sendCostNoTaxEpriceIT = sendCostsEpriceIT - (sendCostsEpriceIT * (fm3 / 100))
      const liquidPriceEpriceIT = costPrice + sendCostNoTaxEpriceIT
      const finalPvpEpriceIT = Number((liquidPriceEpriceIT / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueEpriceIT = finalPvpEpriceIT * (fm3 / 100)
      const marketplaceComissionEpriceIT = Number((finalPvpEpriceIT * (fm2 / 100)).toFixed(2))
      const profitEpriceIT = Number((finalPvpEpriceIT * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('EPRICE - IT', ean, sendCostsEpriceIT, sendCostNoTaxEpriceIT, costPrice, fm1, liquidPriceEpriceIT, fm2, fm3, finalPvpEpriceIT, realTaxValueEpriceIT, marketplaceComissionEpriceIT, profitEpriceIT, special_cat, category) : ''

      // leroy merlin ES
      const sendCostNoTaxLeroymerlinES = sendCostsLeroymerlinES - (sendCostsLeroymerlinES * (fm3 / 100))
      const liquidPriceLeroymerlinES = costPrice + sendCostNoTaxLeroymerlinES
      const finalPvpLeroymerlinES = Number((liquidPriceLeroymerlinES / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueLeroymerlinES = finalPvpLeroymerlinES * (fm3 / 100)
      const marketplaceComissionLeroymerlinES = Number((finalPvpLeroymerlinES * (fm2 / 100)).toFixed(2))
      const profitLeroymerlinES = Number((finalPvpLeroymerlinES * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('LEROY MERLIN - ES', ean, sendCostsLeroymerlinES, sendCostNoTaxLeroymerlinES, costPrice, fm1, liquidPriceLeroymerlinES, fm2, fm3, finalPvpLeroymerlinES, realTaxValueLeroymerlinES, marketplaceComissionLeroymerlinES, profitLeroymerlinES, special_cat, category) : ''

      // leroy merlin PT
      const sendCostNoTaxLeroymerlinPT = sendCostsLeroymerlinPT - (sendCostsLeroymerlinPT * (fm3 / 100))
      const liquidPriceLeroymerlinPT = costPrice + sendCostNoTaxLeroymerlinPT
      const finalPvpLeroymerlinPT = Number((liquidPriceLeroymerlinPT / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueLeroymerlinPT = finalPvpLeroymerlinPT * (fm3 / 100)
      const marketplaceComissionLeroymerlinPT = Number((finalPvpLeroymerlinPT * (fm2 / 100)).toFixed(2))
      const profitLeroymerlinPT = Number((finalPvpLeroymerlinPT * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('LEROY MERLIN - PT', ean, sendCostsLeroymerlinPT, sendCostNoTaxLeroymerlinPT, costPrice, fm1, liquidPriceLeroymerlinPT, fm2, fm3, finalPvpLeroymerlinPT, realTaxValueLeroymerlinPT, marketplaceComissionLeroymerlinPT, profitLeroymerlinPT, special_cat, category) : ''

      // leroy merlin FR
      const sendCostNoTaxLeroymerlinFR = sendCostsLeroymerlinFR - (sendCostsLeroymerlinFR * (fm3 / 100))
      const liquidPriceLeroymerlinFR = costPrice + sendCostNoTaxLeroymerlinFR
      const finalPvpLeroymerlinFR = Number((liquidPriceLeroymerlinFR / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueLeroymerlinFR = finalPvpLeroymerlinFR * (fm3 / 100)
      const marketplaceComissionLeroymerlinFR = Number((finalPvpLeroymerlinFR * (fm2 / 100)).toFixed(2))
      const profitLeroymerlinFR = Number((finalPvpLeroymerlinFR * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('LEROY MERLIN - FR', ean, sendCostsLeroymerlinFR, sendCostNoTaxLeroymerlinFR, costPrice, fm1, liquidPriceLeroymerlinFR, fm2, fm3, finalPvpLeroymerlinFR, realTaxValueLeroymerlinFR, marketplaceComissionLeroymerlinFR, profitLeroymerlinFR, special_cat, category) : ''

      // leroy merlin IT
      const sendCostNoTaxLeroymerlinIT = sendCostsLeroymerlinIT - (sendCostsLeroymerlinIT * (fm3 / 100))
      const liquidPriceLeroymerlinIT = costPrice + sendCostNoTaxLeroymerlinIT
      const finalPvpLeroymerlinIT = Number((liquidPriceLeroymerlinIT / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueLeroymerlinIT = finalPvpLeroymerlinIT * (fm3 / 100)
      const marketplaceComissionLeroymerlinIT = Number((finalPvpLeroymerlinIT * (fm2 / 100)).toFixed(2))
      const profitLeroymerlinIT = Number((finalPvpLeroymerlinIT * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('LEROY MERLIN - IT', ean, sendCostsLeroymerlinIT, sendCostNoTaxLeroymerlinIT, costPrice, fm1, liquidPriceLeroymerlinIT, fm2, fm3, finalPvpLeroymerlinIT, realTaxValueLeroymerlinIT, marketplaceComissionLeroymerlinIT, profitLeroymerlinIT, special_cat, category) : ''

      // leroy merlin PL
      const sendCostNoTaxLeroymerlinPL = sendCostsLeroymerlinPL - (sendCostsLeroymerlinPL * (fm3 / 100))
      const liquidPriceLeroymerlinPL = costPrice + sendCostNoTaxLeroymerlinPL
      const finalPvpLeroymerlinPL = Number((liquidPriceLeroymerlinPL / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueLeroymerlinPL = finalPvpLeroymerlinPL * (fm3 / 100)
      const marketplaceComissionLeroymerlinPL = Number((finalPvpLeroymerlinPL * (fm2 / 100)).toFixed(2))
      const profitLeroymerlinPL = Number((finalPvpLeroymerlinPL * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('LEROY MERLIN - PL', ean, sendCostsLeroymerlinPL, sendCostNoTaxLeroymerlinPL, costPrice, fm1, liquidPriceLeroymerlinPL, fm2, fm3, finalPvpLeroymerlinPL, realTaxValueLeroymerlinPL, marketplaceComissionLeroymerlinPL, profitLeroymerlinPL, special_cat, category) : ''

      // perfumes club PT
      const sendCostNoTaxPerfumesclubPT = sendCostsPerfumesclubPT - (sendCostsPerfumesclubPT * (fm3 / 100))
      const liquidPricePerfumesclubPT = costPrice + sendCostNoTaxPerfumesclubPT
      const finalPvpPerfumesclubPT = Number((liquidPricePerfumesclubPT / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValuePerfumesclubPT = finalPvpPerfumesclubPT * (fm3 / 100)
      const marketplaceComissionPerfumesclubPT = Number((finalPvpPerfumesclubPT * (fm2 / 100)).toFixed(2))
      const profitPerfumesclubPT = Number((finalPvpPerfumesclubPT * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('PERFUMES CLUB - PT', ean, sendCostsPerfumesclubPT, sendCostNoTaxPerfumesclubPT, costPrice, fm1, liquidPricePerfumesclubPT, fm2, fm3, finalPvpPerfumesclubPT, realTaxValuePerfumesclubPT, marketplaceComissionPerfumesclubPT, profitPerfumesclubPT, special_cat, category) : ''

      // phone house ES
      const sendCostNoTaxPhonehouseES = sendCostsPhonehouseES - (sendCostsPhonehouseES * (fm3 / 100))
      const liquidPricePhonehouseES = costPrice + sendCostNoTaxPhonehouseES
      const finalPvpPhonehouseES = Number((liquidPricePhonehouseES / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValuePhonehouseES = finalPvpPhonehouseES * (fm3 / 100)
      const marketplaceComissionPhonehouseES = Number((finalPvpPhonehouseES * (fm2 / 100)).toFixed(2))
      const profitPhonehouseES = Number((finalPvpPhonehouseES * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('PHONE HOUSE - ES', ean, sendCostsPhonehouseES, sendCostNoTaxPhonehouseES, costPrice, fm1, liquidPricePhonehouseES, fm2, fm3, finalPvpPhonehouseES, realTaxValuePhonehouseES, marketplaceComissionPhonehouseES, profitPhonehouseES, special_cat, category) : ''

      // pixmania ES
      const sendCostNoTaxPixmaniaES = sendCostsPixmaniaES - (sendCostsPixmaniaES * (fm3 / 100))
      const liquidPricePixmaniaES = costPrice + sendCostNoTaxPixmaniaES
      const finalPvpPixmaniaES = Number((liquidPricePixmaniaES / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValuePixmaniaES = finalPvpPixmaniaES * (fm3 / 100)
      const marketplaceComissionPixmaniaES = Number((finalPvpPixmaniaES * (fm2 / 100)).toFixed(2))
      const profitPixmaniaES = Number((finalPvpPixmaniaES * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('PIXMANIA - ES', ean, sendCostsPixmaniaES, sendCostNoTaxPixmaniaES, costPrice, fm1, liquidPricePixmaniaES, fm2, fm3, finalPvpPixmaniaES, realTaxValuePixmaniaES, marketplaceComissionPixmaniaES, profitPixmaniaES, special_cat, category) : ''

      // pixmania FR
      const sendCostNoTaxPixmaniaFR = sendCostsPixmaniaFR - (sendCostsPixmaniaFR * (fm3 / 100))
      const liquidPricePixmaniaFR = costPrice + sendCostNoTaxPixmaniaFR
      const finalPvpPixmaniaFR = Number((liquidPricePixmaniaFR / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValuePixmaniaFR = finalPvpPixmaniaFR * (fm3 / 100)
      const marketplaceComissionPixmaniaFR = Number((finalPvpPixmaniaFR * (fm2 / 100)).toFixed(2))
      const profitPixmaniaFR = Number((finalPvpPixmaniaFR * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('PIXMANIA - FR', ean, sendCostsPixmaniaFR, sendCostNoTaxPixmaniaFR, costPrice, fm1, liquidPricePixmaniaFR, fm2, fm3, finalPvpPixmaniaFR, realTaxValuePixmaniaFR, marketplaceComissionPixmaniaFR, profitPixmaniaFR, special_cat, category) : ''

      // rueducomerce FR
      const sendCostNoTaxRueducommerceFR = sendCostsRueducommerceFR - (sendCostsRueducommerceFR * (fm3 / 100))
      const liquidPriceRueducommerceFR = costPrice + sendCostNoTaxRueducommerceFR
      const finalPvpRueducommerceFR = Number((liquidPriceRueducommerceFR / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueRueducommerceFR = finalPvpRueducommerceFR * (fm3 / 100)
      const marketplaceComissionRueducommerceFR = Number((finalPvpRueducommerceFR * (fm2 / 100)).toFixed(2))
      const profitRueducommerceFR = Number((finalPvpRueducommerceFR * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('RUEDUOMMERCE - FR', ean, sendCostsRueducommerceFR, sendCostNoTaxRueducommerceFR, costPrice, fm1, liquidPriceRueducommerceFR, fm2, fm3, finalPvpRueducommerceFR, realTaxValueRueducommerceFR, marketplaceComissionRueducommerceFR, profitRueducommerceFR, special_cat, category) : ''

      // clubefashion FR
      const sendCostNoTaxClubefashionPT = sendCostsClubefashionPT - (sendCostsClubefashionPT * (fm3 / 100))
      const liquidPriceClubefashionPT = costPrice + sendCostNoTaxClubefashionPT
      const finalPvpClubefashionPT = Number((liquidPriceClubefashionPT / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueClubefashionPT = finalPvpClubefashionPT * (fm3 / 100)
      const marketplaceComissionClubefashionPT = Number((finalPvpClubefashionPT * (fm2 / 100)).toFixed(2))
      const profitClubefashionPT = Number((finalPvpClubefashionPT * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('CLUBEFASHION - PT', ean, sendCostsClubefashionPT, sendCostNoTaxClubefashionPT, costPrice, fm1, liquidPriceClubefashionPT, fm2, fm3, finalPvpClubefashionPT, realTaxValueClubefashionPT, marketplaceComissionClubefashionPT, profitClubefashionPT, special_cat, category) : ''

      // planetahuerto ES
      const sendCostNoTaxPlanetahuertoES = sendCostsPlanetahuertoES - (sendCostsPlanetahuertoES * (fm3 / 100))
      const liquidPricePlanetahuertoES = costPrice + sendCostNoTaxPlanetahuertoES
      const finalPvpPlanetahuertoES = Number((liquidPricePlanetahuertoES / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValuePlanetahuertoES = finalPvpPlanetahuertoES * (fm3 / 100)
      const marketplaceComissionPlanetahuertoES = Number((finalPvpPlanetahuertoES * (fm2 / 100)).toFixed(2))
      const profitPlanetahuertoES = Number((finalPvpPlanetahuertoES * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('PLANETAHUERTO - ES', ean, sendCostsPlanetahuertoES, sendCostNoTaxPlanetahuertoES, costPrice, fm1, liquidPricePlanetahuertoES, fm2, fm3, finalPvpPlanetahuertoES, realTaxValuePlanetahuertoES, marketplaceComissionPlanetahuertoES, profitPlanetahuertoES, special_cat, category) : ''

      // vente unique ES
      const sendCostNoTaxVenteuniqueES = sendCostsVenteuniqueES - (sendCostsVenteuniqueES * (fm3 / 100))
      const liquidPriceVenteuniqueES = costPrice + sendCostNoTaxVenteuniqueES
      const finalPvpVenteuniqueES = Number((liquidPriceVenteuniqueES / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueVenteuniqueES = finalPvpVenteuniqueES * (fm3 / 100)
      const marketplaceComissionVenteuniqueES = Number((finalPvpVenteuniqueES * (fm2 / 100)).toFixed(2))
      const profitVenteuniqueES = Number((finalPvpVenteuniqueES * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('VENTEUNIQUE - ES', ean, sendCostsVenteuniqueES, sendCostNoTaxVenteuniqueES, costPrice, fm1, liquidPriceVenteuniqueES, fm2, fm3, finalPvpVenteuniqueES, realTaxValueVenteuniqueES, marketplaceComissionVenteuniqueES, profitVenteuniqueES, special_cat, category) : ''

      // vente unique BE
      const sendCostNoTaxVenteuniqueBE = sendCostsVenteuniqueBE - (sendCostsVenteuniqueBE * (fm3 / 100))
      const liquidPriceVenteuniqueBE = costPrice + sendCostNoTaxVenteuniqueBE
      const finalPvpVenteuniqueBE = Number((liquidPriceVenteuniqueBE / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueVenteuniqueBE = finalPvpVenteuniqueBE * (fm3 / 100)
      const marketplaceComissionVenteuniqueBE = Number((finalPvpVenteuniqueBE * (fm2 / 100)).toFixed(2))
      const profitVenteuniqueBE = Number((finalPvpVenteuniqueBE * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('VENTEUNIQUE - BE', ean, sendCostsVenteuniqueBE, sendCostNoTaxVenteuniqueBE, costPrice, fm1, liquidPriceVenteuniqueBE, fm2, fm3, finalPvpVenteuniqueBE, realTaxValueVenteuniqueBE, marketplaceComissionVenteuniqueBE, profitVenteuniqueBE, special_cat, category) : ''

      // vente unique DE
      const sendCostNoTaxVenteuniqueDE = sendCostsVenteuniqueDE - (sendCostsVenteuniqueDE * (fm3 / 100))
      const liquidPriceVenteuniqueDE = costPrice + sendCostNoTaxVenteuniqueDE
      const finalPvpVenteuniqueDE = Number((liquidPriceVenteuniqueDE / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueVenteuniqueDE = finalPvpVenteuniqueDE * (fm3 / 100)
      const marketplaceComissionVenteuniqueDE = Number((finalPvpVenteuniqueDE * (fm2 / 100)).toFixed(2))
      const profitVenteuniqueDE = Number((finalPvpVenteuniqueDE * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('VENTEUNIQUE - DE', ean, sendCostsVenteuniqueDE, sendCostNoTaxVenteuniqueDE, costPrice, fm1, liquidPriceVenteuniqueDE, fm2, fm3, finalPvpVenteuniqueDE, realTaxValueVenteuniqueDE, marketplaceComissionVenteuniqueDE, profitVenteuniqueDE, special_cat, category) : ''

      // vente unique FR
      const sendCostNoTaxVenteuniqueFR = sendCostsVenteuniqueFR - (sendCostsVenteuniqueFR * (fm3 / 100))
      const liquidPriceVenteuniqueFR = costPrice + sendCostNoTaxVenteuniqueFR
      const finalPvpVenteuniqueFR = Number((liquidPriceVenteuniqueFR / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueVenteuniqueFR = finalPvpVenteuniqueFR * (fm3 / 100)
      const marketplaceComissionVenteuniqueFR = Number((finalPvpVenteuniqueFR * (fm2 / 100)).toFixed(2))
      const profitVenteuniqueFR = Number((finalPvpVenteuniqueFR * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('VENTEUNIQUE - FR', ean, sendCostsVenteuniqueFR, sendCostNoTaxVenteuniqueFR, costPrice, fm1, liquidPriceVenteuniqueFR, fm2, fm3, finalPvpVenteuniqueFR, realTaxValueVenteuniqueFR, marketplaceComissionVenteuniqueFR, profitVenteuniqueFR, special_cat, category) : ''

      // vente unique IT
      const sendCostNoTaxVenteuniqueIT = sendCostsVenteuniqueIT - (sendCostsVenteuniqueIT * (fm3 / 100))
      const liquidPriceVenteuniqueIT = costPrice + sendCostNoTaxVenteuniqueIT
      const finalPvpVenteuniqueIT = Number((liquidPriceVenteuniqueIT / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueVenteuniqueIT = finalPvpVenteuniqueIT * (fm3 / 100)
      const marketplaceComissionVenteuniqueIT = Number((finalPvpVenteuniqueIT * (fm2 / 100)).toFixed(2))
      const profitVenteuniqueIT = Number((finalPvpVenteuniqueIT * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('VENTEUNIQUE - IT', ean, sendCostsVenteuniqueIT, sendCostNoTaxVenteuniqueIT, costPrice, fm1, liquidPriceVenteuniqueIT, fm2, fm3, finalPvpVenteuniqueIT, realTaxValueVenteuniqueIT, marketplaceComissionVenteuniqueIT, profitVenteuniqueIT, special_cat, category) : ''

      // vente unique NL
      const sendCostNoTaxVenteuniqueNL = sendCostsVenteuniqueNL - (sendCostsVenteuniqueNL * (fm3 / 100))
      const liquidPriceVenteuniqueNL = costPrice + sendCostNoTaxVenteuniqueNL
      const finalPvpVenteuniqueNL = Number((liquidPriceVenteuniqueNL / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueVenteuniqueNL = finalPvpVenteuniqueNL * (fm3 / 100)
      const marketplaceComissionVenteuniqueNL = Number((finalPvpVenteuniqueNL * (fm2 / 100)).toFixed(2))
      const profitVenteuniqueNL = Number((finalPvpVenteuniqueNL * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('VENTEUNIQUE - NL', ean, sendCostsVenteuniqueNL, sendCostNoTaxVenteuniqueNL, costPrice, fm1, liquidPriceVenteuniqueNL, fm2, fm3, finalPvpVenteuniqueNL, realTaxValueVenteuniqueNL, marketplaceComissionVenteuniqueNL, profitVenteuniqueNL, special_cat, category) : ''

      // vente unique PT
      const sendCostNoTaxVenteuniquePT = sendCostsVenteuniquePT - (sendCostsVenteuniquePT * (fm3 / 100))
      const liquidPriceVenteuniquePT = costPrice + sendCostNoTaxVenteuniquePT
      const finalPvpVenteuniquePT = Number((liquidPriceVenteuniquePT / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueVenteuniquePT = finalPvpVenteuniquePT * (fm3 / 100)
      const marketplaceComissionVenteuniquePT = Number((finalPvpVenteuniquePT * (fm2 / 100)).toFixed(2))
      const profitVenteuniquePT = Number((finalPvpVenteuniquePT * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('VENTEUNIQUE - PT', ean, sendCostsVenteuniquePT, sendCostNoTaxVenteuniquePT, costPrice, fm1, liquidPriceVenteuniquePT, fm2, fm3, finalPvpVenteuniquePT, realTaxValueVenteuniquePT, marketplaceComissionVenteuniquePT, profitVenteuniquePT, special_cat, category) : ''

      // kaufland DE
      const sendCostNoTaxKauflandDE = sendCostsKauflandDE - (sendCostsKauflandDE * (fm3 / 100))
      const liquidPriceKauflandDE = costPrice + sendCostNoTaxKauflandDE
      const finalPvpKauflandDE = Number((liquidPriceKauflandDE / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueKauflandDE = finalPvpKauflandDE * (fm3 / 100)
      const marketplaceComissionKauflandDE = Number((finalPvpKauflandDE * (fm2 / 100)).toFixed(2))
      const profitKauflandDE = Number((finalPvpKauflandDE * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('KAUFLAND - DE', ean, sendCostsKauflandDE, sendCostNoTaxKauflandDE, costPrice, fm1, liquidPriceKauflandDE, fm2, fm3, finalPvpKauflandDE, realTaxValueKauflandDE, marketplaceComissionKauflandDE, profitKauflandDE, special_cat, category) : ''

      // conrad DE
      const sendCostNoTaxConradDE = sendCostsConradDE - (sendCostsConradDE * (fm3 / 100))
      const liquidPriceConradDE = costPrice + sendCostNoTaxConradDE
      const finalPvpConradDE = Number((liquidPriceConradDE / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueConradDE = finalPvpConradDE * (fm3 / 100)
      const marketplaceComissionConradDE = Number((finalPvpConradDE * (fm2 / 100)).toFixed(2))
      const profitConradDE = Number((finalPvpConradDE * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('CONRAD - DE', ean, sendCostsConradDE, sendCostNoTaxConradDE, costPrice, fm1, liquidPriceConradDE, fm2, fm3, finalPvpConradDE, realTaxValueConradDE, marketplaceComissionConradDE, profitConradDE, special_cat, category) : ''

      // quirumedes ES
      const sendCostNoTaxQuirumedesES = sendCostsQuirumedesES - (sendCostsQuirumedesES * (fm3 / 100))
      const liquidPriceQuirumedesES = costPrice + sendCostNoTaxQuirumedesES
      const finalPvpQuirumedesES = Number((liquidPriceQuirumedesES / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueQuirumedesES = finalPvpQuirumedesES * (fm3 / 100)
      const marketplaceComissionQuirumedesES = Number((finalPvpQuirumedesES * (fm2 / 100)).toFixed(2))
      const profitQuirumedesES = Number((finalPvpQuirumedesES * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('QUIRUMEDES - ES', ean, sendCostsQuirumedesES, sendCostNoTaxQuirumedesES, costPrice, fm1, liquidPriceQuirumedesES, fm2, fm3, finalPvpQuirumedesES, realTaxValueQuirumedesES, marketplaceComissionQuirumedesES, profitQuirumedesES, special_cat, category) : ''

      // shopapotheke DE
      const sendCostNoTaxShopapothekeDE = sendCostsShopapothekeDE - (sendCostsShopapothekeDE * (fm3 / 100))
      const liquidPriceShopapothekeDE = costPrice + sendCostNoTaxShopapothekeDE
      const finalPvpShopapothekeDE = Number((liquidPriceShopapothekeDE / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueShopapothekeDE = finalPvpShopapothekeDE * (fm3 / 100)
      const marketplaceComissionShopapothekeDE = Number((finalPvpShopapothekeDE * (fm2 / 100)).toFixed(2))
      const profitShopapothekeDE = Number((finalPvpShopapothekeDE * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('SHOPAPOTHEKE - DE', ean, sendCostsShopapothekeDE, sendCostNoTaxShopapothekeDE, costPrice, fm1, liquidPriceShopapothekeDE, fm2, fm3, finalPvpShopapothekeDE, realTaxValueShopapothekeDE, marketplaceComissionShopapothekeDE, profitShopapothekeDE, special_cat, category) : ''

      // shopapotheke AT
      const sendCostNoTaxShopapothekeAT = sendCostsShopapothekeAT - (sendCostsShopapothekeAT * (fm3 / 100))
      const liquidPriceShopapothekeAT = costPrice + sendCostNoTaxShopapothekeAT
      const finalPvpShopapothekeAT = Number((liquidPriceShopapothekeAT / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueShopapothekeAT = finalPvpShopapothekeAT * (fm3 / 100)
      const marketplaceComissionShopapothekeAT = Number((finalPvpShopapothekeAT * (fm2 / 100)).toFixed(2))
      const profitShopapothekeAT = Number((finalPvpShopapothekeAT * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('SHOPAPOTHEKE - AT', ean, sendCostsShopapothekeAT, sendCostNoTaxShopapothekeAT, costPrice, fm1, liquidPriceShopapothekeAT, fm2, fm3, finalPvpShopapothekeAT, realTaxValueShopapothekeAT, marketplaceComissionShopapothekeAT, profitShopapothekeAT, special_cat, category) : ''

      // alltricks ES
      const sendCostNoTaxAlltricksES = sendCostsAlltricksES - (sendCostsAlltricksES * (fm3 / 100))
      const liquidPriceAlltricksES = costPrice + sendCostNoTaxAlltricksES
      const finalPvpAlltricksES = Number((liquidPriceAlltricksES / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueAlltricksES = finalPvpAlltricksES * (fm3 / 100)
      const marketplaceComissionAlltricksES = Number((finalPvpAlltricksES * (fm2 / 100)).toFixed(2))
      const profitAlltricksES = Number((finalPvpAlltricksES * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('ALLTRICKS - ES', ean, sendCostsAlltricksES, sendCostNoTaxAlltricksES, costPrice, fm1, liquidPriceAlltricksES, fm2, fm3, finalPvpAlltricksES, realTaxValueAlltricksES, marketplaceComissionAlltricksES, profitAlltricksES, special_cat, category) : ''

      // alltricks FR
      const sendCostNoTaxAlltricksFR = sendCostsAlltricksFR - (sendCostsAlltricksFR * (fm3 / 100))
      const liquidPriceAlltricksFR = costPrice + sendCostNoTaxAlltricksFR
      const finalPvpAlltricksFR = Number((liquidPriceAlltricksFR / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueAlltricksFR = finalPvpAlltricksFR * (fm3 / 100)
      const marketplaceComissionAlltricksFR = Number((finalPvpAlltricksFR * (fm2 / 100)).toFixed(2))
      const profitAlltricksFR = Number((finalPvpAlltricksFR * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('ALLTRICKS - FR', ean, sendCostsAlltricksFR, sendCostNoTaxAlltricksFR, costPrice, fm1, liquidPriceAlltricksFR, fm2, fm3, finalPvpAlltricksFR, realTaxValueAlltricksFR, marketplaceComissionAlltricksFR, profitAlltricksFR, special_cat, category) : ''

      // tiendanimal ES
      const sendCostNoTaxTiendanimalES = sendCostsTiendanimalES - (sendCostsTiendanimalES * (fm3 / 100))
      const liquidPriceTiendanimalES = costPrice + sendCostNoTaxTiendanimalES
      const finalPvpTiendanimalES = Number((liquidPriceTiendanimalES / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueTiendanimalES = finalPvpTiendanimalES * (fm3 / 100)
      const marketplaceComissionTiendanimalES = Number((finalPvpTiendanimalES * (fm2 / 100)).toFixed(2))
      const profitTiendanimalES = Number((finalPvpTiendanimalES * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('TIENDANIMAL - ES', ean, sendCostsTiendanimalES, sendCostNoTaxTiendanimalES, costPrice, fm1, liquidPriceTiendanimalES, fm2, fm3, finalPvpTiendanimalES, realTaxValueTiendanimalES, marketplaceComissionTiendanimalES, profitTiendanimalES, special_cat, category) : ''

      // macway FR
      const sendCostNoTaxMacwayFR = sendCostsMacwayFR - (sendCostsMacwayFR * (fm3 / 100))
      const liquidPriceMacwayFR = costPrice + sendCostNoTaxMacwayFR
      const finalPvpMacwayFR = Number((liquidPriceMacwayFR / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueMacwayFR = finalPvpMacwayFR * (fm3 / 100)
      const marketplaceComissionMacwayFR = Number((finalPvpMacwayFR * (fm2 / 100)).toFixed(2))
      const profitMacwayFR = Number((finalPvpMacwayFR * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('MACWAY - FR', ean, sendCostsMacwayFR, sendCostNoTaxMacwayFR, costPrice, fm1, liquidPriceMacwayFR, fm2, fm3, finalPvpMacwayFR, realTaxValueMacwayFR, marketplaceComissionMacwayFR, profitMacwayFR, special_cat, category) : ''

      // zooplus FR
      const sendCostNoTaxZooplusFR = sendCostsZooplusFR - (sendCostsZooplusFR * (fm3 / 100))
      const liquidPriceZooplusFR = costPrice + sendCostNoTaxZooplusFR
      const finalPvpZooplusFR = Number((liquidPriceZooplusFR / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueZooplusFR = finalPvpZooplusFR * (fm3 / 100)
      const marketplaceComissionZooplusFR = Number((finalPvpZooplusFR * (fm2 / 100)).toFixed(2))
      const profitZooplusFR = Number((finalPvpZooplusFR * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('ZOOPLUS - FR', ean, sendCostsZooplusFR, sendCostNoTaxZooplusFR, costPrice, fm1, liquidPriceZooplusFR, fm2, fm3, finalPvpZooplusFR, realTaxValueZooplusFR, marketplaceComissionZooplusFR, profitZooplusFR, special_cat, category) : ''

      // truffaut FR
      const sendCostNoTaxTruffautFR = sendCostsTruffautFR - (sendCostsTruffautFR * (fm3 / 100))
      const liquidPriceTruffautFR = costPrice + sendCostNoTaxTruffautFR
      const finalPvpTruffautFR = Number((liquidPriceTruffautFR / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueTruffautFR = finalPvpTruffautFR * (fm3 / 100)
      const marketplaceComissionTruffautFR = Number((finalPvpTruffautFR * (fm2 / 100)).toFixed(2))
      const profitTruffautFR = Number((finalPvpTruffautFR * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('TRUFFAUT - FR', ean, sendCostsTruffautFR, sendCostNoTaxTruffautFR, costPrice, fm1, liquidPriceTruffautFR, fm2, fm3, finalPvpTruffautFR, realTaxValueTruffautFR, marketplaceComissionTruffautFR, profitTruffautFR, special_cat, category) : ''

      // ubaldi FR
      const sendCostNoTaxUbaldiFR = sendCostsUbaldiFR - (sendCostsUbaldiFR * (fm3 / 100))
      const liquidPriceUbaldiFR = costPrice + sendCostNoTaxUbaldiFR
      const finalPvpUbaldiFR = Number((liquidPriceUbaldiFR / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const realTaxValueUbaldiFR = finalPvpUbaldiFR * (fm3 / 100)
      const marketplaceComissionUbaldiFR = Number((finalPvpUbaldiFR * (fm2 / 100)).toFixed(2))
      const profitUbaldiFR = Number((finalPvpUbaldiFR * (fm1 / 100)).toFixed(2))

      debug && ean === debugEan ? BIGHubCsvRepo.printDebug('UBALDI - FR', ean, sendCostsUbaldiFR, sendCostNoTaxUbaldiFR, costPrice, fm1, liquidPriceUbaldiFR, fm2, fm3, finalPvpUbaldiFR, realTaxValueUbaldiFR, marketplaceComissionUbaldiFR, profitUbaldiFR, special_cat, category) : ''

      // Price Shape calculations
      const finalPvp = Number((costPrice / (1 - ((fm2 / 100) + (fm3 / 100) + (fm1 / 100)))).toFixed(2))

      const allPrices: number[] = [];
      allPrices.push(
        finalPvpMisterautoFR,
        finalPvpBricodepotES,
        finalPvpBricodepotPT,
        finalPvpCastoramaFR,
        finalPvpTruffautFR,
        finalPvpUbaldiFR,
        finalPvpZooplusFR,
        finalPvpConforamaFR,
        finalPvpConforamaPT,
        finalPvpConforamaES,
        finalPvpEmpikPL,
        finalPvpFnacES,
        finalPvpFnacPT,
        finalPvpFnacFR,
        finalPvpManoamanoES,
        finalPvpCarrefourES,
        finalPvpCarrefourFR,
        finalPvpPccompES,
        finalPvpPccompPT,
        finalPvpPccompIT,
        finalPvpPccompFR,
        finalPvpMakroES,
        finalPvpMakroPT,
        finalPvpWortenES,
        finalPvpWortenPT,
        finalPvpMediamarktES,
        finalPvpMediamarktDE,
        finalPvpKuantokustaPT,
        finalPvpCdiscountFR,
        finalPvpMiraviaES,
        finalPvpBigbangSI,
        finalPvpBulevipES,
        finalPvpElcorteinglesES,
        finalPvpEleclercFR,
        finalPvpEpriceIT,
        finalPvpLeroymerlinES,
        finalPvpLeroymerlinPT,
        finalPvpLeroymerlinFR,
        finalPvpLeroymerlinIT,
        finalPvpLeroymerlinPL,
        finalPvpPerfumesclubPT,
        finalPvpPhonehouseES,
        finalPvpPixmaniaES,
        finalPvpPixmaniaFR,
        finalPvpRueducommerceFR,
        finalPvpClubefashionPT,
        finalPvpPlanetahuertoES,
        finalPvpVenteuniqueES,
        finalPvpVenteuniqueBE,
        finalPvpVenteuniqueDE,
        finalPvpVenteuniqueFR,
        finalPvpVenteuniqueIT,
        finalPvpVenteuniqueNL,
        finalPvpVenteuniquePT,
        finalPvpKauflandDE,
        finalPvpConradDE,
        finalPvpQuirumedesES,
        finalPvpShopapothekeDE,
        finalPvpShopapothekeAT,
        finalPvpAlltricksES,
        finalPvpAlltricksFR,
        finalPvpTiendanimalES,
        finalPvpMacwayFR
      );

      const maxPrice = BIGHubCsvRepo.getMaxValue(allPrices)

      if (isNaN(product.global_price)) {
        console.log(product.global_price)
      }

      if (product.weight !== undefined) {
        weight = product.weight
      }

      const { Id, attributes, ...restProduct } = product;

      return {
        ...restProduct,
        // global
        global_price: maxPrice, // no send costs added
        cost_price: costPrice,
        // disabled marketplaces
        amazon_fr_price: maxPrice,
        amazon_es_price: maxPrice,
        amazon_de_price: maxPrice,
        amazon_it_price: maxPrice,
        amazon_nl_price: maxPrice,
        amazon_pl_price: maxPrice,
        amazon_be_price: maxPrice,
        amazon_se_price: maxPrice,
        manomano_fr_price: maxPrice,
        manomano_it_price: maxPrice,
        mediamarkt_fr_price: maxPrice,

        // misterauto
        misterauto_fr_price: finalPvpMisterautoFR,

        // bricodepot
        bricodepot_es_price: finalPvpBricodepotES,
        bricodepot_pt_price: finalPvpBricodepotPT,

        // castorama
        castorama_fr_price: finalPvpCastoramaFR,

        // truffaut
        truffaut_fr_price: finalPvpTruffautFR,

        // ubaldi
        ubaldi_fr_price: finalPvpUbaldiFR,

        // zooplus
        zooplus_fr_price: finalPvpZooplusFR,

        // conforama
        conforama_fr_price: finalPvpConforamaFR,
        conforama_pt_price: finalPvpConforamaPT,
        conforama_es_price: finalPvpConforamaES,

        // empik
        empik_pl_price: finalPvpEmpikPL,

        // fnac
        fnac_es_price: finalPvpFnacES,
        fnac_pt_price: finalPvpFnacPT,
        fnac_fr_price: finalPvpFnacFR,
        // manoamano
        manomano_es_price: finalPvpManoamanoES,
        // carrefour
        carrefour_es_price: finalPvpCarrefourES,
        carrefour_fr_price: finalPvpCarrefourFR,
        // pccomp
        pccomp_es_price: finalPvpPccompES,
        pccomp_pt_price: finalPvpPccompPT,
        pccomp_it_price: finalPvpPccompIT,
        pccomp_fr_price: finalPvpPccompFR,
        // makro
        makro_es_price: finalPvpMakroES,
        makro_pt_price: finalPvpMakroPT,
        // worten
        worten_es_price: finalPvpWortenES,
        worten_pt_price: finalPvpWortenPT,

        // mediamarkt
        mediamarkt_es_price: finalPvpMediamarktES,
        mediamarkt_de_price: finalPvpMediamarktDE,

        // kuanto kusta
        kuantokusta_pt_price: finalPvpKuantokustaPT,

        // cdiscount
        cdiscount_fr_price: finalPvpCdiscountFR,

        // miravia
        miravia_es_price: finalPvpMiraviaES,

        // bigbang
        bigbang_si_price: finalPvpBigbangSI,

        // bulevip
        bulevip_es_price: finalPvpBulevipES,

        // elcorteingles
        elcorteingles_es_price: finalPvpElcorteinglesES,

        // eleclerc
        eleclerc_fr_price: finalPvpEleclercFR,

        // eprice
        eprice_it_price: finalPvpEpriceIT,

        // leroy merlin
        leroymerlin_es_price: finalPvpLeroymerlinES,
        leroymerlin_pt_price: finalPvpLeroymerlinPT,
        leroymerlin_fr_price: finalPvpLeroymerlinFR,
        leroymerlin_it_price: finalPvpLeroymerlinIT,
        leroymerlin_pl_price: finalPvpLeroymerlinPL,

        // perfumes club
        perfumesclub_pt_price: finalPvpPerfumesclubPT,

        // phone house
        phonehouse_es_price: finalPvpPhonehouseES,

        // pixmania
        pixmania_es_price: finalPvpPixmaniaES,
        pixmania_fr_price: finalPvpPixmaniaFR,

        // rueducomerce
        rueducommerce_fr_price: finalPvpRueducommerceFR,

        // clubefashion
        clubefashion_pt_price: finalPvpClubefashionPT,

        // planetahuerto
        planetahuerto_es_price: finalPvpPlanetahuertoES,

        // venteunique
        venteunique_es_price: finalPvpVenteuniqueES,
        venteunique_be_price: finalPvpVenteuniqueBE,
        venteunique_de_price: finalPvpVenteuniqueDE,
        venteunique_fr_price: finalPvpVenteuniqueFR,
        venteunique_it_price: finalPvpVenteuniqueIT,
        venteunique_nl_price: finalPvpVenteuniqueNL,
        venteunique_pt_price: finalPvpVenteuniquePT,

        // kaufland
        kaufland_de_price: finalPvpKauflandDE,

        // conrad
        conrad_de_price: finalPvpConradDE,

        // quirumedes
        quirumedes_es_price: finalPvpQuirumedesES,

        // Price Shape integration
        priceshape_price: finalPvp,
        priceshape_es_shipping: sendCostsFnacES,
        priceshape_pt_shipping: sendCostsClubefashionPT,
        priceshape_fr_shipping: sendCostsEleclercFR,
        priceshape_it_shipping: sendCostsEpriceIT,
        priceshape_de_shipping: sendCostsConradDE,
        priceshape_pl_shipping: sendCostsEmpikPL,
        priceshape_si_shipping: sendCostsBigbangSI,

        // Shopapotheke
        shopapotheke_de_price: finalPvpShopapothekeDE,
        shopapotheke_at_price: finalPvpShopapothekeAT,

        // Alltricks
        alltricks_es_price: finalPvpAlltricksES,
        alltricks_fr_price: finalPvpAlltricksFR,

        // Tiendanimal
        tiendanimal_es_price: finalPvpTiendanimalES,

        // Macway
        macway_fr_price: finalPvpMacwayFR
      };

    })

    return newProducts;
  }

  // ('EMPIK - PL', ean, sendCostsEmpikPL, sendCostNoTaxEmpikPL, costPrice, m1, liquidPriceEmpikPL, m2, m3, finalPvpEmpikPL, realTaxValueEmpikPL, marketplaceComissionEmpikPL, profitEmpikPL, special_cat)
  static printDebug(
    marketplace: string,
    ean: string,
    sendCosts: number,
    sendCostsNoTax: number,
    costPrice: number,
    m1: number,
    liquidPrice: number,
    m2: number,
    m3: number,
    finalPvp: number,
    realTaxValue: number,
    marketplaceComission: number,
    profit: number,
    spec_cat: boolean,
    category: string
  ) {
    // Create the content string
    let content = `--------------------- ${marketplace}\n`;
    content += '------------------------------------------------------------------------------------------------------------\n';
    content += `EAN:                  ${ean}\n`;
    content += '------------------------------------------------------------------------------------------------------------\n';
    content += `Categoria:            ${category}\n`;
    content += '------------------------------------------------------------------------------------------------------------\n';
    content += `Preço Custo:          ${costPrice}€\n`;
    content += `Portes:               ${sendCosts}€\n`;
    content += `Portes sem IVA:       ${sendCostsNoTax}€\n`;
    content += `Preço Líquido:        ${liquidPrice}€\n`;
    if (spec_cat === true) {
      content += `--------------------- Categoria especial: ${category}\n`;
    }
    content += `Margem:               ${m1}%\n`;
    content += `Comissão Marketplace: ${m2}%\n`;
    content += `Iva:                  ${m3}%\n`;
    content += `PVP Final:            ${finalPvp}€\n`;
    content += `IVA valor:            ${realTaxValue}€\n`;
    content += `Comissão Marketplace: ${marketplaceComission}€\n`;
    content += `Lucro:                ${profit}€\n`;
    content += '............................................................................................................\n\n';

    // Add timestamp
    const timestamp = new Date().toISOString();
    content = `[${timestamp}]\n` + content;

    // Define a single log file
    const logFileName = `${ean}_debug.log`;

    // Ensure the directory exists
    try {
      // Append to the file (or create if it doesn't exist)
      fs.appendFileSync(logFileName, content);
    } catch (error) {
      console.error(`Failed to write to log file: ${error}`);
    }

    // Optional: Keep the console logging as well
    console.log(content);

    return;
  }

  static getMaxValue(allPrices: number[]): number {
    if (allPrices.length === 0) {
      return 50000; // or throw an exception, or return a giant number, based on your needs
    }

    const maxValue = Math.max(...allPrices);

    return maxValue;
  }

  static setSendCosts(weight: number, sOrigin: string, mktplcDestination: string, tableData: any): number {

    // console.log (sOrigin)
    const print: Boolean = false

    //console.log(tableData)

    // const matchEan = '8435037841180'
    print ? console.log(`weight: ${weight} | sOrigin: ${sOrigin} | mktplcDestination: ${mktplcDestination}`) : ''

    let sendCosts: number = 0
    const registry: SendCosts = tableData.find(
      (item: SendCosts) =>
        item.send_from_country === sOrigin &&
        item.send_to_marketplace_country === mktplcDestination
    );

    const baseValue = Number(registry.valor_base)
    const scope1 = Number(registry.scope_1)
    const scope2 = Number(registry.scope_2)
    const scope3 = Number(registry.scope_3)
    const scope4 = Number(registry.scope_4)
    const scope5 = Number(registry.scope_5)
    const scope6 = Number(registry.scope_6)

    if (weight > 0) {
      if (weight > 0 && weight < 5) {
        sendCosts = scope1
        print ? console.log(`custo de envio: ${sendCosts} para peso = ${weight}`) : ''
        return sendCosts
      } else if (weight >= 5 && weight < 10) {
        sendCosts = scope2
        print ? console.log(`custo de envio: ${sendCosts} para peso = ${weight}`) : ''
        return sendCosts
      } else if (weight >= 10 && weight < 50) {
        sendCosts = scope3
        print ? console.log(`custo de envio: ${sendCosts} para peso = ${weight}`) : ''
        return sendCosts
      } else if (weight >= 50 && weight < 100) {
        sendCosts = scope4
        print ? console.log(`custo de envio: ${sendCosts} para peso = ${weight}`) : ''
        return sendCosts
      } else if (weight >= 100 && weight < 200) {
        sendCosts = scope5
        print ? console.log(`custo de envio: ${sendCosts} para peso = ${weight}`) : ''
        return sendCosts
      } else if (weight >= 200 && weight < 300) {
        sendCosts = scope6
        print ? console.log(`custo de envio: ${sendCosts} para peso = ${weight}`) : ''
        return sendCosts
      } else {
        print ? console.log('out of range') : ''
      }
    }
    print ? console.log(`valor base: ${baseValue}`) : ''
    return baseValue
  }
}

// scope_1: number           // 1 a 5 kg
// scope_2: number           // 5 a 10 kg
// scope_3: number           // 10 a 50 kg
// scope_4: number           // 50 a 100 kg
// scope_5: number           // 100 a 200 kg
// scope_6: number           // 200 a 300 kg


// Função para autenticar o cliente
async function authenticate(): Promise<JWT> {
  const auth = new GoogleAuth({
    keyFile: './src/modules/bighub/jsons/ruben-multilojas-d9832f262320.json', // Caminho para o arquivo JSON de credenciais
    scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly'],
  });
  return await auth.getClient() as JWT;
}

// Função para obter os dados do fornecedor
async function getSupplierData(supplierName: string): Promise<string | Control> {
  const authClient = await authenticate();
  const sheets = google.sheets({ version: 'v4', auth: authClient });

  const spreadsheetId = '1eHUXaOK0jmZoeQjfRXvNOrHLFU7dJzEB-yvBi6yeKis'; // Seu ID da planilha
  const range = 'Control!A:N'; // Faixa de dados na planilha

  try {
    const response = await sheets.spreadsheets.values.get({
      spreadsheetId,
      range,
    });

    const values = response.data.values;

    if (!values || values.length === 0) {
      return 'No data found.';
    } else {
      const header = values.shift();

      if (!header) {
        return 'No header found.';
      }

      const supplierIndex = header.indexOf('supplier');

      if (supplierIndex === -1) {
        return 'Supplier column not found.';
      }

      for (const row of values) {
        if (row[supplierIndex] === supplierName) {
          if (header.length !== row.length) {
            return "Error: Row doesn't have the same number of elements as the header.";
          }

          // Cria um objeto para armazenar os dados brutos
          const rawData: Record<string, string> = {};
          header.forEach((key, index) => {
            rawData[key] = row[index];
          });

          // Constrói o objeto Control garantindo que todas as propriedades estejam presentes
          const supplierData: Control = {
            supplier: rawData['supplier'],
            country: rawData['country'],
            status: rawData['status'],
            email: rawData['email'],
            password: rawData['password'],
            id_bighub: Number(rawData['id_bighub']),
            m1: Number(rawData['m1'].replace(',', '.')),
            m2: Number(rawData['m2'].replace(',', '.')),
            m3: Number(rawData['m3'].replace(',', '.')),
            //m4: Number(rawData['m4'].replace(',', '.')),
            integration_type_code: rawData['integration_type_code'],
            cat_prices: rawData['cat_prices'],
            custom_m1: Number(rawData['custom_m1'].replace(',', '.')),
            custom_m2: Number(rawData['custom_m2'].replace(',', '.')),
            custom_m3: Number(rawData['custom_m3'].replace(',', '.')),
            stockly_m1: 0 //Number(rawData['stockly_m1'].replace(',', '.')),
          };

          return supplierData;
        }
      }

      return 'Supplier not found.';
    }
  } catch (error) {
    console.error('The API returned an error, on getSupplier request:', error);
    return `Error: ${error}`;
  }
}

// Função para obter os dados de todos os eans de um determinado supplier
async function getEanSoloData(supplierName: string): Promise<string | Solos[]> {
  const authClient = await authenticate();
  const sheets = google.sheets({ version: 'v4', auth: authClient });

  const spreadsheetId = '1eHUXaOK0jmZoeQjfRXvNOrHLFU7dJzEB-yvBi6yeKis'; // Your spreadsheet ID
  const range = 'Solo!A:G'; // Data range in the spreadsheet

  try {
    const response = await sheets.spreadsheets.values.get({
      spreadsheetId,
      range,
    });

    const values = response.data.values;

    if (!values || values.length === 0) {
      return 'No data found.';
    } else {
      const header = values.shift();

      if (!header) {
        return 'No header found.';
      }

      const supplierIndex = header.indexOf('supplier');

      if (supplierIndex === -1) {
        return 'Supplier column not found.';
      }

      const solosArray: Solos[] = [];

      for (const row of values) {
        if (row[supplierIndex] === supplierName) {
          if (header.length !== row.length) {
            return "Error: Row doesn't have the same number of elements as the header.";
          }

          const rawData: Record<string, string> = {};
          header.forEach((key, index) => {
            rawData[key] = row[index];
          });

          const solo: Solos = {
            ean: rawData['ean'],
            supplier: rawData['supplier'],
            m1: Number(rawData['m1'].replace(',', '.')),
            m2: Number(rawData['m2'].replace(',', '.')),
            m3: Number(rawData['m3'].replace(',', '.')),
            status: rawData['status'],
          };

          solosArray.push(solo);
        }
      }

      if (solosArray.length === 0) {
        return `Solo EANs not found in supplier ${supplierName}`;
      }

      return solosArray;
    }
  } catch (error) {
    console.error('The API returned an error, on getEanSolo request:', error);
    return `Error: ${error}`;
  }


}