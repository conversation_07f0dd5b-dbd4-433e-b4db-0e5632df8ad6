[2025-05-22T11:40:33.603Z] INFO [BigbuyController] Starting BigBuy file download process
Data: {
  "fileCount": 13,
  "destination": "./data/downloads/bigbuy"
}
---
[2025-05-22T11:41:41.167Z] INFO [BigbuyController] Files downloaded successfully
Data: {
  "count": 13
}
---
[2025-05-22T11:41:46.738Z] INFO [BigbuyController] Files combined successfully
Data: {
  "outputPath": "data\\downloads\\bigbuy\\bigbuy_combined.csv"
}
---
[2025-05-22T11:41:46.740Z] INFO [BigbuyController] Fetching latest stock and price data from BigBuy...
---
[2025-05-22T11:41:46.742Z] INFO [BigbuyController] Starting to fetch stock data...
---
[2025-05-22T11:47:12.238Z] INFO [BigbuyController] ⏳ Starting stock file simplification
---
[2025-05-22T11:47:14.222Z] INFO [BigbuyController] 📊 Parsed stock file with 365364 items in 1904ms
---
[2025-05-22T11:47:14.224Z] INFO [BigbuyController] 📋 Sample data format: {"id":265,"sku":"********","stocks":[{"quantity":1,"minHandlingDays":0,"maxHandlingDays":1,"warehouse":1},{"quantity":0,"minHandlingDays":1,"maxHandlingDays":2,"warehouse":1}]}
---
[2025-05-22T11:47:14.226Z] INFO [BigbuyController] 🔄 Creating simplified stock data...
---
[2025-05-22T11:47:14.421Z] INFO [BigbuyController] ✅ Transformed 365364 items in 192ms
---
[2025-05-22T11:47:14.423Z] INFO [BigbuyController] 📋 Sample simplified data: {"sku":"********","stock":1}
---
[2025-05-22T11:47:14.424Z] INFO [BigbuyController] 💾 Saving simplified stock data back to: C:\Users\<USER>\Documents\dev\feedmaster-pro\bigbuy-stock.json
---
[2025-05-22T11:47:14.690Z] INFO [BigbuyController] ✅ Simplified stock data saved in 265ms:
---
[2025-05-22T11:47:14.692Z] INFO [BigbuyController]    - Size: 16957 KB
---
[2025-05-22T11:47:14.694Z] INFO [BigbuyController]    - Items: 365364
---
[2025-05-22T11:47:14.695Z] INFO [BigbuyController]    - Format: { sku, stock }
---
[2025-05-22T11:47:14.697Z] INFO [BigbuyController] Stock data updated successfully and saved to bigbuy-stock.json
---
[2025-05-22T11:47:14.698Z] INFO [BigbuyController] Starting to fetch price data...
---
[2025-05-22T11:52:46.435Z] INFO [BigbuyController] Price data updated successfully and saved to bigbuy-price.json
---
[2025-05-22T11:52:46.438Z] INFO [BigbuyController] ⏳ Starting price file simplification
---
[2025-05-22T11:52:48.499Z] INFO [BigbuyController] 📊 Parsed price file with 365324 items in 2059ms
---
[2025-05-22T11:52:48.501Z] INFO [BigbuyController] 📋 Sample price data format: {"id":1120651,"sku":"S3060870","wholesalePrice":274.4,"inShopsPrice":457.34,"priceLargeQuantities":[{"id":13154819,"quantity":5,"price":271.11},{"id":13154820,"quantity":10,"price":267.81}]}
---
[2025-05-22T11:52:48.502Z] INFO [BigbuyController] 🔄 Creating simplified price data...
---
[2025-05-22T11:52:48.654Z] INFO [BigbuyController] ✅ Transformed 365324 price items in 150ms
---
[2025-05-22T11:52:48.656Z] INFO [BigbuyController] 📋 Sample simplified price data: {"sku":"S3060870","wholesalePrice":274.4}
---
[2025-05-22T11:52:48.657Z] INFO [BigbuyController] 💾 Saving simplified price data back to: C:\Users\<USER>\Documents\dev\feedmaster-pro\bigbuy-price.json
---
[2025-05-22T11:52:49.090Z] INFO [BigbuyController] ✅ Simplified price data saved in 432ms:
---
[2025-05-22T11:52:49.092Z] INFO [BigbuyController]    - Size: 21394 KB
---
[2025-05-22T11:52:49.094Z] INFO [BigbuyController]    - Items: 365324
---
[2025-05-22T11:52:49.096Z] INFO [BigbuyController]    - Format: { sku, wholesalePrice }
---
[2025-05-22T11:52:49.097Z] INFO [BigbuyController] Price data simplified for easier CSV integration
---
[2025-05-22T11:52:49.100Z] INFO [BigbuyController] Starting stock combination process
---
[2025-05-22T11:52:49.103Z] INFO [BigbuyController] ⏳ Starting stock file simplification
---
[2025-05-22T11:52:49.395Z] INFO [BigbuyController] 📊 Parsed stock file with 365364 items in 290ms
---
[2025-05-22T11:52:49.396Z] INFO [BigbuyController] 📋 Sample data format: {"sku":"********","stock":1}
---
[2025-05-22T11:52:49.397Z] INFO [BigbuyController] ✅ Stock data is already in simplified format
---
[2025-05-22T11:52:49.399Z] INFO [BigbuyController] Found stock file at: C:\Users\<USER>\Documents\dev\feedmaster-pro\bigbuy-stock.json
---
[2025-05-22T11:52:49.690Z] INFO [BigbuyController] Loaded 365364 stock records from JSON
---
[2025-05-22T11:52:49.963Z] INFO [BigbuyController] Created stock map with 365364 entries
---
[2025-05-22T11:52:49.964Z] INFO [BigbuyController] 📂 Original CSV file: data\downloads\bigbuy\bigbuy_combined.csv
---
[2025-05-22T11:52:49.966Z] INFO [BigbuyController]    - Size: 316816 KB
---
[2025-05-22T11:52:49.967Z] INFO [BigbuyController] 🔄 Starting CSV processing...
---
[2025-05-22T11:52:49.969Z] INFO [BigbuyController] Examining CSV structure...
---
[2025-05-22T11:52:54.105Z] INFO [BigbuyController] CSV header line: ﻿ID;CATEGORY;NAME;ATTRIBUTE1;ATTRIBUTE2;VALUE1;VALUE2;DESCRIPTION;BRAND;FEATURE;PRICE;PVP_BIGBUY;PVD;IVA;VIDEO;EAN13;WIDTH;HEIGHT;DEPTH;WEIGHT;STOCK;DATE_ADD;DATE_UPD;IMAGE1;IMAGE2;IMAGE3;IMAGE4;IMAGE5;IMAGE6;IMAGE7;IMAGE8;CONDITION
---
[2025-05-22T11:52:54.108Z] INFO [BigbuyController] Detected 32 columns in header
---
[2025-05-22T11:52:54.110Z] INFO [BigbuyController] Header sample: ﻿ID, CATEGORY, NAME, ATTRIBUTE1, ATTRIBUTE2, VALUE1, VALUE2, DESCRIPTION, BRAND, FEATURE
---
[2025-05-22T11:52:54.121Z] INFO [BigbuyController] First column header: "﻿ID"
---
[2025-05-22T11:52:54.123Z] INFO [BigbuyController] Found stock column at index 20
---
[2025-05-22T11:52:54.125Z] INFO [BigbuyController] 📋 CSV Header: ﻿ID;CATEGORY;NAME;ATTRIBUTE1;ATTRIBUTE2;VALUE1;VALUE2;DESCRIPTION;BRAND;FEATURE;PRICE;PVP_BIGBUY;PVD;IVA;VIDEO;EAN13;WIDTH;HEIGHT;DEPTH;WEIGHT;STOCK;DATE_ADD;DATE_UPD;IMAGE1;IMAGE2;IMAGE3;IMAGE4;IMAGE5;IMAGE6;IMAGE7;IMAGE8;CONDITION
---
[2025-05-22T11:52:54.128Z] INFO [BigbuyController] 📊 Detected 32 columns in CSV header
---
[2025-05-22T11:52:54.130Z] INFO [BigbuyController] ID column index: -1, Stock column index: 20
---
[2025-05-22T11:52:54.132Z] WARN [BigbuyController] Could not find ID/SKU column in header! Will use first column.
---
[2025-05-22T11:52:54.140Z] INFO [BigbuyController] Using ID column: "﻿ID", Stock column: "STOCK"
---
[2025-05-22T11:52:54.142Z] INFO [BigbuyController] 🔄 Starting full CSV processing...
---
[2025-05-22T11:53:01.137Z] INFO [BigbuyController] ✓ Updated 50000 stocks so far (100.00% hit rate)
---
[2025-05-22T11:53:01.139Z] INFO [BigbuyController] 📊 Progress: 50000 rows (50000 stocks updated) - 14.3% complete
---
[2025-05-22T11:53:07.989Z] INFO [BigbuyController] 📊 Progress: 100000 rows (99992 stocks updated) - 28.6% complete
---
[2025-05-22T11:53:07.991Z] INFO [BigbuyController] ✓ Updated 100000 stocks so far (99.99% hit rate)
---
[2025-05-22T11:53:16.172Z] INFO [BigbuyController] 📊 Progress: 150000 rows (149992 stocks updated) - 42.9% complete
---
[2025-05-22T11:53:16.175Z] INFO [BigbuyController] ✓ Updated 150000 stocks so far (99.99% hit rate)
---
[2025-05-22T11:53:23.374Z] INFO [BigbuyController] 📊 Progress: 200000 rows (199987 stocks updated) - 57.1% complete
---
[2025-05-22T11:53:23.376Z] INFO [BigbuyController] ✓ Updated 200000 stocks so far (99.99% hit rate)
---
[2025-05-22T11:53:31.020Z] INFO [BigbuyController] 📊 Progress: 250000 rows (249987 stocks updated) - 71.4% complete
---
[2025-05-22T11:53:31.022Z] INFO [BigbuyController] ✓ Updated 250000 stocks so far (99.99% hit rate)
---
[2025-05-22T11:53:39.202Z] INFO [BigbuyController] 📊 Progress: 300000 rows (299987 stocks updated) - 85.7% complete
---
[2025-05-22T11:53:39.203Z] INFO [BigbuyController] ✓ Updated 300000 stocks so far (100.00% hit rate)
---
[2025-05-22T11:53:46.932Z] INFO [BigbuyController] ✅ Finished reading CSV in 57.0s
---
[2025-05-22T11:53:46.934Z] INFO [BigbuyController] 📊 Final processing stats:
---
[2025-05-22T11:53:46.936Z] INFO [BigbuyController]    - Rows processed: 344140
---
[2025-05-22T11:53:46.937Z] INFO [BigbuyController]    - Stocks updated: 344127 (100.00% of rows)
---
[2025-05-22T11:53:46.938Z] INFO [BigbuyController]    - Processing speed: 6041 rows/second
---
[2025-05-22T11:53:46.940Z] INFO [BigbuyController] Starting to write updated data to temporary file: data\downloads\bigbuy\bigbuy_combined.csv.temp
---
[2025-05-22T11:53:46.947Z] INFO [BigbuyController] ✍️ Writing progress: 25000/344140 rows (7.3%)
---
[2025-05-22T11:53:46.953Z] INFO [BigbuyController] ✍️ Writing progress: 50000/344140 rows (14.5%)
---
[2025-05-22T11:53:46.958Z] INFO [BigbuyController] ✍️ Writing progress: 75000/344140 rows (21.8%)
---
[2025-05-22T11:53:46.962Z] INFO [BigbuyController] ✍️ Writing progress: 100000/344140 rows (29.1%)
---
[2025-05-22T11:53:46.968Z] INFO [BigbuyController] ✍️ Writing progress: 125000/344140 rows (36.3%)
---
[2025-05-22T11:53:46.989Z] INFO [BigbuyController] ✍️ Writing progress: 150000/344140 rows (43.6%)
---
[2025-05-22T11:53:46.994Z] INFO [BigbuyController] ✍️ Writing progress: 175000/344140 rows (50.9%)
---
[2025-05-22T11:53:46.998Z] INFO [BigbuyController] ✍️ Writing progress: 200000/344140 rows (58.1%)
---
[2025-05-22T11:53:47.001Z] INFO [BigbuyController] ✍️ Writing progress: 225000/344140 rows (65.4%)
---
[2025-05-22T11:53:47.005Z] INFO [BigbuyController] ✍️ Writing progress: 250000/344140 rows (72.6%)
---
[2025-05-22T11:53:47.013Z] INFO [BigbuyController] ✍️ Writing progress: 275000/344140 rows (79.9%)
---
[2025-05-22T11:53:47.018Z] INFO [BigbuyController] ✍️ Writing progress: 300000/344140 rows (87.2%)
---
[2025-05-22T11:53:47.056Z] INFO [BigbuyController] ✍️ Writing progress: 325000/344140 rows (94.4%)
---
[2025-05-22T11:53:47.060Z] INFO [BigbuyController] ✍️ Writing progress: 344140/344140 rows (100.0%)
---
[2025-05-22T11:54:09.193Z] INFO [BigbuyController] ✅ Successfully wrote all 344140 rows to temporary file
---
[2025-05-22T11:54:09.195Z] INFO [BigbuyController] Using stream-based file replacement strategy...
---
[2025-05-22T11:54:09.197Z] INFO [BigbuyController] Creating backup of original file...
---
[2025-05-22T11:54:09.456Z] INFO [BigbuyController] Backup created at: data\downloads\bigbuy\bigbuy_combined.csv.backup
---
[2025-05-22T11:54:09.458Z] INFO [BigbuyController] Starting stream-based file replacement...
---
[2025-05-22T11:54:09.461Z] INFO [BigbuyController] Piping temporary file to destination...
---
[2025-05-22T11:54:09.559Z] INFO [BigbuyController] Stream progress: 10MB / 323MB (3.09%)
---
[2025-05-22T11:54:09.597Z] INFO [BigbuyController] Stream progress: 20MB / 323MB (6.18%)
---
[2025-05-22T11:54:09.638Z] INFO [BigbuyController] Stream progress: 30MB / 323MB (9.28%)
---
[2025-05-22T11:54:09.677Z] INFO [BigbuyController] Stream progress: 40MB / 323MB (12.37%)
---
[2025-05-22T11:54:09.718Z] INFO [BigbuyController] Stream progress: 50MB / 323MB (15.46%)
---
[2025-05-22T11:54:09.760Z] INFO [BigbuyController] Stream progress: 60MB / 323MB (18.55%)
---
[2025-05-22T11:54:09.809Z] INFO [BigbuyController] Stream progress: 70MB / 323MB (21.64%)
---
[2025-05-22T11:54:09.844Z] INFO [BigbuyController] Stream progress: 80MB / 323MB (24.74%)
---
[2025-05-22T11:54:09.879Z] INFO [BigbuyController] Stream progress: 90MB / 323MB (27.83%)
---
[2025-05-22T11:54:09.921Z] INFO [BigbuyController] Stream progress: 100MB / 323MB (30.92%)
---
[2025-05-22T11:54:09.961Z] INFO [BigbuyController] Stream progress: 110MB / 323MB (34.01%)
---
[2025-05-22T11:54:10.004Z] INFO [BigbuyController] Stream progress: 120MB / 323MB (37.10%)
---
[2025-05-22T11:54:10.057Z] INFO [BigbuyController] Stream progress: 130MB / 323MB (40.20%)
---
[2025-05-22T11:54:10.094Z] INFO [BigbuyController] Stream progress: 140MB / 323MB (43.29%)
---
[2025-05-22T11:54:10.138Z] INFO [BigbuyController] Stream progress: 150MB / 323MB (46.38%)
---
[2025-05-22T11:54:10.190Z] INFO [BigbuyController] Stream progress: 160MB / 323MB (49.47%)
---
[2025-05-22T11:54:10.230Z] INFO [BigbuyController] Stream progress: 170MB / 323MB (52.56%)
---
[2025-05-22T11:54:10.273Z] INFO [BigbuyController] Stream progress: 180MB / 323MB (55.65%)
---
[2025-05-22T11:54:10.312Z] INFO [BigbuyController] Stream progress: 190MB / 323MB (58.75%)
---
[2025-05-22T11:54:10.353Z] INFO [BigbuyController] Stream progress: 200MB / 323MB (61.84%)
---
[2025-05-22T11:54:10.390Z] INFO [BigbuyController] Stream progress: 210MB / 323MB (64.93%)
---
[2025-05-22T11:54:10.433Z] INFO [BigbuyController] Stream progress: 220MB / 323MB (68.02%)
---
[2025-05-22T11:54:10.478Z] INFO [BigbuyController] Stream progress: 230MB / 323MB (71.11%)
---
[2025-05-22T11:54:10.514Z] INFO [BigbuyController] Stream progress: 240MB / 323MB (74.21%)
---
[2025-05-22T11:54:10.551Z] INFO [BigbuyController] Stream progress: 250MB / 323MB (77.30%)
---
[2025-05-22T11:54:10.597Z] INFO [BigbuyController] Stream progress: 260MB / 323MB (80.39%)
---
[2025-05-22T11:54:10.633Z] INFO [BigbuyController] Stream progress: 270MB / 323MB (83.48%)
---
[2025-05-22T11:54:10.670Z] INFO [BigbuyController] Stream progress: 280MB / 323MB (86.57%)
---
[2025-05-22T11:54:10.716Z] INFO [BigbuyController] Stream progress: 290MB / 323MB (89.67%)
---
[2025-05-22T11:54:10.751Z] INFO [BigbuyController] Stream progress: 300MB / 323MB (92.76%)
---
[2025-05-22T11:54:10.782Z] INFO [BigbuyController] Stream progress: 310MB / 323MB (95.85%)
---
[2025-05-22T11:54:10.823Z] INFO [BigbuyController] Stream progress: 320MB / 323MB (98.94%)
---
[2025-05-22T11:54:10.834Z] INFO [BigbuyController] ✅ Stream completed. Verifying file sizes...
---
[2025-05-22T11:54:10.837Z] INFO [BigbuyController] ✅ File sizes match (339132271 bytes). Replacement successful.
---
[2025-05-22T11:54:10.905Z] INFO [BigbuyController] Temporary file deleted
---
[2025-05-22T11:54:10.908Z] INFO [BigbuyController] 🎉 Stock combination completed successfully with stream approach!
---
[2025-05-22T11:54:10.909Z] INFO [BigbuyController] 📈 Final statistics:
---
[2025-05-22T11:54:10.910Z] INFO [BigbuyController] - Total rows processed: 344140
---
[2025-05-22T11:54:10.912Z] INFO [BigbuyController] - Stocks updated: 344127 (100.00% of rows)
---
[2025-05-22T11:54:10.913Z] INFO [BigbuyController] - Updated file: data\downloads\bigbuy\bigbuy_combined.csv
---
[2025-05-22T11:54:10.915Z] INFO [BigbuyController] - File size: 331184 KB
---
[2025-05-22T11:54:10.969Z] INFO [BigbuyController] Backup file deleted as replacement was successful
---
[2025-05-22T11:54:10.973Z] INFO [BigbuyController] Starting price combination process
---
[2025-05-22T11:54:10.976Z] INFO [BigbuyController] ⏳ Starting price file simplification
---
[2025-05-22T11:54:11.395Z] INFO [BigbuyController] 📊 Parsed price file with 365324 items in 417ms
---
[2025-05-22T11:54:11.398Z] INFO [BigbuyController] 📋 Sample price data format: {"sku":"S3060870","wholesalePrice":274.4}
---
[2025-05-22T11:54:11.399Z] INFO [BigbuyController] ✅ Price data is already in simplified format
---
[2025-05-22T11:54:11.401Z] INFO [BigbuyController] Found price file at: C:\Users\<USER>\Documents\dev\feedmaster-pro\bigbuy-price.json
---
[2025-05-22T11:54:11.912Z] INFO [BigbuyController] Loaded 365324 price records from JSON
---
[2025-05-22T11:54:12.197Z] INFO [BigbuyController] Created price map with 365321 entries
---
[2025-05-22T11:54:12.199Z] INFO [BigbuyController] 📂 Original CSV file: data\downloads\bigbuy\bigbuy_combined.csv
---
[2025-05-22T11:54:12.201Z] INFO [BigbuyController]    - Size: 331184 KB
---
[2025-05-22T11:54:12.203Z] INFO [BigbuyController] 🔄 Starting CSV processing for price updates...
---
[2025-05-22T11:54:15.966Z] INFO [BigbuyController] CSV header line: "ID";"CATEGORY";"NAME";"ATTRIBUTE1";"ATTRIBUTE2";"VALUE1";"VALUE2";"DESCRIPTION";"BRAND";"FEATURE";"PRICE";"PVP_BIGBUY";"PVD";"IVA";"VIDEO";"EAN13";"WIDTH";"HEIGHT";"DEPTH";"WEIGHT";"STOCK";"DATE_ADD";"DATE_UPD";"IMAGE1";"IMAGE2";"IMAGE3";"IMAGE4";"IMAGE5";"IMAGE6";"IMAGE7";"IMAGE8";"CONDITION"
---
[2025-05-22T11:54:15.972Z] INFO [BigbuyController] Detected 32 columns in header
---
[2025-05-22T11:54:15.976Z] INFO [BigbuyController] Found wholesale price column at index 10: ""PRICE""
---
[2025-05-22T11:54:15.987Z] INFO [BigbuyController] 🔄 Starting full CSV processing for price updates...
---
[2025-05-22T11:54:28.170Z] INFO [BigbuyController] 📊 Progress: 50000 rows (49999 prices updated) - 14.3% complete
---
[2025-05-22T11:54:28.172Z] INFO [BigbuyController] ✓ Updated 50000 prices so far (100.00% hit rate)
---
[2025-05-22T11:54:38.118Z] INFO [BigbuyController] 📊 Progress: 100000 rows (99991 prices updated) - 28.6% complete
---
[2025-05-22T11:54:38.146Z] INFO [BigbuyController] ✓ Updated 100000 prices so far (99.99% hit rate)
---
[2025-05-22T11:54:46.629Z] INFO [BigbuyController] 📊 Progress: 150000 rows (149991 prices updated) - 42.9% complete
---
[2025-05-22T11:54:46.640Z] INFO [BigbuyController] ✓ Updated 150000 prices so far (99.99% hit rate)
---
[2025-05-22T11:54:58.669Z] INFO [BigbuyController] 📊 Progress: 200000 rows (199946 prices updated) - 57.1% complete
---
[2025-05-22T11:54:58.702Z] INFO [BigbuyController] ✓ Updated 200000 prices so far (99.97% hit rate)
---
[2025-05-22T11:55:08.882Z] INFO [BigbuyController] 📊 Progress: 250000 rows (249944 prices updated) - 71.4% complete
---
[2025-05-22T11:55:08.889Z] INFO [BigbuyController] ✓ Updated 250000 prices so far (99.98% hit rate)
---
[2025-05-22T11:55:17.298Z] INFO [BigbuyController] 📊 Progress: 300000 rows (299942 prices updated) - 85.7% complete
---
[2025-05-22T11:55:17.312Z] INFO [BigbuyController] ✓ Updated 300000 prices so far (99.98% hit rate)
---
[2025-05-22T11:55:27.095Z] INFO [BigbuyController] ✅ Finished reading CSV for price updates in 74.9s
---
[2025-05-22T11:55:27.097Z] INFO [BigbuyController] 📊 Final processing stats:
---
[2025-05-22T11:55:27.098Z] INFO [BigbuyController]    - Rows processed: 344140
---
[2025-05-22T11:55:27.100Z] INFO [BigbuyController]    - Prices updated: 344082 (99.98% of rows)
---
[2025-05-22T11:55:27.101Z] INFO [BigbuyController]    - Processing speed: 4595 rows/second
---
[2025-05-22T11:55:27.104Z] INFO [BigbuyController] Starting to write price-updated data to temporary file: data\downloads\bigbuy\bigbuy_combined.csv.price_temp
---
[2025-05-22T11:55:27.113Z] INFO [BigbuyController] ✍️ Writing progress: 25000/344140 rows (7.3%)
---
[2025-05-22T11:55:27.119Z] INFO [BigbuyController] ✍️ Writing progress: 50000/344140 rows (14.5%)
---
[2025-05-22T11:55:27.123Z] INFO [BigbuyController] ✍️ Writing progress: 75000/344140 rows (21.8%)
---
[2025-05-22T11:55:27.129Z] INFO [BigbuyController] ✍️ Writing progress: 100000/344140 rows (29.1%)
---
[2025-05-22T11:55:27.133Z] INFO [BigbuyController] ✍️ Writing progress: 125000/344140 rows (36.3%)
---
[2025-05-22T11:55:27.136Z] INFO [BigbuyController] ✍️ Writing progress: 150000/344140 rows (43.6%)
---
[2025-05-22T11:55:27.142Z] INFO [BigbuyController] ✍️ Writing progress: 175000/344140 rows (50.9%)
---
[2025-05-22T11:55:27.146Z] INFO [BigbuyController] ✍️ Writing progress: 200000/344140 rows (58.1%)
---
[2025-05-22T11:55:27.149Z] INFO [BigbuyController] ✍️ Writing progress: 225000/344140 rows (65.4%)
---
[2025-05-22T11:55:27.152Z] INFO [BigbuyController] ✍️ Writing progress: 250000/344140 rows (72.6%)
---
[2025-05-22T11:55:27.157Z] INFO [BigbuyController] ✍️ Writing progress: 275000/344140 rows (79.9%)
---
[2025-05-22T11:55:27.162Z] INFO [BigbuyController] ✍️ Writing progress: 300000/344140 rows (87.2%)
---
[2025-05-22T11:55:27.244Z] INFO [BigbuyController] ✍️ Writing progress: 325000/344140 rows (94.4%)
---
[2025-05-22T11:55:27.247Z] INFO [BigbuyController] ✍️ Writing progress: 344140/344140 rows (100.0%)
---
[2025-05-22T11:55:45.852Z] INFO [BigbuyController] ✅ Successfully wrote all 344140 rows with price updates to temporary file
---
[2025-05-22T11:55:45.854Z] INFO [BigbuyController] Using stream-based file replacement for price updates...
---
[2025-05-22T11:55:45.857Z] INFO [BigbuyController] Creating backup of original file...
---
[2025-05-22T11:55:46.130Z] INFO [BigbuyController] Backup created at: data\downloads\bigbuy\bigbuy_combined.csv.price_backup
---
[2025-05-22T11:55:46.147Z] INFO [BigbuyController] Piping temporary price file to destination...
---
[2025-05-22T11:55:46.241Z] INFO [BigbuyController] Stream progress: 10MB / 323MB (3.09%)
---
[2025-05-22T11:55:46.272Z] INFO [BigbuyController] Stream progress: 20MB / 323MB (6.18%)
---
[2025-05-22T11:55:47.292Z] INFO [BigbuyController] Stream progress: 30MB / 323MB (9.27%)
---
[2025-05-22T11:55:47.637Z] INFO [BigbuyController] Stream progress: 40MB / 323MB (12.37%)
---
[2025-05-22T11:55:47.683Z] INFO [BigbuyController] Stream progress: 50MB / 323MB (15.46%)
---
[2025-05-22T11:55:47.727Z] INFO [BigbuyController] Stream progress: 60MB / 323MB (18.55%)
---
[2025-05-22T11:55:47.765Z] INFO [BigbuyController] Stream progress: 70MB / 323MB (21.64%)
---
[2025-05-22T11:55:47.853Z] INFO [BigbuyController] Stream progress: 80MB / 323MB (24.73%)
---
[2025-05-22T11:55:47.933Z] INFO [BigbuyController] Stream progress: 90MB / 323MB (27.82%)
---
[2025-05-22T11:55:48.068Z] INFO [BigbuyController] Stream progress: 100MB / 323MB (30.92%)
---
[2025-05-22T11:55:48.116Z] INFO [BigbuyController] Stream progress: 110MB / 323MB (34.01%)
---
[2025-05-22T11:55:48.163Z] INFO [BigbuyController] Stream progress: 120MB / 323MB (37.10%)
---
[2025-05-22T11:55:48.205Z] INFO [BigbuyController] Stream progress: 130MB / 323MB (40.19%)
---
[2025-05-22T11:55:48.234Z] INFO [BigbuyController] Stream progress: 140MB / 323MB (43.28%)
---
[2025-05-22T11:55:48.263Z] INFO [BigbuyController] Stream progress: 150MB / 323MB (46.37%)
---
[2025-05-22T11:55:48.294Z] INFO [BigbuyController] Stream progress: 160MB / 323MB (49.47%)
---
[2025-05-22T11:55:48.321Z] INFO [BigbuyController] Stream progress: 170MB / 323MB (52.56%)
---
[2025-05-22T11:55:48.348Z] INFO [BigbuyController] Stream progress: 180MB / 323MB (55.65%)
---
[2025-05-22T11:55:48.375Z] INFO [BigbuyController] Stream progress: 190MB / 323MB (58.74%)
---
[2025-05-22T11:55:48.404Z] INFO [BigbuyController] Stream progress: 200MB / 323MB (61.83%)
---
[2025-05-22T11:55:48.434Z] INFO [BigbuyController] Stream progress: 210MB / 323MB (64.92%)
---
[2025-05-22T11:55:48.469Z] INFO [BigbuyController] Stream progress: 220MB / 323MB (68.01%)
---
[2025-05-22T11:55:48.528Z] INFO [BigbuyController] Stream progress: 230MB / 323MB (71.11%)
---
[2025-05-22T11:55:48.564Z] INFO [BigbuyController] Stream progress: 240MB / 323MB (74.20%)
---
[2025-05-22T11:55:48.622Z] INFO [BigbuyController] Stream progress: 250MB / 323MB (77.29%)
---
[2025-05-22T11:55:48.677Z] INFO [BigbuyController] Stream progress: 260MB / 323MB (80.38%)
---
[2025-05-22T11:55:48.721Z] INFO [BigbuyController] Stream progress: 270MB / 323MB (83.47%)
---
[2025-05-22T11:55:48.761Z] INFO [BigbuyController] Stream progress: 280MB / 323MB (86.56%)
---
[2025-05-22T11:55:48.833Z] INFO [BigbuyController] Stream progress: 290MB / 323MB (89.66%)
---
[2025-05-22T11:55:48.876Z] INFO [BigbuyController] Stream progress: 300MB / 323MB (92.75%)
---
[2025-05-22T11:55:48.930Z] INFO [BigbuyController] Stream progress: 310MB / 323MB (95.84%)
---
[2025-05-22T11:55:48.976Z] INFO [BigbuyController] Stream progress: 320MB / 323MB (98.93%)
---
[2025-05-22T11:55:48.993Z] INFO [BigbuyController] ✅ Stream completed. Verifying file sizes...
---
[2025-05-22T11:55:48.996Z] INFO [BigbuyController] ✅ File sizes match (339173177 bytes). Replacement successful.
---
[2025-05-22T11:55:49.157Z] INFO [BigbuyController] Temporary price file deleted
---
[2025-05-22T11:55:49.234Z] INFO [BigbuyController] 🎉 Price combination completed successfully with stream approach!
---
[2025-05-22T11:55:49.236Z] INFO [BigbuyController] 📈 Final statistics:
---
[2025-05-22T11:55:49.238Z] INFO [BigbuyController] - Total rows processed: 344140
---
[2025-05-22T11:55:49.240Z] INFO [BigbuyController] - Prices updated: 344082 (99.98% of rows)
---
[2025-05-22T11:55:49.243Z] INFO [BigbuyController] - Updated file: data\downloads\bigbuy\bigbuy_combined.csv
---
[2025-05-22T11:55:49.245Z] INFO [BigbuyController] - File size: 331224 KB
---
[2025-05-22T11:55:49.371Z] INFO [BigbuyController] Price backup file deleted as replacement was successful