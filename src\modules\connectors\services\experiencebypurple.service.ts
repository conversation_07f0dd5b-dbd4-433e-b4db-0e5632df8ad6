import { ProductModel as BighubProductModel } from '../../bighub/models/product.model';
import { BIGHubCsvPort } from '../../bighub/ports/bighub-csv.port';
import { ProductModel } from '../../catalogue/models/product.model';
import { YamlRepo } from '../../configurations/repos/yaml.repo';
import { ExperiencebypurplePort } from '../ports/experiencebypurple.port';
import { ExperiencebypurpleModel } from '../models/experiencebypurple.model';
import { ConnectorCode } from '../enums/connector-code.enum';
import { ExperiencebypurpleMapper } from '../mappers/experiencebypurple.mapper';

export class ExperiencebypurpleService {
  experiencebypurpleRepo: ExperiencebypurplePort;
  bighubRepo: BIGHubCsvPort
  configuration: YamlRepo

  constructor(
    repo: ExperiencebypurplePort,
    bighubRepo: BIGHubCsvPort,
    configuration: YamlRepo
  ) {
    this.experiencebypurpleRepo = repo
    this.bighubRepo = bighubRepo
    this.configuration = configuration
  }

  async getProducts(): Promise<ProductModel[]> {
    const experiencebypurple = await this.experiencebypurpleRepo.getAll();
    return experiencebypurple.map((item: ExperiencebypurpleModel) => ExperiencebypurpleMapper
      .toProduct(item))
      .filter(product => product.stock > 0)
  }

  async getProductsBighub(): Promise<BighubProductModel[]> {
    const BIGHubServiceProducts = await this.experiencebypurpleRepo.getAll();

    let products = BIGHubServiceProducts.map((item: ExperiencebypurpleModel) => ExperiencebypurpleMapper.toProductBigHub(item))

    // products = await this.experiencebypurpleRepo.validateOffers(products)

    products = products.filter(product => product.ean !== '')
    // stock rules
    products = products.filter(product => product.stock > 0)
    // price rules
    products = products.filter(product => product.global_price > 0)
    // NaN's filter
    products = products.filter(product => !isNaN(product.global_price))
    // tax rules
    // 10 % discount in all products

    products = await this.bighubRepo.applyTaxes(products, ConnectorCode.EXPERIENCEBYPURPLE)

    return products;
  }
}