import { SupercompRepo } from './../../suppliers/repos/supercomp.repo';
import { ProductModel } from '../../bighub/models/product.model';
import { ConfigurationPort } from '../../configurations/ports/configuration.port';
import { MuchoCartuchoRepo } from '../../suppliers/repos/muchocartucho.repo';
import { CoolAccesoriosRepo } from '../../suppliers/repos/coolaccesorios.repo';
import { RamsonsRepo } from '../../suppliers/repos/ramsons.repo';
import { B2b1Repo } from '../../suppliers/repos/b2b1.repo';
import { BIGHubCsvRepo, Tax } from '../repos/bighub-csv.repo';
import { BeontimeRepo } from '../../suppliers/repos/beontime.repo';
import { ActibiosRepo } from '../../suppliers/repos/actibios.repo';
import { DreamloveRepo } from '../../suppliers/repos/dreamlove.repo';
import { HispamicroRepo } from '../../suppliers/repos/hispamicro.repo';
import { GrupoyaguRepo } from '../../suppliers/repos/grupoyagu.repo';
import { DispersajuguetesRepo } from '../../suppliers/repos/dispersajuguetes.repo';
import { AlcoRepo } from '../../suppliers/repos/alco.repo';
import { PortugalMarcasRepo } from '../../suppliers/repos/portugalmarcas.repo';
import { DmiRepo } from '../../suppliers/repos/dmi.repo';
import { DepauRepo } from '../../suppliers/repos/depau.repo';
import { EmucaRepo } from '../../suppliers/repos/emuca.repo';
import { MegasurRepo } from '../../suppliers/repos/megasur.repo';
import { ExcitasyRepo } from '../../suppliers/repos/excitasy.repo';
import { BtswholesalerRepo } from '../../suppliers/repos/btswholesaler.repo';
import { EsprinetRepo } from '../../suppliers/repos/esprinet.repo';
import { InfortisaRepo } from '../../suppliers/repos/infortisa.repo';
import { RibamundoRepo } from '../../suppliers/repos/ribamundo.repo';
import { PrietoRepo } from '../../suppliers/repos/prieto.repo';
import { InpexopcionRepo } from '../../suppliers/repos/inpexopcion.repo';
import { ProdacRepo } from '../../suppliers/repos/prodac.repo';
import { ZoodropRepo } from '../../suppliers/repos/zoodrop.repo';
import { Trends4CentsRepo } from '../../suppliers/repos/trends4cents.repo';
import { AseuropaRepo } from '../../suppliers/repos/aseuropa.repo';
import { GreeniceRepo } from '../../suppliers/repos/greenice.repo';
import { NextRepo } from '../../suppliers/repos/next.repo';
import { MakantRepo } from '../../suppliers/repos/makant.repo';
import { ApokinRepo } from '../../suppliers/repos/apokin.repo';
import { MaxcomRepo } from '../../suppliers/repos/maxcom.repo';
import { AlldisRepo } from '../../suppliers/repos/alldis.repo';
import { CyberportRepo } from '../../suppliers/repos/cyberport.repo';
import { GrutiRepo } from '../../suppliers/repos/gruti.repo';
import { HandytreffRepo } from '../../suppliers/repos/handytreff.repo';
import { JacobRepo } from '../../suppliers/repos/jacob.repo';
import { IndustryelectronicsRepo } from '../../suppliers/repos/industryelectronics.repo';
import { LilsRepo } from '../../suppliers/repos/lils.repo';
import { PurmaxRepo } from '../../suppliers/repos/purmax.repo';
import { DoctorbarkRepo } from '../../suppliers/repos/doctorbark.repo';
import { LameeRepo } from '../../suppliers/repos/lamee.repo';
import { Bbnet1Repo } from '../../suppliers/repos/bbnet1.repo';
import { Bbnet2Repo } from '../../suppliers/repos/bbnet2.repo';
import { OrionRepo } from '../../suppliers/repos/orion.repo';
import { WaveRepo } from '../../suppliers/repos/wave.repo';
import { SweetsinRepo } from '../../suppliers/repos/sweetsin.repo';
import { ZweygartRepo } from '../../suppliers/repos/zweygart.repo';
import { PowerundhandelRepo } from '../../suppliers/repos/powerundhandel.repo';
import { LinkuRepo } from '../../suppliers/repos/linku.repo';
import { OciostockRepo } from '../../suppliers/repos/ociostock.repo';
import { UniversalParfumsRepo } from '../../suppliers/repos/universalparfums.repo';
import { BucketUpload } from '@application/interfaces/bucket/bucket-upload';
import { CompuspainRepo } from 'src/modules/suppliers/repos/compuspain.repo';
import { UnimallRepo } from 'src/modules/suppliers/repos/unimall.repo';
import { FaroRepo } from 'src/modules/suppliers/repos/faro.repo';
import { DeloRepo } from 'src/modules/suppliers/repos/delo.repo';
import { BgsRepo } from 'src/modules/suppliers/repos/bgs.repo';
import { EetgroupRepo } from 'src/modules/suppliers/repos/eetgroup.repo';
import { BlitecRepo } from 'src/modules/suppliers/repos/blitec.repo';
import { HerweckRepo } from 'src/modules/suppliers/repos/herweck.repo';
import { KosatecRepo } from 'src/modules/suppliers/repos/kosatec.repo';
import { MemorypcRepo } from 'src/modules/suppliers/repos/memorypc.repo';
import { PhoeniximportRepo } from 'src/modules/suppliers/repos/phoeniximport.repo';
import { ReclineRepo } from 'src/modules/suppliers/repos/recline.repo';
import { LedboxRepo } from 'src/modules/suppliers/repos/ledbox.repo';
import { MapcoRepo } from 'src/modules/suppliers/repos/mapco.repo';
import { CttRepo } from 'src/modules/suppliers/repos/ctt.repo';
import { EurovideoRepo } from 'src/modules/suppliers/repos/eurovideo.repo';
import { AlcasaRepo } from 'src/modules/suppliers/repos/alcasa.repo';
import { EtreeRepo } from 'src/modules/suppliers/repos/etree.repo';
import { EetgroupdeRepo } from 'src/modules/suppliers/repos/eetgroupde.repo';
import { MacleRepo } from 'src/modules/suppliers/repos/macle.repo';
import { TalkskyRepo } from 'src/modules/suppliers/repos/talksky.repo';
import { XtremmediaRepo } from 'src/modules/suppliers/repos/xtremmedia.repo';
import { TmapRepo } from 'src/modules/suppliers/repos/tmap.repo';
import { BurkleRepo } from 'src/modules/suppliers/repos/burkle.repo';
import { EnoRepo } from 'src/modules/suppliers/repos/eno.repo';
import { GettygoRepo } from 'src/modules/suppliers/repos/gettygo.repo';
import { ApiRepo } from 'src/modules/suppliers/repos/api.repo';
import { AlsoRepo } from 'src/modules/suppliers/repos/also.repo';
import { VidaxlptRepo } from 'src/modules/suppliers/repos/vidaxlpt.repo';
import { ElinkRepo } from 'src/modules/suppliers/repos/elink.repo';
import { DghRepo } from 'src/modules/suppliers/repos/dgh.repo';
import { ManolyaRepo } from 'src/modules/suppliers/repos/manolya.repo';
import { SiewertundkauRepo } from 'src/modules/suppliers/repos/siewertundkau.repo';
import { AtomicwebRepo } from 'src/modules/suppliers/repos/atomicweb.repo';
import { NovaRepo } from 'src/modules/suppliers/repos/nova.repo';
import { ItversandRepo } from 'src/modules/suppliers/repos/itversand.repo';
import { LevamecontigoRepo } from 'src/modules/connectors/repos/levamecontigo.repo';
import { HeimbuchRepo } from 'src/modules/suppliers/repos/heimbuch.repo';
import { Heimbuch2Repo } from 'src/modules/suppliers/repos/heimbuch2.repo';
import { TdsynnexRepo } from 'src/modules/suppliers/repos/tdsynnex.repo';
import { BigpromoRepo } from 'src/modules/suppliers/repos/bigpromo.repo';
import { HyksRepo } from 'src/modules/suppliers/repos/hyks.repo';
import { AwartisanRepo } from 'src/modules/suppliers/repos/awartisan.repo';
import { AnovalojaRepo } from 'src/modules/connectors/repos/anovaloja.repo';
import { AuroxRepo } from 'src/modules/connectors/repos/aurox.repo';
import { AromaterapiaRepo } from 'src/modules/connectors/repos/aromaterapia.repo';
import { BeunikRepo } from 'src/modules/connectors/repos/beunik.repo';
import { BeiralbrandsRepo } from 'src/modules/connectors/repos/beiralbrands.repo';
import { BmeRepo } from 'src/modules/connectors/repos/bme.repo';
import { BricksandtricksRepo } from 'src/modules/connectors/repos/bricksandtricks.repo';
import { BrincatoysRepo } from 'src/modules/connectors/repos/brincatoys.repo';
import { CeramorRepo } from 'src/modules/connectors/repos/ceramor.repo';
import { CnobrestoreRepo } from 'src/modules/connectors/repos/cnobrestore.repo';
import { ConceitosbeautyRepo } from 'src/modules/connectors/repos/conceitosbeauty.repo';
import { LadybachRepo } from 'src/modules/connectors/repos/ladybach.repo';
import { MeuamorzinhoRepo } from 'src/modules/connectors/repos/meuamorzinho.repo';
import { PartypeoplesunglassesRepo } from 'src/modules/connectors/repos/partypeoplesunglasses.repo';
import { SplinkRepo } from 'src/modules/connectors/repos/splink.repo';
import { SuenosdeciguenaRepo } from 'src/modules/connectors/repos/suenosdeciguena.repo';
import { TiendaalvaroRepo } from 'src/modules/connectors/repos/tiendaalvaro.repo';
import { UnderwearzoneRepo } from 'src/modules/connectors/repos/underwearzone.repo';
import { UniversoRepo } from 'src/modules/connectors/repos/universo.repo';
import { VogafashionRepo } from 'src/modules/connectors/repos/vogafashion.repo';
import { WinebrokerRepo } from 'src/modules/connectors/repos/winebroker.repo';
import { GuardajoiasRepo } from 'src/modules/connectors/repos/guardajoias.repo';
import { FourpaperRepo } from 'src/modules/connectors/repos/fourpaper.repo';
import { FogododesejoRepo } from 'src/modules/connectors/repos/fogododesejo.repo';
import { MundoalfombraRepo } from 'src/modules/connectors/repos/mundoalfombra.repo';
import { JesuspradoRepo } from 'src/modules/connectors/repos/jesusprado.repo';
import { EkilikuahomeRepo } from 'src/modules/connectors/repos/ekilikuahome.repo';
import { AlvashopRepo } from 'src/modules/connectors/repos/alvashop.repo';
import { SanttoecoRepo } from 'src/modules/connectors/repos/santtoeco.repo';
import { SweetdreamsRepo } from 'src/modules/connectors/repos/sweetdreams.repo';
import { LovecherryRepo } from 'src/modules/suppliers/repos/lovecherry.repo';
import { FerreteriajuananaRepo } from 'src/modules/connectors/repos/ferreteriajuanana.repo';
import { EysaRepo } from 'src/modules/suppliers/repos/eysa.repo';
import { GadjetfactoryRepo } from 'src/modules/connectors/repos/gadjetfactory.repo';
import { BestelectronicsRepo } from 'src/modules/connectors/repos/bestelectronics.repo';
import { SherwoodRepo } from 'src/modules/suppliers/repos/sherwood.repo';
import { KingchurroRepo } from 'src/modules/connectors/repos/kingchurro.repo';

import got from 'got';
import { promisify } from 'util';
import stream from 'stream';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import * as fs from 'fs';
import * as path from 'path';
import { AngelRepo } from 'src/modules/suppliers/repos/angel.repo';
import { BigbuyRepo } from 'src/modules/suppliers/repos/bigbuy.repo';
import { BiciseguraRepo } from 'src/modules/connectors/repos/bicisegura.repo';
import { LisbonglamRepo } from 'src/modules/connectors/repos/lisbonglam.repo';
import { EstudioplastRepo } from 'src/modules/connectors/repos/estudioplast.repo';
import { PharmavidaRepo } from 'src/modules/connectors/repos/pharmavida.repo';
import { LojadosbebesRepo } from 'src/modules/suppliers/repos/lojadosbebes.repo';
import { VanguardpcRepo } from 'src/modules/connectors/repos/vanguardpc.repo';
import { LpknclothesRepo } from 'src/modules/connectors/repos/lpknclothes.repo';
import { InformaticapavonRepo } from 'src/modules/suppliers/repos/informaticapavon.repo';
import { SwitchtechnologyRepo } from 'src/modules/connectors/repos/switchtechnology.repo';
import { GlobalshoppingRepo } from 'src/modules/connectors/repos/globalshopping.repo';
import { FbeautyRepo } from 'src/modules/connectors/repos/fbeauty.repo';
import { RagatexRepo } from 'src/modules/suppliers/repos/ragatex.repo';
import { EneturalRepo } from 'src/modules/connectors/repos/enetural.repo';
import { HostelpakRepo } from 'src/modules/connectors/repos/hostelpak.repo';
import { LaboresbellaRepo } from 'src/modules/connectors/repos/laboresbella.repo';
import { BabykidsRepo } from 'src/modules/connectors/repos/babykids.repo';
import { ManchatoysRepo } from 'src/modules/connectors/repos/manchatoys.repo';
import { MusaRepo } from 'src/modules/connectors/repos/musa.repo';
import { ImperiodasmalasRepo } from 'src/modules/connectors/repos/imperiodasmalas.repo';
import { CerdaRepo } from 'src/modules/suppliers/repos/cerda.repo';
import { ExperiencebypurpleRepo } from 'src/modules/connectors/repos/experiencebypurple.repo';
import { FitnisRepo } from 'src/modules/connectors/repos/fitnis.repo';
import { KomsaRepo } from 'src/modules/suppliers/repos/komsa.repo';
import { IdirectoRepo } from 'src/modules/suppliers/repos/idirecto.repo';
import { EsferasossegadaRepo } from 'src/modules/connectors/repos/esferasossegada.repo';
import { BeautyfortRepo } from 'src/modules/suppliers/repos/beautyfort.repo';
import { SilicongirlsRepo } from 'src/modules/connectors/repos/silicongirls.repo';
import { HeimdallRepo } from 'src/modules/connectors/repos/heimdall.repo';
import { FerrimexRepo } from 'src/modules/connectors/repos/ferrimex.repo';
import { DjthomasRepo } from 'src/modules/connectors/repos/djthomas.repo';
import { TujugueteeroticoRepo } from 'src/modules/connectors/repos/tujugueteerotico.repo';
import { XtratusRepo } from 'src/modules/connectors/repos/xtratus.repo';
import { DumicorRepo } from 'src/modules/connectors/repos/dumicor.repo';
import { BigbuyStockRepo } from 'src/modules/suppliers/repos/bigbuy-stock.repo';
import { CastroesilvaRepo } from 'src/modules/connectors/repos/castroesilva.repo';
import { RosmarinusRepo } from 'src/modules/connectors/repos/rosmarinus.repo';
import { BigbuyPriceRepo } from 'src/modules/suppliers/repos/bigbuy-price.repo';
import { PartimpimRepo } from 'src/modules/connectors/repos/partimpim.repo';
import { SibaRepo } from 'src/modules/connectors/repos/siba.repo';
import { JardimsuspensoRepo } from 'src/modules/connectors/repos/jardimsuspenso.repo';

const pipeline = promisify(stream.pipeline);
const unlinkAsync = promisify(fs.unlink);

export class BigHubService {
  bigCsvRepo: BIGHubCsvRepo
  muchoCartuchoRepo: MuchoCartuchoRepo
  coolAccesoriosRepo: CoolAccesoriosRepo
  ramsonsRepo: RamsonsRepo
  b2b1Repo: B2b1Repo
  supercompRepo: SupercompRepo
  beontimeRepo: BeontimeRepo
  actibiosRepo: ActibiosRepo
  hispamicroRepo: HispamicroRepo
  grupoyaguRepo: GrupoyaguRepo
  dispersajuguetesRepo: DispersajuguetesRepo
  alcoRepo: AlcoRepo
  portugalmarcasRepo: PortugalMarcasRepo
  dmiRepo: DmiRepo
  depauRepo: DepauRepo
  emucaRepo: EmucaRepo
  megasurRepo: MegasurRepo
  excitasyRepo: ExcitasyRepo
  dreamloveRepo: DreamloveRepo
  btswholesalerRepo: BtswholesalerRepo
  esprinetRepo: EsprinetRepo
  infortisaRepo: InfortisaRepo
  ribamundoRepo: RibamundoRepo
  prietoRepo: PrietoRepo
  inpexopcionRepo: InpexopcionRepo
  prodacRepo: ProdacRepo
  zoodropRepo: ZoodropRepo
  trends4centsRepo: Trends4CentsRepo
  aseuropaRepo: AseuropaRepo
  greeniceRepo: GreeniceRepo
  nextRepo: NextRepo
  makantRepo: MakantRepo
  apokinRepo: ApokinRepo
  maxcomRepo: MaxcomRepo
  alldisRepo: AlldisRepo
  cyberportRepo: CyberportRepo
  grutiRepo: GrutiRepo
  handytreffRepo: HandytreffRepo
  jacobRepo: JacobRepo
  industryelectronicsRepo: IndustryelectronicsRepo
  purmaxRepo: PurmaxRepo
  lilsRepo: LilsRepo
  doctorbarkRepo: DoctorbarkRepo
  lameeRepo: LameeRepo
  bbnet1Repo: Bbnet1Repo
  bbnet2Repo: Bbnet2Repo
  orionRepo: OrionRepo
  waveRepo: WaveRepo
  sweetsinRepo: SweetsinRepo
  zweygartRepo: ZweygartRepo
  powerundhandelRepo: PowerundhandelRepo
  linkuRepo: LinkuRepo
  ociostockRepo: OciostockRepo
  universalparfumsRepo: UniversalParfumsRepo
  compuspainRepo: CompuspainRepo
  unimallRepo: UnimallRepo
  faroRepo: FaroRepo
  deloRepo: DeloRepo
  bgsRepo: BgsRepo
  eetgroupRepo: EetgroupRepo
  blitecRepo: BlitecRepo
  herweckRepo: HerweckRepo
  kosatecRepo: KosatecRepo
  memorypcRepo: MemorypcRepo
  phoeniximportRepo: PhoeniximportRepo
  reclineRepo: ReclineRepo
  ledboxRepo: LedboxRepo
  mapcoRepo: MapcoRepo
  cttRepo: CttRepo
  eurovideoRepo: EurovideoRepo
  alcasaRepo: AlcasaRepo
  etreeRepo: EtreeRepo
  eetgroupdeRepo: EetgroupdeRepo
  macleRepo: MacleRepo
  talkskyRepo: TalkskyRepo
  xtremmediaRepo: XtremmediaRepo
  tmapRepo: TmapRepo
  burkleRepo: BurkleRepo
  enoRepo: EnoRepo
  gettygoRepo: GettygoRepo
  apiRepo: ApiRepo
  alsoRepo: AlsoRepo
  vidaxlptRepo: VidaxlptRepo
  elinkRepo: ElinkRepo
  dghRepo: DghRepo
  manolyaRepo: ManolyaRepo
  siewertundkauRepo: SiewertundkauRepo
  atomicwebRepo: AtomicwebRepo
  novaRepo: NovaRepo
  iteversandRepo: ItversandRepo
  heimbuchRepo: HeimbuchRepo
  heimbuch2Repo: Heimbuch2Repo
  tdsynnexRepo: TdsynnexRepo
  bigpromoRepo: BigpromoRepo
  hyksRepo: HyksRepo
  awartisanRepo: AwartisanRepo
  lovecherryRepo: LovecherryRepo
  eysaRepo: EysaRepo
  sherwoodRepo: SherwoodRepo
  angelRepo: AngelRepo
  lojadosbebesRepo: LojadosbebesRepo
  informaticapavonRepo: InformaticapavonRepo
  ragatexRepo: RagatexRepo
  cerdaRepo: CerdaRepo
  komsaRepo: KomsaRepo
  idirectoRepo: IdirectoRepo
  beatuyfortRepo: BeautyfortRepo
  bigbuyRepo: BigbuyRepo
  bigbuystockRepo: BigbuyStockRepo
  bigbuypriceRepo: BigbuyPriceRepo
  // Connectors Shopify
  anovalojaRepo: AnovalojaRepo
  aromaterapiaRepo: AromaterapiaRepo
  auroxRepo: AuroxRepo
  beiralbrandsRepo: BeiralbrandsRepo
  beunikRepo: BeunikRepo
  bmeRepo: BmeRepo
  bricksandtricksRepo: BricksandtricksRepo
  brincatoysRepo: BrincatoysRepo
  ceramorRepo: CeramorRepo
  cnobrestoreRepo: CnobrestoreRepo
  conceitosbeautyRepo: ConceitosbeautyRepo
  ladybachRepo: LadybachRepo
  levamecontigoRepo: LevamecontigoRepo
  meuamorzinhoRepo: MeuamorzinhoRepo
  partypeoplesunglassesRepo: PartypeoplesunglassesRepo
  splinkRepo: SplinkRepo
  suenosdeciguenaRepo: SuenosdeciguenaRepo
  tiendaalvaroRepo: TiendaalvaroRepo
  underwearzoneRepo: UnderwearzoneRepo
  universoRepo: UniversoRepo
  vogafashionRepo: VogafashionRepo
  winebrokerRepo: WinebrokerRepo
  fogododesejoRepo: FogododesejoRepo
  mundoalfombraRepo: MundoalfombraRepo
  alvashopRepo: AlvashopRepo
  lpknclothesRepo: LpknclothesRepo
  globalshoppingRepo: GlobalshoppingRepo
  fbeautyRepo: FbeautyRepo
  manchatoysRepo: ManchatoysRepo
  heimdallRepo: HeimdallRepo
  xtratusRepo: XtratusRepo
  dumicorRepo: DumicorRepo
  rosmarinusRepo: RosmarinusRepo
  // Connectors Prestashop
  guardajoiasRepo: GuardajoiasRepo
  ferreteriajuananaRepo: FerreteriajuananaRepo
  ferrimexRepo: FerrimexRepo
  tujugueteeroticoRepo: TujugueteeroticoRepo
  jardimsuspensoRepo: JardimsuspensoRepo
  // Connectors: Kuantokusta
  fourpaperRepo: FourpaperRepo
  partimpimRepo: PartimpimRepo
  switchtechnologyRepo: SwitchtechnologyRepo
  ekilikuahomeRepo: EkilikuahomeRepo
  pharmavidaRepo: PharmavidaRepo
  esferasossegadaRepo: EsferasossegadaRepo
  fitnisRepo: FitnisRepo
  castroesilvaRepo: CastroesilvaRepo
  // Connectors: WooCommerce
  jesuspradoRepo: JesuspradoRepo
  gadjetfactoryRepo: GadjetfactoryRepo
  kingchurroRepo: KingchurroRepo
  biciseguraRepo: BiciseguraRepo
  vanguardpcRepo: VanguardpcRepo
  hostelpakRepo: HostelpakRepo
  babykidsRepo: BabykidsRepo
  musaRepo: MusaRepo
  silicongirlsRepo: SilicongirlsRepo
  sibaRepo: SibaRepo
  // Connectors: Shopify
  santtoecoRepo: SanttoecoRepo
  sweetdreamsRepo: SweetdreamsRepo
  lisbonglamRepo: LisbonglamRepo
  // Connectors: Weasy
  bestelectronicsRepo: BestelectronicsRepo
  // XML
  estudioplastRepo: EstudioplastRepo
  eneturalRepo: EneturalRepo
  laboresbellaRepo: LaboresbellaRepo
  // Connectors: CS Cart
  imperiodasmalasRepo: ImperiodasmalasRepo
  // Jumpseller
  experiencebypurpleRepo: ExperiencebypurpleRepo
  // Fourthwall
  djthomasRepo: DjthomasRepo

  configuration: ConfigurationPort
  private static s3Client: S3Client

  constructor(
    private readonly bucketUploadUseCase: BucketUpload.UseCase,
    config: ConfigurationPort,
    bigCsvRepo: BIGHubCsvRepo,
    muchoCartuchoRepo: MuchoCartuchoRepo,
    coolAccesoriosRepo: CoolAccesoriosRepo,
    ramsonsRepo: RamsonsRepo,
    b2b1Repo: B2b1Repo,
    supercompRepo: SupercompRepo,
    beontimeRepo: BeontimeRepo,
    actibiosRepo: ActibiosRepo,
    dreamloveRepo: DreamloveRepo,
    hispamicroRepo: HispamicroRepo,
    grupoyaguRepo: GrupoyaguRepo,
    dispersajuguetesRepo: DispersajuguetesRepo,
    alcoRepo: AlcoRepo,
    portugalmarcasRepo: PortugalMarcasRepo,
    dmiRepo: DmiRepo,
    depauRepo: DepauRepo,
    emucaRepo: EmucaRepo,
    megasurRepo: MegasurRepo,
    excitasyRepo: ExcitasyRepo,
    btswholesalerRepo: BtswholesalerRepo,
    esprinetRepo: EsprinetRepo,
    infortisaRepo: InfortisaRepo,
    ribamundoRepo: RibamundoRepo,
    prietoRepo: PrietoRepo,
    inpexopcionRepo: InpexopcionRepo,
    prodacRepo: ProdacRepo,
    zoodropRepo: ZoodropRepo,
    trends4centsRepo: Trends4CentsRepo,
    aseuropaRepo: AseuropaRepo,
    greeniceRepo: GreeniceRepo,
    nextRepo: NextRepo,
    makantRepo: MakantRepo,
    apokinRepo: ApokinRepo,
    maxcomRepo: MaxcomRepo,
    alldisRepo: AlldisRepo,
    cyberportRepo: CyberportRepo,
    grutiRepo: GrutiRepo,
    handytreffRepo: HandytreffRepo,
    jacobRepo: JacobRepo,
    industryelectronicsRepo: IndustryelectronicsRepo,
    lilsRepo: LilsRepo,
    purmaxRepo: PurmaxRepo,
    doctorbarkRepo: DoctorbarkRepo,
    lameeRepo: LameeRepo,
    bbnet1Repo: Bbnet1Repo,
    bbnet2Repo: Bbnet2Repo,
    orionRepo: OrionRepo,
    waveRepo: WaveRepo,
    sweetsinRepo: SweetsinRepo,
    zweygartRepo: ZweygartRepo,
    powerundhandelRepo: PowerundhandelRepo,
    linkuRepo: LinkuRepo,
    ociostockRepo: OciostockRepo,
    universalparfumsRepo: UniversalParfumsRepo,
    compuspainRepo: CompuspainRepo,
    unimallRepo: UnimallRepo,
    faroRepo: FaroRepo,
    deloRepo: DeloRepo,
    bgsRepo: BgsRepo,
    eetgroupRepo: EetgroupRepo,
    blitecRepo: BlitecRepo,
    herweckRepo: HerweckRepo,
    kosatecRepo: KosatecRepo,
    memorypcRepo: MemorypcRepo,
    phoeniximportRepo: PhoeniximportRepo,
    reclineRepo: ReclineRepo,
    ledboxRepo: LedboxRepo,
    mapcoRepo: MapcoRepo,
    cttRepo: CttRepo,
    eurovideoRepo: EurovideoRepo,
    alcasaRepo: AlcasaRepo,
    etreeRepo: EtreeRepo,
    eetgroupdeRepo: EetgroupdeRepo,
    macleRepo: MacleRepo,
    talkskyRepo: TalkskyRepo,
    xtremmediaRepo: XtremmediaRepo,
    tmapRepo: TmapRepo,
    burkleRepo: BurkleRepo,
    enoRepo: EnoRepo,
    gettygoRepo: GettygoRepo,
    apiRepo: ApiRepo,
    alsoRepo: AlsoRepo,
    vidaxlptRepo: VidaxlptRepo,
    elinkRepo: ElinkRepo,
    dghRepo: DghRepo,
    manolyaRepo: ManolyaRepo,
    siewertundkauRepo: SiewertundkauRepo,
    atomicwebRepo: AtomicwebRepo,
    novaRepo: NovaRepo,
    itversandRepo: ItversandRepo,
    heimbuchRepo: HeimbuchRepo,
    heimbuch2Repo: Heimbuch2Repo,
    tdsynnexRepo: TdsynnexRepo,
    bigpromoRepo: BigpromoRepo,
    hyksRepo: HyksRepo,
    awartisanRepo: AwartisanRepo,
    lovecherryRepo: LovecherryRepo,
    eysaRepo: EysaRepo,
    sherwoodRepo: SherwoodRepo,
    angelRepo: AngelRepo,
    lojadosbebesRepo: LojadosbebesRepo,
    informaticapavonRepo: InformaticapavonRepo,
    ragatexRepo: RagatexRepo,
    cerdaRepo: CerdaRepo,
    komsaRepo: KomsaRepo,
    idirectoRepo: IdirectoRepo,
    beatuyfortRepo: BeautyfortRepo,
    bigbuyRepo: BigbuyRepo,
    bigbuystockRepo: BigbuyStockRepo,
    bigbuypriceRepo: BigbuyPriceRepo,
    // Connectors Shopify
    anovalojaRepo: AnovalojaRepo,
    aromaterapiaRepo: AromaterapiaRepo,
    auroxRepo: AuroxRepo,
    beiralbrandsRepo: BeiralbrandsRepo,
    beunikRepo: BeunikRepo,
    bmeRepo: BmeRepo,
    bricksandtricksRepo: BricksandtricksRepo,
    brincatoysRepo: BrincatoysRepo,
    ceramorRepo: CeramorRepo,
    cnobrestoreRepo: CnobrestoreRepo,
    conceitosbeautyRepo: ConceitosbeautyRepo,
    ladybachRepo: LadybachRepo,
    levamecontigoRepo: LevamecontigoRepo,
    meuamorzinhoRepo: MeuamorzinhoRepo,
    partypeoplesunglassesRepo: PartypeoplesunglassesRepo,
    splinkRepo: SplinkRepo,
    suenosdeciguenaRepo: SuenosdeciguenaRepo,
    tiendaalvaroRepo: TiendaalvaroRepo,
    underwearzoneRepo: UnderwearzoneRepo,
    universoRepo: UniversoRepo,
    vogafashionRepo: VogafashionRepo,
    winebrokerRepo: WinebrokerRepo,
    fogododesejoRepo: FogododesejoRepo,
    mundoalfombraRepo: MundoalfombraRepo,
    alvashopRepo: AlvashopRepo,
    lpknclothesRepo: LpknclothesRepo,
    globalshoppingRepo: GlobalshoppingRepo,
    fbeautyRepo: FbeautyRepo,
    manchatoysRepo: ManchatoysRepo,
    heimdallRepo: HeimdallRepo,
    xtratusRepo: XtratusRepo,
    dumicorRepo: DumicorRepo,
    rosmarinusRepo: RosmarinusRepo,
    // Connectors Prestashop
    guardajoiasRepo: GuardajoiasRepo,
    ferreteriajuananaRepo: FerreteriajuananaRepo,
    ferrimexRepo: FerrimexRepo,
    tujugueteeroticoRepo: TujugueteeroticoRepo,
    jardimsuspensoRepo: JardimsuspensoRepo,
    // Connectors KunantoKusta
    fourpaperRepo: FourpaperRepo,
    partimpimRepo: PartimpimRepo,
    switchtechnologyRepo: SwitchtechnologyRepo,
    ekilikuahomeRepo: EkilikuahomeRepo,
    pharmavidaRepo: PharmavidaRepo,
    esferasossegadaRepo: EsferasossegadaRepo,
    fitnisRepo: FitnisRepo,
    castroesilvaRepo: CastroesilvaRepo,
    // Connectors WooCommerce
    jesuspradoRepo: JesuspradoRepo,
    gadjetfactoryRepo: GadjetfactoryRepo,
    kingchurroRepo: KingchurroRepo,
    biciseguraRepo: BiciseguraRepo,
    vanguardpcRepo: VanguardpcRepo,
    hostelpakRepo: HostelpakRepo,
    babykidsRepo: BabykidsRepo,
    musaRepo: MusaRepo,
    silicongirlsRepo: SilicongirlsRepo,
    sibaRepo: SibaRepo,
    // Connectors Shopify
    santtoecoRepo: SanttoecoRepo,
    sweetdreamsRepo: SweetdreamsRepo,
    lisbonglamRepo: LisbonglamRepo,
    // Connectors Weasy
    bestelectronicsRepo: BestelectronicsRepo,
    // XML
    estudioplastRepo: EstudioplastRepo,
    eneturalRepo: EneturalRepo,
    laboresbellaRepo: LaboresbellaRepo,
    // Connectors CS Cart
    imperiodasmalasRepo: ImperiodasmalasRepo,
    // Jumpseller
    experiencebypurpleRepo: ExperiencebypurpleRepo,
    // Fourthwall
    djthomasRepo: DjthomasRepo
  ) {

    this.configuration = config,
    this.bigCsvRepo = bigCsvRepo
    this.muchoCartuchoRepo = muchoCartuchoRepo
    this.coolAccesoriosRepo = coolAccesoriosRepo
    this.ramsonsRepo = ramsonsRepo
    this.b2b1Repo = b2b1Repo
    this.supercompRepo = supercompRepo
    this.beontimeRepo = beontimeRepo
    this.actibiosRepo = actibiosRepo
    this.dreamloveRepo = dreamloveRepo
    this.hispamicroRepo = hispamicroRepo
    this.grupoyaguRepo = grupoyaguRepo
    this.dispersajuguetesRepo = dispersajuguetesRepo
    this.alcoRepo = alcoRepo
    this.portugalmarcasRepo = portugalmarcasRepo
    this.dmiRepo = dmiRepo
    this.depauRepo = depauRepo
    this.emucaRepo = emucaRepo
    this.megasurRepo = megasurRepo
    this.excitasyRepo = excitasyRepo
    this.btswholesalerRepo = btswholesalerRepo
    this.esprinetRepo = esprinetRepo
    this.infortisaRepo = infortisaRepo
    this.ribamundoRepo = ribamundoRepo
    this.prietoRepo = prietoRepo
    this.inpexopcionRepo = inpexopcionRepo
    this.prodacRepo = prodacRepo
    this.zoodropRepo = zoodropRepo
    this.trends4centsRepo = trends4centsRepo
    this.aseuropaRepo = aseuropaRepo
    this.greeniceRepo = greeniceRepo
    this.nextRepo = nextRepo
    this.makantRepo = makantRepo
    this.apokinRepo = apokinRepo
    this.maxcomRepo = maxcomRepo
    this.alldisRepo = alldisRepo
    this.cyberportRepo = cyberportRepo
    this.grutiRepo = grutiRepo
    this.handytreffRepo = handytreffRepo
    this.jacobRepo = jacobRepo
    this.industryelectronicsRepo = industryelectronicsRepo
    this.lilsRepo = lilsRepo
    this.purmaxRepo = purmaxRepo
    this.doctorbarkRepo = doctorbarkRepo
    this.lameeRepo = lameeRepo
    this.bbnet1Repo = bbnet1Repo
    this.bbnet2Repo = bbnet2Repo
    this.orionRepo = orionRepo
    this.waveRepo = waveRepo
    this.sweetsinRepo = sweetsinRepo
    this.zweygartRepo = zweygartRepo
    this.powerundhandelRepo = powerundhandelRepo
    this.linkuRepo = linkuRepo
    this.ociostockRepo = ociostockRepo
    this.universalparfumsRepo = universalparfumsRepo
    this.compuspainRepo = compuspainRepo
    this.unimallRepo = unimallRepo
    this.faroRepo = faroRepo
    this.deloRepo = deloRepo
    this.bgsRepo = bgsRepo
    this.eetgroupRepo = eetgroupRepo
    this.blitecRepo = blitecRepo
    this.herweckRepo = herweckRepo
    this.kosatecRepo = kosatecRepo
    this.memorypcRepo = memorypcRepo
    this.phoeniximportRepo = phoeniximportRepo
    this.reclineRepo = reclineRepo
    this.ledboxRepo = ledboxRepo
    this.mapcoRepo = mapcoRepo
    this.cttRepo = cttRepo
    this.eurovideoRepo = eurovideoRepo
    this.alcasaRepo = alcasaRepo
    this.etreeRepo = etreeRepo
    this.eetgroupdeRepo = eetgroupdeRepo
    this.macleRepo = macleRepo
    this.talkskyRepo = talkskyRepo
    this.xtremmediaRepo = xtremmediaRepo
    this.tmapRepo = tmapRepo
    this.burkleRepo = burkleRepo
    this.enoRepo = enoRepo
    this.gettygoRepo = gettygoRepo
    this.apiRepo = apiRepo
    this.alsoRepo = alsoRepo
    this.vidaxlptRepo = vidaxlptRepo
    this.elinkRepo = elinkRepo
    this.dghRepo = dghRepo
    this.manolyaRepo = manolyaRepo
    this.siewertundkauRepo = siewertundkauRepo
    this.atomicwebRepo = atomicwebRepo
    this.novaRepo = novaRepo
    this.iteversandRepo = itversandRepo
    this.heimbuchRepo = heimbuchRepo
    this.heimbuch2Repo = heimbuch2Repo
    this.tdsynnexRepo = tdsynnexRepo
    this.bigpromoRepo = bigpromoRepo
    this.hyksRepo = hyksRepo
    this.awartisanRepo = awartisanRepo
    this.lovecherryRepo = lovecherryRepo
    this.eysaRepo = eysaRepo
    this.sherwoodRepo = sherwoodRepo
    this.angelRepo = angelRepo
    this.lojadosbebesRepo = lojadosbebesRepo
    this.informaticapavonRepo = informaticapavonRepo
    this.ragatexRepo = ragatexRepo
    this.cerdaRepo = cerdaRepo
    this.komsaRepo = komsaRepo
    this.idirectoRepo = idirectoRepo
    this.beatuyfortRepo = beatuyfortRepo
    this.bigbuyRepo = bigbuyRepo
    this.bigbuystockRepo = bigbuystockRepo
    this.bigbuypriceRepo = bigbuypriceRepo
    // Connectors Shopify
    this.anovalojaRepo = anovalojaRepo
    this.aromaterapiaRepo = aromaterapiaRepo
    this.auroxRepo = auroxRepo
    this.beiralbrandsRepo = beiralbrandsRepo
    this.beunikRepo = beunikRepo
    this.bmeRepo = bmeRepo
    this.bricksandtricksRepo = bricksandtricksRepo
    this.brincatoysRepo = brincatoysRepo
    this.ceramorRepo = ceramorRepo
    this.cnobrestoreRepo = cnobrestoreRepo
    this.conceitosbeautyRepo = conceitosbeautyRepo
    this.ladybachRepo = ladybachRepo
    this.levamecontigoRepo = levamecontigoRepo
    this.meuamorzinhoRepo = meuamorzinhoRepo
    this.partypeoplesunglassesRepo = partypeoplesunglassesRepo
    this.splinkRepo = splinkRepo
    this.suenosdeciguenaRepo = suenosdeciguenaRepo
    this.tiendaalvaroRepo = tiendaalvaroRepo
    this.underwearzoneRepo = underwearzoneRepo
    this.universoRepo = universoRepo
    this.vogafashionRepo = vogafashionRepo
    this.winebrokerRepo = winebrokerRepo
    this.fogododesejoRepo = fogododesejoRepo
    this.mundoalfombraRepo = mundoalfombraRepo
    this.alvashopRepo = alvashopRepo
    this.lpknclothesRepo = lpknclothesRepo
    this.globalshoppingRepo = globalshoppingRepo
    this.fbeautyRepo = fbeautyRepo
    this.manchatoysRepo = manchatoysRepo
    this.heimdallRepo = heimdallRepo
    this.xtratusRepo = xtratusRepo
    this.dumicorRepo = dumicorRepo
    this.rosmarinusRepo = rosmarinusRepo
    // Connectors Prestashop
    this.guardajoiasRepo = guardajoiasRepo
    this.ferreteriajuananaRepo = ferreteriajuananaRepo
    this.ferrimexRepo = ferrimexRepo
    this.tujugueteeroticoRepo = tujugueteeroticoRepo
    this.jardimsuspensoRepo = jardimsuspensoRepo
    // Connectors Custom Kuantokusta
    this.fourpaperRepo = fourpaperRepo
    this.partimpimRepo = partimpimRepo
    // Connectors Custom
    this.switchtechnologyRepo = switchtechnologyRepo
    this.ekilikuahomeRepo = ekilikuahomeRepo
    this.pharmavidaRepo = pharmavidaRepo
    this.esferasossegadaRepo = esferasossegadaRepo
    this.fitnisRepo = fitnisRepo
    this.castroesilvaRepo = castroesilvaRepo
    // Connectors WooCommerce
    this.jesuspradoRepo = jesuspradoRepo
    this.gadjetfactoryRepo = gadjetfactoryRepo
    this.kingchurroRepo = kingchurroRepo
    this.biciseguraRepo = biciseguraRepo
    this.vanguardpcRepo = vanguardpcRepo
    this.hostelpakRepo = hostelpakRepo
    this.babykidsRepo = babykidsRepo
    this.musaRepo = musaRepo
    this.silicongirlsRepo = silicongirlsRepo
    this.sibaRepo = sibaRepo
    // Connectors Shopify
    this.santtoecoRepo = santtoecoRepo
    this.sweetdreamsRepo = sweetdreamsRepo
    this.lisbonglamRepo = lisbonglamRepo
    // Connectors Weasy
    this.bestelectronicsRepo = bestelectronicsRepo
    // Connectors XML
    this.estudioplastRepo = estudioplastRepo
    this.eneturalRepo = eneturalRepo
    this.laboresbellaRepo = laboresbellaRepo
    // Connectors CS Cart
    this.imperiodasmalasRepo = imperiodasmalasRepo
    // Jumpseller
    this.experiencebypurpleRepo = experiencebypurpleRepo
    // Fourthwall
    this.djthomasRepo = djthomasRepo

    // Initialize S3 client
    BigHubService.s3Client = new S3Client({
      credentials: {
        accessKeyId: 'HHG2NYNFNXW3F4BBWD2E', // HHG2NYNFNXW3F4BBWD2E
        secretAccessKey: 'BgBDHQKDBGfyhKHX7wVxzzvHIqOoHEW6HJbZpnCX', // BgBDHQKDBGfyhKHX7wVxzzvHIqOoHEW6HJbZpnCX
      },
      region: 'eu-west-1',
      endpoint: 'https://ams1.vultrobjects.com/',
      forcePathStyle: true,
    });
  }

  supplierStreamCsv(products: ProductModel[]) {
    return this.bigCsvRepo.streamCsv(products);
  }

  async supplierCreateCsv(filename: string, products: ProductModel[], userId: number) {
    await this.bigCsvRepo.put(filename, products);
    // Also upload to the bighub ftp
    const arg = { file: filename, path: `/store-prd/batch/${userId}/${filename}` }
    await this.bucketUploadUseCase.execute(arg)
    // Upload the generated CSV file to internal bucket
    await this.uploadCSVToS3(filename, userId);
  }

  async supplierAppendCsv(filename: string, products: ProductModel[], userId?: number) {
    // Upload the updated CSV file to S3
    await this.uploadCSVToS3(filename, userId);
    return this.bigCsvRepo.append(filename, products);
  }

 /**
 * Uploads an existing CSV file to the S3 'internal' bucket in a date-based folder structure
 * @param filename Name of the CSV file to upload
 * @param clientId ID of the client to be included in the folder path
 */
  private async uploadCSVToS3(filename: string, clientId?: number): Promise<string> {
    try {
      console.log(`Uploading existing CSV ${filename} to S3 internal bucket...`);

      // Assuming the CSV is in the root directory
      const filePath = path.resolve(process.cwd(), filename);

      // Check if file exists
      if (!fs.existsSync(filePath)) {
        throw new Error(`CSV file not found at ${filePath}`);
      }

      // Read the file content
      const fileContent = fs.readFileSync(filePath);

      // Generate date folder in day-month-year format
      const today = new Date();
      const day = String(today.getDate()).padStart(2, '0');
      const month = String(today.getMonth() + 1).padStart(2, '0'); // Months are 0-based
      const year = today.getFullYear();
      const dateFolder = `${day}-${month}-${year}`;

      // Add timestamp to filename
      const filenameWithTimestamp = BigHubService.addTimestampToFilename(filename);

      // Include client ID in the path if provided
      const clientFolder = clientId ? `${clientId}/` : '';

      // Set up the upload parameters with the client-id and date folder structure
      const uploadParams = {
        Bucket: 'internal',
        Key: `${clientFolder}${dateFolder}/${filenameWithTimestamp}`, // Put file in client/date folder structure
        Body: fileContent,
        ContentType: 'text/csv',
      };

      // Upload to S3
      const command = new PutObjectCommand(uploadParams);
      await BigHubService.s3Client.send(command);

      // Return the public URL
      const publicUrl = `https://ams1.vultrobjects.com/internal/${clientFolder}${dateFolder}/${filenameWithTimestamp}`;
      console.log(`CSV uploaded successfully to internal bucket. URL: ${publicUrl}`);
      return publicUrl;
    } catch (error) {
      console.error('Error uploading CSV to S3:', error);
      throw error;
    }
  }

  /**
   * Uploads a file to the S3 bucket in a client-id and date-based folder structure
   * Can be called by any repository that needs to upload files to S3
   * 
   * @param filePath Full path to the file to upload
   * @param customFilename Optional custom filename to use in S3 (if not provided, uses original filename)
   * @param bucket Optional bucket name (defaults to 'seller')
   * @param clientId Optional client ID to be included in the folder path
   * @returns Promise with the public URL of the uploaded file
   */
  static async uploadFileToS3(
    filePath: string,
    customFilename?: string,
    bucket: string = 'seller',
    clientId?: number
  ): Promise<string> {
    try {
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found at ${filePath}`);
      }

      // Get the filename from the path if no custom filename is provided
      const filename = customFilename || path.basename(filePath);

      console.log(`Uploading file ${filename} to S3 ${bucket} bucket...`);

      // Read the file content
      const fileContent = fs.readFileSync(filePath);

      // Generate date folder in day-month-year format
      const today = new Date();
      const day = String(today.getDate()).padStart(2, '0');
      const month = String(today.getMonth() + 1).padStart(2, '0'); // Months are 0-based
      const year = today.getFullYear();
      const dateFolder = `${day}-${month}-${year}`;

      // Include client ID in the path if provided
      const clientFolder = clientId ? `${clientId}/` : '';
      const filenameWithTimestamp = this.addTimestampToFilename(`${filename}.csv`);
      // Set up the upload parameters with the client-id and date folder structure
      const uploadParams = {
        Bucket: bucket,
        Key: `${clientFolder}${dateFolder}/${filenameWithTimestamp}`, // Put file in client/date folder structure
        Body: fileContent,
        ContentType: 'text/csv',
      };

      // Upload to S3
      const command = new PutObjectCommand(uploadParams);
      await this.s3Client.send(command);

      // Return the public URL
      const publicUrl = `https://ams1.vultrobjects.com/${bucket}/${clientFolder}${dateFolder}/${filenameWithTimestamp}`;
      console.log(`File uploaded successfully to ${bucket} bucket. URL: ${publicUrl}`);
      return publicUrl;
    } catch (error) {
      console.error('Error uploading file to S3:', error);
      throw error;
    }
  }

  /**
   * Downloads a file from a URL and uploads it to S3, then deletes the local file
   * 
   * @param url URL to download the file from
   * @param filenamePrefix Prefix for the generated filename
   * @param bucket Optional bucket name (defaults to 'seller')
   * @param clientId Optional client ID to be included in the folder path
   * @returns Promise with the public URL of the uploaded file
   */
  static async downloadAndUploadToS3(
    url: string,
    filenamePrefix: string,
    bucket: string = 'seller',
    clientId?: number
  ): Promise<string> {
    console.log(url);
    const filenameWithTimestamp = this.addTimestampToFilename(`${filenamePrefix}.csv`);
    const filePath = path.resolve(process.cwd(), filenameWithTimestamp);

    try {
      // Download the file
      console.log(`Downloading file from ${url} to ${filePath}`);
      const downloadStream = got.stream(url);
      const fileWriteStream = fs.createWriteStream(filePath);
      await pipeline(downloadStream, fileWriteStream);
      console.log('File downloaded successfully');

      // Upload to S3
      const s3Url = await this.uploadFileToS3(filePath, filenameWithTimestamp, bucket, clientId);

      // Delete local file
      await unlinkAsync(filePath);
      console.log(`Deleted local file: ${filePath}`);

      return s3Url;
    } catch (error) {
      console.error('Error in downloading/uploading to S3:', error);

      // Clean up the file if it exists and there was an error
      if (fs.existsSync(filePath)) {
        try {
          await unlinkAsync(filePath);
          console.log(`Cleaned up file after error: ${filePath}`);
        } catch (cleanupError) {
          console.error('Error cleaning up file:', cleanupError);
        }
      }

      throw error;
    }
  }

  private static addTimestampToFilename(filename: string): string {
    const now = new Date();
    const timestamp = now.toLocaleTimeString('pt-PT', { hour12: false }).replace(/:/g, '-');

    // Split filename and extension
    const extension = '.csv'
    const baseFilename = filename.substring(0, filename.length - extension.length);

    // Add timestamp and return
    return `${baseFilename}_${timestamp}${extension}`;
  }
}