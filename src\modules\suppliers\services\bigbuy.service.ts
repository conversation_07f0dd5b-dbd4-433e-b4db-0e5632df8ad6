import { ProductModel as BighubProductModel } from '../../bighub/models/product.model';
import { BIGHubCsvPort } from '../../bighub/ports/bighub-csv.port';
import { ProductModel } from '../../catalogue/models/product.model';
import { SupplierCode } from '../enums/supplier-code.enum';
import { BigbuyMapper } from '../mappers/bigbuy.mapper';
import { BigbuyModel } from '../models/bigbuy.model';
import { BigbuyPort } from '../ports/bigbuy.port';
import { defer } from '@shared/utils/promise.util';
import * as fs from 'fs';
import * as path from 'path';

export class BigbuyService {
  bigbuyRepo: BigbuyPort;
  bighubRepo: BIGHubCsvPort;
  manufacturerMap: Record<string, string> = {};

  constructor(
    repo: BigbuyPort,
    bighubRepo: BIGHubCsvPort,
  ) {
    this.bigbuyRepo = repo;
    this.bighubRepo = bighubRepo;
    // Load manufacturer mapping on service initialization
    this.loadManufacturerMapping();
  }

  /**
   * Loads the manufacturer ID-to-name mapping from the JSON file
   */
  private loadManufacturerMapping(): void {
    try {
      // Path to the manufacturer mapping JSON file
      const mappingFilePath = path.resolve(__dirname, '../jsons/manufacturers/manufacturers.json');
      
      // Read and parse the JSON file
      const jsonData = fs.readFileSync(mappingFilePath, 'utf8');
      this.manufacturerMap = JSON.parse(jsonData);
      
      console.log(`Loaded manufacturer mapping with ${Object.keys(this.manufacturerMap).length} entries`);
    } catch (error) {
      console.error('Error loading manufacturer mapping:', error);
      // Initialize with empty object if file can't be loaded
      this.manufacturerMap = {};
    }
  }

  getManufacturerName(id: string): string {
    return this.manufacturerMap[id] || 'Unknown Manufacturer';
  }

  async getProducts(): Promise<ProductModel[]> {
    return []
  }

  async readCsvFile(filename: string): Promise<BighubProductModel[]> {
    const deferrred = defer()
        
    const rows: BigbuyModel[] = []

    this.bigbuyRepo.readCsvFile(filename, (row: BigbuyModel) => {
      rows.push(row)
    }, () => deferrred.resolve())

    await deferrred.promise
    console.log(`${rows.length} - rows`)

    let products: BighubProductModel[] = []

    // Map rows to products and enrich with manufacturer names
    products = rows.map((item: BigbuyModel) => {
      const product = BigbuyMapper.toBIGHubProduct(item);
      
      // Get manufacturer name from the lookup map based on brand ID
      if (product.brand) {
        // Assign manufacturer name based on brand ID
        product.brand = this.getManufacturerName(product.brand);
      }
      
      return product;
    })
    
    console.log(`${products.length} - products`)

    // required fields rules
    products = products.filter(product => product.ean !== '') 
    console.log(`${products.length} - after ean filter`)
    // stock rules
    //products = products.filter(product => product.stock > 0)
    console.log(`${products.length} - after stock filter`)
    // price rules
    products = products.filter(product => product.global_price > 0)
    console.log(`${products.length} - after price filter`)
    // NaN's filter
    products = products.filter(product => !isNaN(product.global_price))
    console.log(`${products.length} - after NaN filter`)
    // tax rules  
    products = await this.bighubRepo.applyTaxes(products, SupplierCode.BIGBUY)

    return products
  }

  async getProductsBighub(): Promise<string[]> {
    try {
      let files = await this.bigbuyRepo.getAll();

      if (files.length === 0) {
        throw new Error('No products found')
      }
  
      return files
    } catch (error: any) {
      console.error(error)
      return []
    }
  }
}