export interface HeimdallModel {
    id?: number;
    provider: string;
    ean: string;
    sku: string;
    title: string;
    description: string;
    brand: string;
    price: number;
    stock: number;
    condition: string;
    image: string;
    weight: number;
    variants: any[];
    vendor: string;
    body_html: string;
    tags?: string;  // Added tags property based on your JSON example
    product_type?: string;
    images?: any[];
    status?: string;
}