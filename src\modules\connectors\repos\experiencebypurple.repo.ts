import { ExperiencebypurpleModel } from '../models/experiencebypurple.model';
import got from 'got';
import { YamlRepo } from '../../configurations/repos/yaml.repo';
import { ExperiencebypurplePort } from '../ports/experiencebypurple.port';
import { ProductModel } from 'src/modules/bighub/models/product.model';

export class ExperiencebypurpleRepo implements ExperiencebypurplePort {
  private rows: ExperiencebypurpleModel[] = [];
  private configuration: YamlRepo;

  constructor(configuration: YamlRepo) {
    this.configuration = configuration;
  }
  
  validateOffers(products: ProductModel[]): Promise<ProductModel[]> {
    throw new Error('Method not implemented.');
  }

  async getAll(): Promise<ExperiencebypurpleModel[]> {
    return new Promise(async (resolve, reject) => {
      try {
        const loginCredentials = this.configuration.get('conexions', 'connector', 'experiencebypurple');
        const apiUrl = loginCredentials.api_url || 'https://api.jumpseller.com/v1';
        const login = loginCredentials.login;
        const authtoken = loginCredentials.authtoken;

        // Jumpseller uses pagination via page parameter
        let page = 1;
        const limit = 100; // Maximum allowed by Jumpseller API
        let hasMoreProducts = true;

        while (hasMoreProducts) {
          const url = `${apiUrl}/products.json?page=${page}&limit=${limit}`;
          
          const response = await got(url, {
            responseType: 'json',
            username: login,
            password: authtoken,
            headers: {
              'Content-Type': 'application/json'
            }
          });

          const products = (response.body as any[]) || [];

          if (!products || products.length === 0) {
            hasMoreProducts = false;
            break;
          }

          // Process each product
          products.forEach(productWrapper => {
            // Jumpseller format has product data inside a "product" property
            if (productWrapper.product) {
              this.processLine(productWrapper.product);
            }
          });

          // Check if we have more pages
          if (products.length < limit) {
            hasMoreProducts = false;
          } else {
            page += 1;
          }
        }

        resolve(this.rows);
      } catch (error) {
        console.error('Error fetching Jumpseller products:', error);
        reject(error);
      }
    });
  }

  private processLine(product: any) {
    const imagePlaceholder = 'https://cdn.bighub.store/image/product-placeholder.png';

    // Extract first variant if exists, otherwise use main product data
    const variant = product.variants && product.variants.length > 0 
      ? product.variants[0].variant 
      : null;

    // Get the first image if available
    const firstImage = product.images && product.images.length > 0 
      ? product.images[0].image 
      : null;
    
    const imageSrc = firstImage ? firstImage.url : imagePlaceholder;

    const newRow: ExperiencebypurpleModel = {
      ...product,
      id: product.id,
      ean: variant ? variant.barcode : '',
      sku: variant ? variant.sku : product.sku || '',
      title: product.name || '',
      description: product.description ? product.description.replace(/\n/g, '').replace(/\r/g, '') : '',
      brand: product.brand || '',
      price: variant ? parseFloat(variant.price) : parseFloat(product.price || '0'),
      stock: variant ? variant.stock : product.stock || 0,
      image: imageSrc,
      weight: variant ? variant.weight : product.weight || 0,
    };

    this.rows.push(newRow);
  }
}