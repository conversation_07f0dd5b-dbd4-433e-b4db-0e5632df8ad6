import { ProductModel as BighubProductModel } from '../../bighub/models/product.model';
import { BIGHubCsvPort } from '../../bighub/ports/bighub-csv.port';
import { ProductModel } from '../../catalogue/models/product.model';
import { YamlRepo } from '../../configurations/repos/yaml.repo';
import { PartimpimPort } from '../ports/partimpim.port';
import { PartimpimModel } from '../models/partimpim.model';
import { ConnectorCode } from '../enums/connector-code.enum';
import { PartimpimMapper } from '../mappers/partimpim.mapper';

export class PartimpimService {
  partimpimRepo: PartimpimPort;
  bighubRepo: BIGHubCsvPort

  constructor(
    repo: PartimpimPort,
    bighubRepo: BIGHubCsvPort,
  ) {
    this.partimpimRepo = repo
    this.bighubRepo = bighubRepo
  }

  async getProducts(): Promise<ProductModel[]> {
    const partimpim = await this.partimpimRepo.getAll();
    return partimpim.map((item: PartimpimModel) => PartimpimMapper
      .toProduct(item))
      .filter(product => product.stock > 0)
  }

  async getProductsBighub(): Promise<BighubProductModel[]> {
    const BIGHubServiceProducts = await this.partimpimRepo.getAll();

    let products = BIGHubServiceProducts.map((item: PartimpimModel) => PartimpimMapper.toProductBigHub(item))

    // products = await this.partimpimRepo.validateOffers(products)

    products = products.filter(product => product.ean !== '')
    console.log('Partimpim products with EAN filter', products.length)
    // stock rules
    products = products.filter(product => product.stock > 0)
    console.log('Partimpim products with stock filter', products.length)
    // price rules
    products = products.filter(product => product.global_price > 0)
    console.log('Partimpim products with price filter', products.length)
    // NaN's filter
    products = products.filter(product => !isNaN(product.global_price))
    console.log('Partimpim products with NaN filter', products.length)
    // tax rules
    // 10 % discount in all products

    products = await this.bighubRepo.applyTaxes(products, ConnectorCode.PARTIMPIM)

    return products;
  }
}