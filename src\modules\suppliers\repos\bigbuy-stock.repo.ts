import { BigbuyStockModel } from '../models/bigbuy-stock.model';
import { BigbuyStockPort } from '../ports/bigbuy-stock.port';
import got from 'got';
import * as yaml from 'js-yaml';
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join } from 'path';

interface BigBuyConfig {
    supplier: string;
    api_token: string;
    api_url: string;
    test_mode: boolean;
}

interface ConfigFile {
    conexions: BigBuyConfig[];
}

interface RequestState {
    lastRequest: number;
    totalRequests: number;
}

export class BigbuyStockRepo implements BigbuyStockPort {
    private baseUrl: string;
    private accessToken: string;
    private testMode: boolean;
    private stocks: BigbuyStockModel[] = [];
    private requestState: RequestState;
    private readonly REQUEST_INTERVAL = 30000; // 30 seconds between requests
    
    // Optimized page sizes to maximize data per request
    private readonly PRODUCTS_PAGE_SIZE = 50000; // Maximum allowed
    private readonly VARIATIONS_PAGE_SIZE = 50000; // Maximum allowed

    constructor(configPath: string = './config.yml') {
        const config = this.loadConfig(configPath);
        this.baseUrl = `${config.api_url}/rest/catalog`;
        this.accessToken = config.api_token;
        this.testMode = config.test_mode;
        
        // Initialize request state
        this.requestState = this.loadRequestState();
        
        console.log(`BigBuy Stock Repository initialized`);
        console.log(`- API URL: ${this.baseUrl}`);
        console.log(`- Test Mode: ${this.testMode}`);
        console.log(`- Request Interval: ${this.REQUEST_INTERVAL / 1000} seconds between requests`);
    }

    private loadConfig(configPath: string): BigBuyConfig {
        try {
            const fileContents = readFileSync(configPath, 'utf8');
            const config = yaml.load(fileContents) as ConfigFile;
            
            const bigbuyConfig = config.conexions.find(
                connection => connection.supplier === 'bigbuy-stock'
            );
            
            if (!bigbuyConfig) {
                throw new Error('BigBuy stock configuration not found in YAML file');
            }
            
            // Validate required fields
            if (!bigbuyConfig.api_token || !bigbuyConfig.api_url) {
                throw new Error('Missing required BigBuy configuration fields (api_token, api_url)');
            }
            
            return bigbuyConfig;
        } catch (error) {
            console.error('Error loading BigBuy configuration:', error);
            throw error;
        }
    }

    private loadRequestState(): RequestState {
        try {
            const statePath = './data/bigbuy-request-state.json';
            if (existsSync(statePath)) {
                const state = JSON.parse(readFileSync(statePath, 'utf8'));
                return {
                    lastRequest: state.lastRequest || 0,
                    totalRequests: state.totalRequests || 0
                };
            }
        } catch (error) {
            console.warn('Could not load request state, starting fresh:', error);
        }
        
        return {
            lastRequest: 0,
            totalRequests: 0
        };
    }

    private saveRequestState(): void {
        try {
            const statePath = './data/bigbuy-request-state.json';
            writeFileSync(statePath, JSON.stringify(this.requestState, null, 2));
        } catch (error) {
            console.warn('Could not save request state:', error);
        }
    }

    private async waitForInterval(): Promise<void> {
        const now = Date.now();
        const timeSinceLastRequest = now - this.requestState.lastRequest;
        
        if (timeSinceLastRequest < this.REQUEST_INTERVAL) {
            const waitTime = this.REQUEST_INTERVAL - timeSinceLastRequest;
            console.log(`⏳ Waiting ${Math.ceil(waitTime / 1000)} seconds before next request...`);
            await this.delay(waitTime);
        }
    }

    private async makeApiRequest(url: string, params: any = {}): Promise<any> {
        // Wait for interval if needed
        await this.waitForInterval();
        
        try {
            console.log(`🔄 Making API request...`);
            const startTime = Date.now();
            
            const response = await got.get(url, {
                headers: {
                    'Authorization': `Bearer ${this.accessToken}`,
                    'Content-Type': 'application/json'
                },
                searchParams: params,
                responseType: 'json',
                timeout: {
                    request: 30000 // 30 second timeout
                }
            });

            // Update request state
            this.requestState.lastRequest = Date.now();
            this.requestState.totalRequests++;
            this.saveRequestState();

            const requestTime = (Date.now() - startTime) / 1000;
            console.log(`✅ Request completed in ${requestTime.toFixed(2)}s (Total requests: ${this.requestState.totalRequests})`);
            
            return response.body;
        } catch (error: any) {
            // Handle 429 (Too Many Requests) specifically
            if (error.response?.statusCode === 429) {
                console.log('⚠️  Received 429 Too Many Requests - waiting 60 seconds and retrying...');
                await this.delay(60000); // Wait 1 minute
                return await this.makeApiRequest(url, params); // Retry
            }
            
            console.error('Request failed:', error.message);
            throw error;
        }
    }

    clear(): void {
        this.stocks = [];
    }

    async getAllProductsStock(): Promise<BigbuyStockModel[]> {
        this.stocks = [];
        let page = 0;
        let hasMoreData = true;
        let totalProcessed = 0;

        console.log(`\n=== Starting Products Stock Fetch ===`);
        console.log(`Target: ~350,000 products`);
        console.log(`Page size: ${this.PRODUCTS_PAGE_SIZE}`);
        console.log(`Estimated pages needed: ${Math.ceil(350000 / this.PRODUCTS_PAGE_SIZE)}`);
        console.log(`Request interval: ${this.REQUEST_INTERVAL / 1000} seconds\n`);

        try {
            while (hasMoreData) {
                console.log(`\n--- Fetching products stock page ${page} ---`);
                
                const stockData = await this.makeApiRequest(
                    `${this.baseUrl}/productsstockbyhandlingdays.json`,
                    {
                        page: page,
                        pageSize: this.PRODUCTS_PAGE_SIZE
                    }
                );
                
                if (stockData && Array.isArray(stockData) && stockData.length > 0) {
                    // Process and add stock data
                    stockData.forEach((item: any) => {
                        const stockModel: BigbuyStockModel = {
                            id: item.id,
                            sku: item.sku,
                            stocks: item.stocks?.map((stock: any) => ({
                                quantity: stock.quantity,
                                minHandlingDays: stock.minHandlingDays,
                                maxHandlingDays: stock.maxHandlingDays,
                                warehouse: stock.warehouse
                            })) || []
                        };
                        this.stocks.push(stockModel);
                    });
                    
                    totalProcessed += stockData.length;
                    console.log(`✓ Processed ${stockData.length} products from page ${page}`);
                    console.log(`✓ Total products processed: ${totalProcessed}`);
                    console.log(`✓ Progress: ${((totalProcessed / 350000) * 100).toFixed(2)}%`);
                    
                    page++;
                    
                    // Check if we have less data than pageSize, meaning we've reached the end
                    if (stockData.length < this.PRODUCTS_PAGE_SIZE) {
                        hasMoreData = false;
                        console.log(`✓ Reached end of product stock data`);
                    }
                } else {
                    hasMoreData = false;
                    console.log(`✓ No more product stock data available`);
                }
            }

            console.log(`\n=== Products Stock Fetch Complete ===`);
            console.log(`Total products with stock data: ${this.stocks.length}`);
            return this.stocks;

        } catch (error) {
            console.error('Error fetching product stock data:', error);
            throw error;
        }
    }

    async getProductVariationsStock(): Promise<BigbuyStockModel[]> {
        const variationStocks: BigbuyStockModel[] = [];
        let page = 0;
        let hasMoreData = true;
        let totalProcessed = 0;

        console.log(`\n=== Starting Variations Stock Fetch ===`);
        console.log(`Page size: ${this.VARIATIONS_PAGE_SIZE}`);

        try {
            while (hasMoreData) {
                console.log(`\n--- Fetching variations stock page ${page} ---`);
                
                const stockData = await this.makeApiRequest(
                    `${this.baseUrl}/productsvariationsstockbyhandlingdays.json`,
                    {
                        page: page,
                        pageSize: this.VARIATIONS_PAGE_SIZE
                    }
                );
                
                if (stockData && Array.isArray(stockData) && stockData.length > 0) {
                    stockData.forEach((item: any) => {
                        const stockModel: BigbuyStockModel = {
                            id: item.id,
                            sku: item.sku,
                            stocks: item.stocks?.map((stock: any) => ({
                                quantity: stock.quantity,
                                minHandlingDays: stock.minHandlingDays,
                                maxHandlingDays: stock.maxHandlingDays,
                                warehouse: stock.warehouse
                            })) || []
                        };
                        variationStocks.push(stockModel);
                    });
                    
                    totalProcessed += stockData.length;
                    console.log(`✓ Processed ${stockData.length} variations from page ${page}`);
                    console.log(`✓ Total variations processed: ${totalProcessed}`);
                    
                    page++;
                    
                    if (stockData.length < this.VARIATIONS_PAGE_SIZE) {
                        hasMoreData = false;
                        console.log(`✓ Reached end of variation stock data`);
                    }
                } else {
                    hasMoreData = false;
                    console.log(`✓ No more variation stock data available`);
                }
            }

            console.log(`\n=== Variations Stock Fetch Complete ===`);
            console.log(`Total variations with stock data: ${variationStocks.length}`);
            return variationStocks;

        } catch (error) {
            console.error('Error fetching variation stock data:', error);
            throw error;
        }
    }

    async getProductStockById(productId: string): Promise<BigbuyStockModel | null> {
        try {
            const stockData = await this.makeApiRequest(
                `${this.baseUrl}/productstockbyhandlingdays/${productId}.json`
            );
            
            if (stockData) {
                return {
                    id: stockData.id,
                    sku: stockData.sku,
                    stocks: stockData.stocks?.map((stock: any) => ({
                        quantity: stock.quantity,
                        minHandlingDays: stock.minHandlingDays,
                        maxHandlingDays: stock.maxHandlingDays,
                        warehouse: stock.warehouse
                    })) || []
                };
            }

            return null;

        } catch (error) {
            console.error(`Error fetching stock for product ${productId}:`, error);
            throw error;
        }
    }

    async getProductVariationStockById(variationId: string): Promise<BigbuyStockModel | null> {
        try {
            const stockData = await this.makeApiRequest(
                `${this.baseUrl}/productvariationsstockbyhandlingdays/${variationId}.json`
            );
            
            if (stockData) {
                return {
                    id: stockData.id,
                    sku: stockData.sku,
                    stocks: stockData.stocks?.map((stock: any) => ({
                        quantity: stock.quantity,
                        minHandlingDays: stock.minHandlingDays,
                        maxHandlingDays: stock.maxHandlingDays,
                        warehouse: stock.warehouse
                    })) || []
                };
            }

            return null;

        } catch (error) {
            console.error(`Error fetching stock for variation ${variationId}:`, error);
            throw error;
        }
    }

    // Additional method for pagination with start page (useful for resuming)
    async getAllProductsStockFromPage(startPage: number = 0): Promise<BigbuyStockModel[]> {
        const products: BigbuyStockModel[] = [];
        let page = startPage;
        let hasMoreData = true;
        let totalProcessed = 0;

        console.log(`\n=== Resuming Products Stock Fetch from page ${startPage} ===`);

        try {
            while (hasMoreData) {
                console.log(`\n--- Fetching products stock page ${page} ---`);
                
                const stockData = await this.makeApiRequest(
                    `${this.baseUrl}/productsstockbyhandlingdays.json`,
                    {
                        page: page,
                        pageSize: this.PRODUCTS_PAGE_SIZE
                    }
                );
                
                if (stockData && Array.isArray(stockData) && stockData.length > 0) {
                    stockData.forEach((item: any) => {
                        const stockModel: BigbuyStockModel = {
                            id: item.id,
                            sku: item.sku,
                            stocks: item.stocks?.map((stock: any) => ({
                                quantity: stock.quantity,
                                minHandlingDays: stock.minHandlingDays,
                                maxHandlingDays: stock.maxHandlingDays,
                                warehouse: stock.warehouse
                            })) || []
                        };
                        products.push(stockModel);
                    });
                    
                    totalProcessed += stockData.length;
                    console.log(`✓ Processed ${stockData.length} products from page ${page}`);
                    console.log(`✓ Total products processed: ${totalProcessed}`);
                    
                    page++;
                    
                    if (stockData.length < this.PRODUCTS_PAGE_SIZE) {
                        hasMoreData = false;
                        console.log(`✓ Reached end of product stock data`);
                    }
                } else {
                    hasMoreData = false;
                    console.log(`✓ No more product stock data available`);
                }
            }

            console.log(`\n=== Products Stock Fetch Complete ===`);
            console.log(`Total products with stock data: ${products.length}`);
            return products;

        } catch (error) {
            console.error('Error fetching product stock data:', error);
            throw error;
        }
    }

    // Method to get all stock (products + variations) in one call
    async getAllStock(): Promise<{ products: BigbuyStockModel[]; variations: BigbuyStockModel[] }> {
        console.log('\n=== Starting Complete Stock Fetch ===');
        
        // First get products
        const products = await this.getAllProductsStock();
        
        // Then get variations
        const variations = await this.getProductVariationsStock();
        
        console.log('\n=== Complete Stock Fetch Finished ===');
        console.log(`Total products: ${products.length}`);
        console.log(`Total variations: ${variations.length}`);
        console.log(`Total items: ${products.length + variations.length}`);
        
        return {
            products,
            variations
        };
    }

    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Additional utility methods
    getConfig(): { baseUrl: string; testMode: boolean } {
        return {
            baseUrl: this.baseUrl,
            testMode: this.testMode
        };
    }

    isTestMode(): boolean {
        return this.testMode;
    }

    getRequestStatus(): RequestState & { timeSinceLastRequest: number } {
        const now = Date.now();
        const timeSinceLastRequest = now - this.requestState.lastRequest;
        
        return {
            ...this.requestState,
            timeSinceLastRequest
        };
    }

    async estimateFullSyncTime(): Promise<{ estimatedMinutes: number; estimatedRequests: number }> {
        const totalProducts = 350000;
        const totalPages = Math.ceil(totalProducts / this.PRODUCTS_PAGE_SIZE);
        const estimatedRequests = totalPages + 5; // Add buffer for variations
        const estimatedMinutes = Math.ceil((estimatedRequests * this.REQUEST_INTERVAL) / 1000 / 60);
        
        return {
            estimatedMinutes,
            estimatedRequests
        };
    }

    // Reset request state (useful for testing)
    resetRequestState(): void {
        this.requestState = {
            lastRequest: 0,
            totalRequests: 0
        };
        this.saveRequestState();
        console.log('Request state reset');
    }

    // Check if we can make a request right now
    canMakeRequestNow(): boolean {
        const now = Date.now();
        const timeSinceLastRequest = now - this.requestState.lastRequest;
        return timeSinceLastRequest >= this.REQUEST_INTERVAL;
    }

    // Get seconds until next request is allowed
    getSecondsUntilNextRequest(): number {
        const now = Date.now();
        const timeSinceLastRequest = now - this.requestState.lastRequest;
        const remainingWait = this.REQUEST_INTERVAL - timeSinceLastRequest;
        return Math.max(0, Math.ceil(remainingWait / 1000));
    }
}