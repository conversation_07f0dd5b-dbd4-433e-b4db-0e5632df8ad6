import { ProductModel as BighubProductModel } from '../../bighub/models/product.model';
import { BIGHubCsvPort } from '../../bighub/ports/bighub-csv.port';
import { ProductModel } from '../../catalogue/models/product.model';
import { YamlRepo } from '../../configurations/repos/yaml.repo';
import { SibaPort } from '../ports/siba.port';
import { SibaModel } from '../models/siba.model';
import { ConnectorCode } from '../enums/connector-code.enum';
import { SibaMapper } from '../mappers/siba.mapper';

export class SibaService {
  sibaRepo: SibaPort;
  bighubRepo: BIGHubCsvPort
  configuration: YamlRepo

  constructor(
    repo: SibaPort,
    bighubRepo: BIGHubCsvPort,
    configuration: YamlRepo
  ) {
    this.sibaRepo = repo
    this.bighubRepo = bighubRepo
    this.configuration = configuration
  }

  async getProducts(): Promise<ProductModel[]> {
    const siba = await this.sibaRepo.getAll();
    return siba.map((item: SibaModel) => SibaMapper
      .toProduct(item))
      .filter(product => product.stock > 0)
  }

  async getProductsBighub(): Promise<BighubProductModel[]> {
    const BIGHubServiceProducts = await this.sibaRepo.getAll();

    let products = BIGHubServiceProducts.map((item: SibaModel) => SibaMapper.toProductBigHub(item))

    products = products.filter(product => product.ean !== '')
    console.log('After ean filter:', products.length)
    
    // stock rules 
    products = products.filter(product => product.stock > 0)
    console.log('After stock filter:', products.length)
    // price rules
    products = products.filter(product => product.global_price > 0)
    console.log('After price filter:', products.length)
    // NaN's filter
    products = products.filter(product => !isNaN(product.global_price))
    console.log('After NaN filter:', products.length)
    // tax rules
    products = await this.bighubRepo.applyTaxes(products, ConnectorCode.SIBA)

    return products;
  }
}