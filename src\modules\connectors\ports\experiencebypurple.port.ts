import { ProductModel } from 'src/modules/bighub/models/product.model';
import { ExperiencebypurpleModel } from '../models/experiencebypurple.model';

export interface ExperiencebypurplePort {
  /**
   * Retrieves all products from the Jumpseller store
   * @returns Promise containing an array of ExperiencebypurpleModel products
   */
  getAll(): Promise<ExperiencebypurpleModel[]>;
  
  /**
   * Validates if the products are available in the Jumpseller store
   * @param products Array of products to validate
   * @returns Promise containing validated products
   */
  validateOffers(products: ProductModel[]): Promise<ProductModel[]>;
  
  /**
   * Optional: Get a single product by ID
   * @param id The product ID to retrieve
   */
  // getById?(id: number): Promise<ExperiencebypurpleModel | null>;
  
  /**
   * Optional: Search products by criteria
   * @param query The search query parameters
   */
  // search?(query: string, fields?: string[]): Promise<ExperiencebypurpleModel[]>;
}