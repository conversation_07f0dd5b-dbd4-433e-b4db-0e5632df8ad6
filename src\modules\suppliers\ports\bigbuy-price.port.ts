import { BigbuyPriceModel } from '../models/bigbuy-price.model';

export interface BigbuyPricePort {
    clear(): void;
    getAllProductsPrices(includeLargeQuantities?: boolean): Promise<BigbuyPriceModel[]>;
    getProductVariationPrices(includeLargeQuantities?: boolean): Promise<BigbuyPriceModel[]>;
    getProductPriceById(productId: string, includeLargeQuantities?: boolean): Promise<BigbuyPriceModel | null>;
    getProductVariationPriceById(variationId: string, includeLargeQuantities?: boolean): Promise<BigbuyPriceModel | null>;
}