import { Request, Response } from 'express';
import { BigHubService } from '../../../bighub/services/bighub.service';
import { SibaService } from '../../services/siba.service';
import fs from 'fs'

export class SibaController {

  sibaService: SibaService;
  bigHubService: BigHubService;
  
  constructor(bigHubService: BigHubService, sibaService: SibaService) {
    // coolaccesorios service
    this.sibaService = sibaService;
    // bighub service
    this.bigHubService = bigHubService;
  }
  
  async getProducts(req: Request, res: Response) {
    let response = {
      status: 200,
      message: 'Products found'
    }

    try {
      const products = await this.sibaService.getProducts();
      res.status(response.status).json(products)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubCsv(req: Request, res: Response) {
    try {
      const filename = '1459-bighub-siba.csv';
      res.setHeader('Content-disposition', 'attachment; filename=' + filename);
      res.setHeader('Content-type', 'text/csv');
      fs.createReadStream(filename).pipe(res)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubExportCsv(req: Request, res: Response) {
    try {
      const filename = '1459-bighub-siba.csv';
      const products = await this.sibaService.getProductsBighub();
      this.bigHubService.supplierCreateCsv(filename, products, 1459); //1459

      res.status(200).send('ok');
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }
}