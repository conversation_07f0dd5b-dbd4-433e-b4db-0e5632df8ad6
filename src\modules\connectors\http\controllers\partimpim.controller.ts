import { Request, Response } from 'express';
import { BigHubService } from '../../../bighub/services/bighub.service';
import { PartimpimService } from '../../services/partimpim.service';
import fs from 'fs'

export class PartimpimController {

  partimpimService: PartimpimService;
  bigHubService: BigHubService;
  
  constructor(bigHubService: BigHubService, partimpimService: PartimpimService) {
    // coolaccesorios service
    this.partimpimService = partimpimService;
    // bighub service
    this.bigHubService = bigHubService;
  }
  
  async getProducts(req: Request, res: Response) {
    let response = {
      status: 200,
      message: 'Products found'
    }

    try {
      const products = await this.partimpimService.getProducts();
      res.status(response.status).json(products)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubCsv(req: Request, res: Response) {
    try {
      const filename = '1-bighub-partimpim.csv';
      res.setHeader('Content-disposition', 'attachment; filename=' + filename);
      res.setHeader('Content-type', 'text/csv');
      fs.createReadStream(filename).pipe(res)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubExportCsv(req: Request, res: Response) {
    try {
      const filename = '1-bighub-partimpim.csv';
      const products = await this.partimpimService.getProductsBighub();
      this.bigHubService.supplierCreateCsv(filename, products, 1); //1

      res.status(200).send('ok');
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }
}