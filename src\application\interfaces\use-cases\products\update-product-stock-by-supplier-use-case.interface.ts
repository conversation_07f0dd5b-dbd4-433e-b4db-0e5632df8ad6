import { ProductProps } from '../../../../domain/entities/product'
import { UseCase } from '../use-case.interface'

export interface UpdateProductStockBySupplierUseCaseInterface 
  extends UseCase<UpdateProductStockBySupplierUseCaseInterface.Request, UpdateProductStockBySupplierUseCaseInterface.Response> {
  execute(request: UpdateProductStockBySupplierUseCaseInterface.Request): Promise<UpdateProductStockBySupplierUseCaseInterface.Response>
}

export namespace UpdateProductStockBySupplierUseCaseInterface {
  export type Request = Pick<ProductProps, 'stock' | 'supplier'>
  export type Response = boolean
}