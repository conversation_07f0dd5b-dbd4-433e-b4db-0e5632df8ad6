[
  {
    "id": 49355751578,
    "name": "00. Almacén principal",
    "address1": "Calle del General Arrando 34",
    "address2": "",
    "city": "Madrid",
    "zip": "28010",
    "province": "M",
    "country": "ES",
    "phone": "+34687447494",
    "created_at": "2020-07-01T20:21:15+02:00",
    "updated_at": "2021-02-09T00:32:25+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/49355751578",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 61250470042,
    "name": "Amazon",
    "address1": "",
    "address2": "",
    "city": "Madrid",
    "zip": "28029",
    "province": "Madrid",
    "country": "ES",
    "phone": "",
    "created_at": "2021-03-12T10:09:22+01:00",
    "updated_at": "2022-01-30T18:22:50+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/61250470042",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 98694889798,
    "name": "Anchanto WMS",
    "address1": null,
    "address2": null,
    "city": null,
    "zip": null,
    "province": null,
    "country": "ES",
    "phone": null,
    "created_at": "2024-11-11T16:31:34+01:00",
    "updated_at": "2024-11-11T16:31:34+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": null,
    "legacy": true,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/98694889798",
    "localized_country_name": "Spain",
    "localized_province_name": null
  },
  {
    "id": 104086733126,
    "name": "Buddify",
    "address1": null,
    "address2": null,
    "city": null,
    "zip": null,
    "province": null,
    "country": "ES",
    "phone": null,
    "created_at": "2024-12-23T10:15:48+01:00",
    "updated_at": "2024-12-23T10:15:48+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": null,
    "legacy": true,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/104086733126",
    "localized_country_name": "Spain",
    "localized_province_name": null
  },
  {
    "id": 59828306074,
    "name": "Defectuoso",
    "address1": "",
    "address2": "",
    "city": "",
    "zip": "",
    "province": "M",
    "country": "ES",
    "phone": "",
    "created_at": "2021-02-07T07:26:45+01:00",
    "updated_at": "2023-04-17T18:41:06+02:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": false,
    "admin_graphql_api_id": "gid://shopify/Location/59828306074",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 61249454234,
    "name": "H0GOLFHOUSE - Castellana",
    "address1": "Paseo de la Castellana 278",
    "address2": "",
    "city": "Madrid",
    "zip": "28046",
    "province": "Madrid",
    "country": "ES",
    "phone": "+34619516687",
    "created_at": "2021-03-12T09:09:02+01:00",
    "updated_at": "2024-05-24T16:54:01+02:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/61249454234",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 89045893446,
    "name": "Printful",
    "address1": null,
    "address2": null,
    "city": null,
    "zip": null,
    "province": null,
    "country": "ES",
    "phone": null,
    "created_at": "2024-01-09T19:13:47+01:00",
    "updated_at": "2024-01-09T19:13:47+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": null,
    "legacy": true,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/89045893446",
    "localized_country_name": "Spain",
    "localized_province_name": null
  },
  {
    "id": 59746582682,
    "name": "Revisar",
    "address1": "",
    "address2": "",
    "city": "",
    "zip": "",
    "province": "A Coruña",
    "country": "ES",
    "phone": "",
    "created_at": "2021-02-03T09:22:34+01:00",
    "updated_at": "2021-12-28T12:20:57+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "C",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/59746582682",
    "localized_country_name": "Spain",
    "localized_province_name": "A Coruña"
  },
  {
    "id": 94382981446,
    "name": "Venta presencial",
    "address1": "",
    "address2": "",
    "city": "",
    "zip": "",
    "province": "Madrid",
    "country": "ES",
    "phone": "",
    "created_at": "2024-07-20T07:26:09+02:00",
    "updated_at": "2024-07-20T07:26:09+02:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/94382981446",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  }
]
---
[
  {
    "id": 49355751578,
    "name": "00. Almacén principal",
    "address1": "Calle del General Arrando 34",
    "address2": "",
    "city": "Madrid",
    "zip": "28010",
    "province": "M",
    "country": "ES",
    "phone": "+34687447494",
    "created_at": "2020-07-01T20:21:15+02:00",
    "updated_at": "2021-02-09T00:32:25+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/49355751578",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 61250470042,
    "name": "Amazon",
    "address1": "",
    "address2": "",
    "city": "Madrid",
    "zip": "28029",
    "province": "Madrid",
    "country": "ES",
    "phone": "",
    "created_at": "2021-03-12T10:09:22+01:00",
    "updated_at": "2022-01-30T18:22:50+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/61250470042",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 104086733126,
    "name": "Buddify",
    "address1": null,
    "address2": null,
    "city": null,
    "zip": null,
    "province": null,
    "country": "ES",
    "phone": null,
    "created_at": "2024-12-23T10:15:48+01:00",
    "updated_at": "2024-12-23T10:15:48+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": null,
    "legacy": true,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/104086733126",
    "localized_country_name": "Spain",
    "localized_province_name": null
  },
  {
    "id": 59828306074,
    "name": "Defectuoso",
    "address1": "",
    "address2": "",
    "city": "",
    "zip": "",
    "province": "M",
    "country": "ES",
    "phone": "",
    "created_at": "2021-02-07T07:26:45+01:00",
    "updated_at": "2023-04-17T18:41:06+02:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": false,
    "admin_graphql_api_id": "gid://shopify/Location/59828306074",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 61249454234,
    "name": "H0GOLFHOUSE - Castellana",
    "address1": "Paseo de la Castellana 278",
    "address2": "",
    "city": "Madrid",
    "zip": "28046",
    "province": "Madrid",
    "country": "ES",
    "phone": "+34619516687",
    "created_at": "2021-03-12T09:09:02+01:00",
    "updated_at": "2024-05-24T16:54:01+02:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/61249454234",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 89045893446,
    "name": "Printful",
    "address1": null,
    "address2": null,
    "city": null,
    "zip": null,
    "province": null,
    "country": "ES",
    "phone": null,
    "created_at": "2024-01-09T19:13:47+01:00",
    "updated_at": "2024-01-09T19:13:47+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": null,
    "legacy": true,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/89045893446",
    "localized_country_name": "Spain",
    "localized_province_name": null
  },
  {
    "id": 59746582682,
    "name": "Revisar",
    "address1": "",
    "address2": "",
    "city": "",
    "zip": "",
    "province": "A Coruña",
    "country": "ES",
    "phone": "",
    "created_at": "2021-02-03T09:22:34+01:00",
    "updated_at": "2021-12-28T12:20:57+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "C",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/59746582682",
    "localized_country_name": "Spain",
    "localized_province_name": "A Coruña"
  },
  {
    "id": 94382981446,
    "name": "Venta presencial",
    "address1": "",
    "address2": "",
    "city": "",
    "zip": "",
    "province": "Madrid",
    "country": "ES",
    "phone": "",
    "created_at": "2024-07-20T07:26:09+02:00",
    "updated_at": "2024-07-20T07:26:09+02:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/94382981446",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  }
]
---
[
  {
    "id": 49355751578,
    "name": "00. Almacén principal",
    "address1": "Calle del General Arrando 34",
    "address2": "",
    "city": "Madrid",
    "zip": "28010",
    "province": "M",
    "country": "ES",
    "phone": "+34687447494",
    "created_at": "2020-07-01T20:21:15+02:00",
    "updated_at": "2021-02-09T00:32:25+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/49355751578",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 61250470042,
    "name": "Amazon",
    "address1": "",
    "address2": "",
    "city": "Madrid",
    "zip": "28029",
    "province": "Madrid",
    "country": "ES",
    "phone": "",
    "created_at": "2021-03-12T10:09:22+01:00",
    "updated_at": "2022-01-30T18:22:50+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/61250470042",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 104086733126,
    "name": "Buddify",
    "address1": null,
    "address2": null,
    "city": null,
    "zip": null,
    "province": null,
    "country": "ES",
    "phone": null,
    "created_at": "2024-12-23T10:15:48+01:00",
    "updated_at": "2024-12-23T10:15:48+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": null,
    "legacy": true,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/104086733126",
    "localized_country_name": "Spain",
    "localized_province_name": null
  },
  {
    "id": 59828306074,
    "name": "Defectuoso",
    "address1": "",
    "address2": "",
    "city": "",
    "zip": "",
    "province": "M",
    "country": "ES",
    "phone": "",
    "created_at": "2021-02-07T07:26:45+01:00",
    "updated_at": "2023-04-17T18:41:06+02:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": false,
    "admin_graphql_api_id": "gid://shopify/Location/59828306074",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 61249454234,
    "name": "H0GOLFHOUSE - Castellana",
    "address1": "Paseo de la Castellana 278",
    "address2": "",
    "city": "Madrid",
    "zip": "28046",
    "province": "Madrid",
    "country": "ES",
    "phone": "+34619516687",
    "created_at": "2021-03-12T09:09:02+01:00",
    "updated_at": "2024-05-24T16:54:01+02:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/61249454234",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 89045893446,
    "name": "Printful",
    "address1": null,
    "address2": null,
    "city": null,
    "zip": null,
    "province": null,
    "country": "ES",
    "phone": null,
    "created_at": "2024-01-09T19:13:47+01:00",
    "updated_at": "2024-01-09T19:13:47+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": null,
    "legacy": true,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/89045893446",
    "localized_country_name": "Spain",
    "localized_province_name": null
  },
  {
    "id": 59746582682,
    "name": "Revisar",
    "address1": "",
    "address2": "",
    "city": "",
    "zip": "",
    "province": "A Coruña",
    "country": "ES",
    "phone": "",
    "created_at": "2021-02-03T09:22:34+01:00",
    "updated_at": "2021-12-28T12:20:57+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "C",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/59746582682",
    "localized_country_name": "Spain",
    "localized_province_name": "A Coruña"
  },
  {
    "id": 94382981446,
    "name": "Venta presencial",
    "address1": "",
    "address2": "",
    "city": "",
    "zip": "",
    "province": "Madrid",
    "country": "ES",
    "phone": "",
    "created_at": "2024-07-20T07:26:09+02:00",
    "updated_at": "2024-07-20T07:26:09+02:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/94382981446",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  }
]
---
[
  {
    "id": 49355751578,
    "name": "00. Almacén principal",
    "address1": "Calle del General Arrando 34",
    "address2": "",
    "city": "Madrid",
    "zip": "28010",
    "province": "M",
    "country": "ES",
    "phone": "+34687447494",
    "created_at": "2020-07-01T20:21:15+02:00",
    "updated_at": "2021-02-09T00:32:25+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/49355751578",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 61250470042,
    "name": "Amazon",
    "address1": "",
    "address2": "",
    "city": "Madrid",
    "zip": "28029",
    "province": "Madrid",
    "country": "ES",
    "phone": "",
    "created_at": "2021-03-12T10:09:22+01:00",
    "updated_at": "2022-01-30T18:22:50+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/61250470042",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 104086733126,
    "name": "Buddify",
    "address1": null,
    "address2": null,
    "city": null,
    "zip": null,
    "province": null,
    "country": "ES",
    "phone": null,
    "created_at": "2024-12-23T10:15:48+01:00",
    "updated_at": "2024-12-23T10:15:48+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": null,
    "legacy": true,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/104086733126",
    "localized_country_name": "Spain",
    "localized_province_name": null
  },
  {
    "id": 59828306074,
    "name": "Defectuoso",
    "address1": "",
    "address2": "",
    "city": "",
    "zip": "",
    "province": "M",
    "country": "ES",
    "phone": "",
    "created_at": "2021-02-07T07:26:45+01:00",
    "updated_at": "2023-04-17T18:41:06+02:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": false,
    "admin_graphql_api_id": "gid://shopify/Location/59828306074",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 61249454234,
    "name": "H0GOLFHOUSE - Castellana",
    "address1": "Paseo de la Castellana 278",
    "address2": "",
    "city": "Madrid",
    "zip": "28046",
    "province": "Madrid",
    "country": "ES",
    "phone": "+34619516687",
    "created_at": "2021-03-12T09:09:02+01:00",
    "updated_at": "2024-05-24T16:54:01+02:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/61249454234",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 89045893446,
    "name": "Printful",
    "address1": null,
    "address2": null,
    "city": null,
    "zip": null,
    "province": null,
    "country": "ES",
    "phone": null,
    "created_at": "2024-01-09T19:13:47+01:00",
    "updated_at": "2024-01-09T19:13:47+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": null,
    "legacy": true,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/89045893446",
    "localized_country_name": "Spain",
    "localized_province_name": null
  },
  {
    "id": 59746582682,
    "name": "Revisar",
    "address1": "",
    "address2": "",
    "city": "",
    "zip": "",
    "province": "A Coruña",
    "country": "ES",
    "phone": "",
    "created_at": "2021-02-03T09:22:34+01:00",
    "updated_at": "2021-12-28T12:20:57+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "C",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/59746582682",
    "localized_country_name": "Spain",
    "localized_province_name": "A Coruña"
  },
  {
    "id": 94382981446,
    "name": "Venta presencial",
    "address1": "",
    "address2": "",
    "city": "",
    "zip": "",
    "province": "Madrid",
    "country": "ES",
    "phone": "",
    "created_at": "2024-07-20T07:26:09+02:00",
    "updated_at": "2024-07-20T07:26:09+02:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/94382981446",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  }
]
---
[
  {
    "id": 49355751578,
    "name": "00. Almacén principal",
    "address1": "Calle del General Arrando 34",
    "address2": "",
    "city": "Madrid",
    "zip": "28010",
    "province": "M",
    "country": "ES",
    "phone": "+34687447494",
    "created_at": "2020-07-01T20:21:15+02:00",
    "updated_at": "2021-02-09T00:32:25+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/49355751578",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 61250470042,
    "name": "Amazon",
    "address1": "",
    "address2": "",
    "city": "Madrid",
    "zip": "28029",
    "province": "Madrid",
    "country": "ES",
    "phone": "",
    "created_at": "2021-03-12T10:09:22+01:00",
    "updated_at": "2022-01-30T18:22:50+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/61250470042",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 104086733126,
    "name": "Buddify",
    "address1": null,
    "address2": null,
    "city": null,
    "zip": null,
    "province": null,
    "country": "ES",
    "phone": null,
    "created_at": "2024-12-23T10:15:48+01:00",
    "updated_at": "2024-12-23T10:15:48+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": null,
    "legacy": true,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/104086733126",
    "localized_country_name": "Spain",
    "localized_province_name": null
  },
  {
    "id": 59828306074,
    "name": "Defectuoso",
    "address1": "",
    "address2": "",
    "city": "",
    "zip": "",
    "province": "M",
    "country": "ES",
    "phone": "",
    "created_at": "2021-02-07T07:26:45+01:00",
    "updated_at": "2023-04-17T18:41:06+02:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": false,
    "admin_graphql_api_id": "gid://shopify/Location/59828306074",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 61249454234,
    "name": "H0GOLFHOUSE - Castellana",
    "address1": "Paseo de la Castellana 278",
    "address2": "",
    "city": "Madrid",
    "zip": "28046",
    "province": "Madrid",
    "country": "ES",
    "phone": "+34619516687",
    "created_at": "2021-03-12T09:09:02+01:00",
    "updated_at": "2024-05-24T16:54:01+02:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/61249454234",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 89045893446,
    "name": "Printful",
    "address1": null,
    "address2": null,
    "city": null,
    "zip": null,
    "province": null,
    "country": "ES",
    "phone": null,
    "created_at": "2024-01-09T19:13:47+01:00",
    "updated_at": "2024-01-09T19:13:47+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": null,
    "legacy": true,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/89045893446",
    "localized_country_name": "Spain",
    "localized_province_name": null
  },
  {
    "id": 59746582682,
    "name": "Revisar",
    "address1": "",
    "address2": "",
    "city": "",
    "zip": "",
    "province": "A Coruña",
    "country": "ES",
    "phone": "",
    "created_at": "2021-02-03T09:22:34+01:00",
    "updated_at": "2021-12-28T12:20:57+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "C",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/59746582682",
    "localized_country_name": "Spain",
    "localized_province_name": "A Coruña"
  },
  {
    "id": 94382981446,
    "name": "Venta presencial",
    "address1": "",
    "address2": "",
    "city": "",
    "zip": "",
    "province": "Madrid",
    "country": "ES",
    "phone": "",
    "created_at": "2024-07-20T07:26:09+02:00",
    "updated_at": "2024-07-20T07:26:09+02:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/94382981446",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  }
]
---
[
  {
    "id": 49355751578,
    "name": "00. Almacén principal",
    "address1": "Calle del General Arrando 34",
    "address2": "",
    "city": "Madrid",
    "zip": "28010",
    "province": "M",
    "country": "ES",
    "phone": "+34687447494",
    "created_at": "2020-07-01T20:21:15+02:00",
    "updated_at": "2021-02-09T00:32:25+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/49355751578",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 61250470042,
    "name": "Amazon",
    "address1": "",
    "address2": "",
    "city": "Madrid",
    "zip": "28029",
    "province": "Madrid",
    "country": "ES",
    "phone": "",
    "created_at": "2021-03-12T10:09:22+01:00",
    "updated_at": "2022-01-30T18:22:50+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/61250470042",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 104086733126,
    "name": "Buddify",
    "address1": null,
    "address2": null,
    "city": null,
    "zip": null,
    "province": null,
    "country": "ES",
    "phone": null,
    "created_at": "2024-12-23T10:15:48+01:00",
    "updated_at": "2024-12-23T10:15:48+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": null,
    "legacy": true,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/104086733126",
    "localized_country_name": "Spain",
    "localized_province_name": null
  },
  {
    "id": 59828306074,
    "name": "Defectuoso",
    "address1": "",
    "address2": "",
    "city": "",
    "zip": "",
    "province": "M",
    "country": "ES",
    "phone": "",
    "created_at": "2021-02-07T07:26:45+01:00",
    "updated_at": "2023-04-17T18:41:06+02:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": false,
    "admin_graphql_api_id": "gid://shopify/Location/59828306074",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 61249454234,
    "name": "H0GOLFHOUSE - Castellana",
    "address1": "Paseo de la Castellana 278",
    "address2": "",
    "city": "Madrid",
    "zip": "28046",
    "province": "Madrid",
    "country": "ES",
    "phone": "+34619516687",
    "created_at": "2021-03-12T09:09:02+01:00",
    "updated_at": "2024-05-24T16:54:01+02:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/61249454234",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  },
  {
    "id": 89045893446,
    "name": "Printful",
    "address1": null,
    "address2": null,
    "city": null,
    "zip": null,
    "province": null,
    "country": "ES",
    "phone": null,
    "created_at": "2024-01-09T19:13:47+01:00",
    "updated_at": "2024-01-09T19:13:47+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": null,
    "legacy": true,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/89045893446",
    "localized_country_name": "Spain",
    "localized_province_name": null
  },
  {
    "id": 59746582682,
    "name": "Revisar",
    "address1": "",
    "address2": "",
    "city": "",
    "zip": "",
    "province": "A Coruña",
    "country": "ES",
    "phone": "",
    "created_at": "2021-02-03T09:22:34+01:00",
    "updated_at": "2021-12-28T12:20:57+01:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "C",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/59746582682",
    "localized_country_name": "Spain",
    "localized_province_name": "A Coruña"
  },
  {
    "id": 94382981446,
    "name": "Venta presencial",
    "address1": "",
    "address2": "",
    "city": "",
    "zip": "",
    "province": "Madrid",
    "country": "ES",
    "phone": "",
    "created_at": "2024-07-20T07:26:09+02:00",
    "updated_at": "2024-07-20T07:26:09+02:00",
    "country_code": "ES",
    "country_name": "Spain",
    "province_code": "M",
    "legacy": false,
    "active": true,
    "admin_graphql_api_id": "gid://shopify/Location/94382981446",
    "localized_country_name": "Spain",
    "localized_province_name": "Madrid Province"
  }
]