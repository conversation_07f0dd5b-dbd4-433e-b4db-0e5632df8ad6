import { Request, Response } from 'express';
import { BigHubService } from '../../../bighub/services/bighub.service';
import { JardimsuspensoService } from '../../services/jardimsuspenso.service';
import fs from 'fs'

export class JardimsuspensoController {

  jardimsuspensoService: JardimsuspensoService;
  bigHubService: BigHubService;
  
  constructor(bigHubService: BigHubService, jardimsuspensoService: JardimsuspensoService) {
    // coolaccesorios service
    this.jardimsuspensoService = jardimsuspensoService;
    // bighub service
    this.bigHubService = bigHubService;
  }
  
  async getProducts(req: Request, res: Response) {
    let response = {
      status: 200,
      message: 'Products found'
    }

    try {
      const products = await this.jardimsuspensoService.getProducts();
      res.status(response.status).json(products)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubCsv(req: Request, res: Response) {
    try {
      const filename = '153-bighub-jardimsuspenso.csv';
      res.setHeader('Content-disposition', 'attachment; filename=' + filename);
      res.setHeader('Content-type', 'text/csv');
      fs.createReadStream(filename).pipe(res)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubExportCsv(req: Request, res: Response) {
    try {
      const filename = '153-bighub-jardimsuspenso.csv';
      const products = await this.jardimsuspensoService.getProductsBighub();
      this.bigHubService.supplierCreateCsv(filename, products, 153); //153

      res.status(200).send('ok');
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }
}