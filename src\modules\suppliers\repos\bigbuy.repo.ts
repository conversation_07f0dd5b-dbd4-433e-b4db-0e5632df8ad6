import { BigbuyModel } from '../models/bigbuy.model';
import { BigbuyPort } from '../ports/bigbuy.port';
import got from 'got';
import { promisify } from 'util'
import stream, { Transform } from 'stream';
import iconv from 'iconv-lite';
import { defer } from '../../../shared/utils/promise.util';
import { parse, format } from 'fast-csv';
import { createReadStream, createWriteStream } from 'node:fs';
import { randomUUID } from 'crypto';
import { BigHubService } from 'src/modules/bighub/services/bighub.service';

const pipeline = promisify(stream.pipeline)

export class BigbuyRepo implements BigbuyPort {

    private batchSize: number
    private lineCount: number
    private fileCount: number

    private files: string[]

    private csvStream: any

    private headers: string[] = [];

    private rows: BigbuyModel[] = []

    constructor() {
        this.batchSize = 100000
        this.fileCount = 0
        this.lineCount = 0
        this.files = []

        this.csvStream = this.createNewFile()
    }

    clear(): void {
        this.rows = []
    }

    createNewFile(): any {
        this.fileCount++
        this.lineCount = 0

        const filename = `./data/${this.fileCount}-${randomUUID()}.csv`
        this.files.push(filename)

        const outputStream = createWriteStream(filename)
        const csvStream = format({ headers: true })
        csvStream.pipe(outputStream)

        return csvStream
    }

    readCsvFile(path: string, cb: (row: BigbuyModel) => void, done: () => void): void {
        const inputStream = createReadStream(path)
        const parseCsv = parse({ headers: true })

        parseCsv.on('data', (row: BigbuyModel) => {
            cb(row)
        })

        parseCsv.on('end', (rowCount: number) => {
            console.log(`Parsed ${rowCount} rows, on file ${this.fileCount}`)
            done()
        })

        inputStream.pipe(parseCsv)
    }

    async getAll(): Promise<string[]> {

        this.rows = []
        const deferred = defer()

        const csvOptions = {
            headers: false,
            delimiter: ';',
            ignoreEmpty: true,
            discardUnmappedColumns: true,
            strictColumnHandling: false,
        }

        try {

            /*
            const removeQuotes = new Transform({
                transform(chunk: any, encoding: any, callback: any) {
                    callback(null, chunk.toString().replace(/"/g, ''));
                },
            });
            */
            const parseCsv = parse(csvOptions)

            let count = 0

            parseCsv.on('data', (row: any) => {
                if (count > 0) {
                    this.processLine(row)
                }
                count++
            })
            parseCsv.on('end', (rowCount: number) => {
                console.log(`Parsed ${rowCount} rows`)
                deferred.resolve()
            })
            await pipeline(createReadStream('./data/downloads/bigbuy/bigbuy_combined.csv'), iconv.decodeStream('UTF-8'), parseCsv)
            console.log('CSV obtido com sucesso')

            // After successfully processing the CSV, use BigHubService to download and upload to S3
            /*
            try {
              const s3Url = await BigHubService.downloadAndUploadToS3(this.url, 'bigbuy', 'seller', 1145);
              console.log(`Uploaded to S3: ${s3Url}`);
            } catch (s3Error) {
              // Log the error but don't fail the main operation
              console.error('Error uploading to S3, but continuing with data processing:', s3Error);
            }
            */
        } catch (error) {
            console.log('Erro na obtenção do csv', error)
            deferred.reject()
        }
        await deferred.promise

        return this.files

        //return this.rows

    }

    private processLine(row: any) {
        let imagePlaceholder: string = 'https://cdn.bighub.store/image/product-placeholder.png';
        if (this.headers.length === 0) {
            this.headers = row;
        } else {
            const newRow: BigbuyModel = {
                ID: row[0],
                CATEGORY: row[1],
                NAME: row[2],
                ATTRIBUTE1: row[3],
                ATTRIBUTE2: row[4],
                VALUE1: row[5],
                VALUE2: row[6],
                DESCRIPTION: row[7],
                BRAND: row[8],
                FEATURE: row[9],
                PRICE: row[10],
                PVP_BIGBUY: row[11],
                PVD: row[12],
                IVA: row[13],
                VIDEO: row[14],
                EAN13: row[15],
                WIDTH: row[16],
                HEIGHT: row[17],
                DEPTH: row[18],
                WEIGHT: row[19],
                STOCK: row[20],
                DATE_ADD: row[21],
                DATE_UPD: row[22],
                IMAGE1: row[23],
                IMAGE2: row[24],
                IMAGE3: row[25],
                IMAGE4: row[26],
                IMAGE5: row[27],
                IMAGE6: row[28],
                IMAGE7: row[29],
                IMAGE8: row[30],
                CONDITION: row[31]
            }

            if (this.lineCount >= this.batchSize) {
                this.csvStream.end()
                console.log(`File ${this.fileCount} generated`)
                this.csvStream = this.createNewFile()
            }

            this.csvStream.write(newRow)
            this.lineCount++
        }
    }
}