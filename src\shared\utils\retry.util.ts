export async function retry<T>(
    fn: () => Promise<T>,
    maxAttempts = 3,
    delayMs = 1000
  ): Promise<T> {
    let attempt = 0
    let lastError: any
  
    while (attempt < maxAttempts) {
      try {
        return await fn()
      } catch (err) {
        lastError = err
        attempt++
        if (attempt < maxAttempts) {
          const backoff = delayMs * Math.pow(2, attempt - 1)
          console.warn(`Retrying (${attempt}/${maxAttempts}) after ${backoff}ms:`, err instanceof Error ? err.message : String(err))
          await new Promise(res => setTimeout(res, backoff))
        }
      }
    }
  
    throw lastError
  }
  