import { Request, Response } from 'express';
import { BigHubService } from '../../../bighub/services/bighub.service';
import { FerrimexService } from '../../services/ferrimex.service';
import fs from 'fs'

export class FerrimexController {

  ferrimexService: FerrimexService;
  bigHubService: BigHubService;
  
  constructor(bigHubService: BigHubService, ferrimexService: FerrimexService) {
    // coolaccesorios service
    this.ferrimexService = ferrimexService;
    // bighub service
    this.bigHubService = bigHubService;
  }
  
  async getProducts(req: Request, res: Response) {
    let response = {
      status: 200,
      message: 'Products found'
    }

    try {
      const products = await this.ferrimexService.getProducts();
      res.status(response.status).json(products)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubCsv(req: Request, res: Response) {
    try {
      const filename = '824-bighub-ferrimex.csv';
      res.setHeader('Content-disposition', 'attachment; filename=' + filename);
      res.setHeader('Content-type', 'text/csv');
      fs.createReadStream(filename).pipe(res)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubExportCsv(req: Request, res: Response) {
    try {
      const filename = '824-bighub-ferrimex.csv';
      const products = await this.ferrimexService.getProductsBighub();
      this.bigHubService.supplierCreateCsv(filename, products, 824); //824

      res.status(200).send('ok');
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }
}