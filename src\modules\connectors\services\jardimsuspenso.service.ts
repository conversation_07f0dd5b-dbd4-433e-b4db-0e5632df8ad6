import { ProductModel as BighubProductModel } from '../../bighub/models/product.model';
import { BIGHubCsvPort } from '../../bighub/ports/bighub-csv.port';
import { ProductModel } from '../../catalogue/models/product.model';
import { YamlRepo } from '../../configurations/repos/yaml.repo';
import { JardimsuspensoPort } from '../ports/jardimsuspenso.port';
import { JardimsuspensoModel } from '../models/jardimsuspenso.model';
import { ConnectorCode } from '../enums/connector-code.enum';
import { JardimsuspensoMapper } from '../mappers/jardimsuspenso.mapper';

export class JardimsuspensoService {
  jardimsuspensoRepo: JardimsuspensoPort;
  bighubRepo: BIGHubCsvPort
  configuration: YamlRepo
  private EXCLUDED_CATEGORY = 'Tubular LED'; // Added constant for excluded category

  constructor(
    repo: JardimsuspensoPort,
    bighubRepo: BIGHubCsvPort,
    configuration: YamlRepo
  ) {
    this.jardimsuspensoRepo = repo
    this.bighubRepo = bighubRepo
    this.configuration = configuration
  }

  async getProducts(): Promise<ProductModel[]> {
    const jardimsuspenso = await this.jardimsuspensoRepo.getAll();
    
    return jardimsuspenso.map((item: JardimsuspensoModel) => JardimsuspensoMapper
      .toProduct(item))
      .filter(product => product.stock > 0);
  }

  async getProductsBighub(): Promise<BighubProductModel[]> {
    const BIGHubServiceProducts = await this.jardimsuspensoRepo.getAll();

    console.log(`Total products: ${BIGHubServiceProducts.length}`);

    let products = BIGHubServiceProducts.map((item: JardimsuspensoModel) => JardimsuspensoMapper.toProductBigHub(item));

    console.log('Products:', products.length)
    products = products.filter(product => product.ean !== '')
    console.log('After ean filter:', products.length)
    // stock rules 
    products = products.filter(product => product.stock > 0)
    console.log('After stock filter:', products.length)
    // price rules
    products = products.filter(product => product.global_price > 0)
    console.log('After price filter:', products.length)
    // NaN's filter
    products = products.filter(product => !isNaN(product.global_price))
    console.log('After NaN filter:', products.length)
    // tax rules
    products = await this.bighubRepo.applyTaxes(products, ConnectorCode.JARDIMSUSPENSO)

    return products;
  }
}