import { Request, Response } from 'express';
import { BigHubService } from '../../../bighub/services/bighub.service';
import { FitnisService } from '../../services/fitnis.service';
import fs from 'fs'

export class FitnisController {

  fitnisService: FitnisService;
  bigHubService: BigHubService;
  
  constructor(bigHubService: BigHubService, fitnisService: FitnisService) {
    // coolaccesorios service
    this.fitnisService = fitnisService;
    // bighub service
    this.bigHubService = bigHubService;
  }
  
  async getProducts(req: Request, res: Response) {
    let response = {
      status: 200,
      message: 'Products found'
    }

    try {
      const products = await this.fitnisService.getProducts();
      res.status(response.status).json(products)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubCsv(req: Request, res: Response) {
    try {
      const filename = '1407-bighub-fitnis.csv';
      res.setHeader('Content-disposition', 'attachment; filename=' + filename);
      res.setHeader('Content-type', 'text/csv');
      fs.createReadStream(filename).pipe(res)
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }

  async getProductsBigHubExportCsv(req: Request, res: Response) {
    try {
      const filename = '1407-bighub-fitnis.csv';
      const products = await this.fitnisService.getProductsBighub();
      this.bigHubService.supplierCreateCsv(filename, products, 1407); //1407

      res.status(200).send('ok');
    } catch (error: any) {
      console.error(error);
      res.status(500).json(error.message)
    }
  }
}