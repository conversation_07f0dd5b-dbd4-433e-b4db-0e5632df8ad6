import { CastroesilvaModel } from '../models/castroesilva.model';
import { CastroesilvaPort } from '../ports/castroesilva.port';
import got from 'got';
import { promisify } from 'util'
import stream, { Transform } from 'stream';
import iconv from 'iconv-lite';
import { parse } from 'fast-csv';
import { defer } from '../../../shared/utils/promise.util';

const pipeline = promisify(stream.pipeline)

export class CastroesilvaRepo implements CastroesilvaPort {

  private url: string;
  private headers: string[] = []

  private rows: CastroesilvaModel[] = []

  constructor(url: string) {
    this.url = url;
  }

  async getAll(): Promise<CastroesilvaModel[]> {

    this.rows = []
    const deferred = defer()

    const csvOptions = {
      headers: false,
      delimiter: ',',
      ignoreEmpty: true,
      discardUnmappedColumns: true,
      strictColumnHandling: false,
    }

    try {

      const parseCsv = parse(csvOptions)

      parseCsv.on('data', (row: any) => {
        this.processLine(row)
      })
      parseCsv.on('end', (rowCount: number) => {
        console.log(`Parsed ${rowCount} rows`)
        deferred.resolve()
      })
      await pipeline(got.stream(this.url), iconv.decodeStream('ISO-8859-1'), parseCsv) //ISO-8859-1
      console.log('CSV obtido com sucesso')
    } catch (error) {
      console.log('Erro na obtenção do csv', error)
      deferred.reject()
    }

    await deferred.promise

    return this.rows

  }

  private processLine(row: any) {
    if (this.headers.length === 0) {
      this.headers = row;
    } else {
      const newRow: CastroesilvaModel = {
        ean: row[0],
        sku: row[1],
        title: row[2],
        description: row[3],
        brand: row[4],
        global_price: row[5],
        stock: row[6],
        condition: row[7],
        image: row[8],
        min_quantity_alert: row[9],
        package_quantity: row[10],
        leadtime_to_ship: row[11],
        shipping_price: row[12],
        weight: row[13],
      }
      this.rows.push(newRow);
    }
  }
}