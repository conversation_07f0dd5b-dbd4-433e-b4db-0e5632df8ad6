import { Request, Response } from 'express';
import { Logger } from '@shared/utils/logger.util';
import { BigbuyStockService } from '../../services/bigbuy-stock.service';
import * as fs from 'fs';
import * as path from 'path';

export class BigbuyStockController {
  bigbuyStockService: BigbuyStockService;

  constructor(bigbuyStockService: BigbuyStockService) {
    this.bigbuyStockService = bigbuyStockService;
  }

  async getProducts(req: Request, res: Response) {
    const logger = new Logger('BigbuyStockController');
    
    try {
      logger.info('Fetching BigBuy stock products');
      const products = await this.bigbuyStockService.getBigbuyStocks();
      
      // Create simplified stock data directly here to ensure it's correct
      logger.info('Creating simplified stock data directly...');
      const simplifiedStocks = products.map(item => {
        // Calculate total stock across all warehouses
        const totalStock = item.stocks.reduce((sum, stock) => sum + (stock.quantity || 0), 0);
        return {
          sku: item.sku,
          stock: totalStock
        };
      });
      
      // Save simplified data to bigbuy-stock.json
      const jsonFilePath = path.join(process.cwd(), 'bigbuy-stock.json');
      logger.info(`Saving simplified stock data to: ${jsonFilePath}`);
      fs.writeFileSync(jsonFilePath, JSON.stringify(simplifiedStocks, null, 2), 'utf8');
      
      const fileStats = fs.statSync(jsonFilePath);
      logger.info(`Simplified stock data saved: ${Math.round(fileStats.size / 1024)} KB, ${simplifiedStocks.length} items`);
      
      res.status(200).json({
        status: 'success',
        message: 'Products found and simplified stock data saved',
        count: products.length
      });
    } catch (error: any) {
      logger.error('Error fetching BigBuy stock products', error);
      console.error(error);
      res.status(500).json({
        status: 'error',
        message: error.message
      });
    }
  }

  async getStockSummary(req: Request, res: Response) {
    const logger = new Logger('BigbuyStockController');
    
    try {
      logger.info('Fetching BigBuy stock summary');
      const summary = await this.bigbuyStockService.getStockSummary();
      logger.info('Successfully fetched stock summary');
      
      res.status(200).json({
        status: 'success',
        message: 'Stock summary retrieved',
        data: summary
      });
    } catch (error: any) {
      logger.error('Error fetching stock summary', error);
      console.error(error);
      res.status(500).json({
        status: 'error',
        message: error.message
      });
    }
  }

  async syncStock(req: Request, res: Response) {
    const logger = new Logger('BigbuyStockController');
    
    try {
      logger.info('Starting BigBuy stock synchronization');
      const products = await this.bigbuyStockService.getBigbuyStocks();
      
      // Create simplified stock data directly
      logger.info('Creating simplified stock data directly...');
      const simplifiedStocks = products.map(item => {
        // Calculate total stock across all warehouses
        const totalStock = item.stocks.reduce((sum, stock) => sum + (stock.quantity || 0), 0);
        return {
          sku: item.sku,
          stock: totalStock
        };
      });
      
      // Save simplified data to bigbuy-stock.json
      const jsonFilePath = path.join(process.cwd(), 'bigbuy-stock.json');
      logger.info(`Saving simplified stock data to: ${jsonFilePath}`);
      fs.writeFileSync(jsonFilePath, JSON.stringify(simplifiedStocks, null, 2), 'utf8');
      
      const fileStats = fs.statSync(jsonFilePath);
      logger.info(`Simplified stock data saved: ${Math.round(fileStats.size / 1024)} KB, ${simplifiedStocks.length} items`);
      
      res.status(200).json({
        status: 'success',
        message: 'Stock synchronization completed and simplified data saved',
        count: products.length
      });
    } catch (error: any) {
      logger.error('Error during stock synchronization', error);
      console.error(error);
      res.status(500).json({
        status: 'error',
        message: error.message
      });
    }
  }

  async clearCache(req: Request, res: Response) {
    const logger = new Logger('BigbuyStockController');
    
    try {
      this.bigbuyStockService.clearCache();
      logger.info('BigBuy stock cache cleared');
      
      res.status(200).json({
        status: 'success',
        message: 'Cache cleared successfully'
      });
    } catch (error: any) {
      logger.error('Error clearing cache', error);
      console.error(error);
      res.status(500).json({
        status: 'error',
        message: error.message
      });
    }
  }
  
  // New method to force creating simplified stock from existing file
  async forceSimplifiedStock(req: Request, res: Response) {
    const logger = new Logger('BigbuyStockController');
    
    try {
      logger.info('Starting forced simplified stock creation');
      
      const stockPath = path.join(process.cwd(), 'bigbuy-stock.json');
      
      if (!fs.existsSync(stockPath)) {
        logger.error('Stock file not found');
        return res.status(404).json({
          status: 'error',
          message: 'Stock file not found'
        });
      }
      
      // Read the existing file
      const rawData = fs.readFileSync(stockPath, 'utf8');
      let stockData;
      
      try {
        stockData = JSON.parse(rawData);
      } catch (parseError) {
        logger.error('Failed to parse stock data', parseError);
        return res.status(500).json({
          status: 'error',
          message: 'Failed to parse stock data'
        });
      }
      
      // Check if it's already simplified
      if (stockData.length > 0 && 'stock' in stockData[0] && 'sku' in stockData[0] && Object.keys(stockData[0]).length === 2) {
        logger.info('Stock data is already simplified');
        return res.status(200).json({
          status: 'success',
          message: 'Stock data is already in simplified format',
          count: stockData.length
        });
      }
      
      // Transform to simplified format
      logger.info('Creating simplified stock data...');
      const simplifiedStocks = stockData.map((item: any) => {
        // Check format and extract total stock
        let totalStock = 0;
        
        if (item.stocks && Array.isArray(item.stocks)) {
          // Original format with stocks array
          totalStock = item.stocks.reduce((sum: number, stock: any) => sum + (stock.quantity || 0), 0);
        } else if ('stock' in item) {
          // Already simplified
          totalStock = item.stock;
        }
        
        return {
          sku: item.sku,
          stock: totalStock
        };
      });
      
      // Save back to the same file
      logger.info(`Saving simplified stock data back to: ${stockPath}`);
      fs.writeFileSync(stockPath, JSON.stringify(simplifiedStocks, null, 2), 'utf8');
      
      const fileStats = fs.statSync(stockPath);
      logger.info(`Simplified stock data saved: ${Math.round(fileStats.size / 1024)} KB, ${simplifiedStocks.length} items`);
      
      res.status(200).json({
        status: 'success',
        message: 'Stock data simplified successfully',
        count: simplifiedStocks.length
      });
      
    } catch (error: any) {
      logger.error('Error simplifying stock data', error);
      console.error(error);
      res.status(500).json({
        status: 'error',
        message: error.message
      });
    }
  }
}