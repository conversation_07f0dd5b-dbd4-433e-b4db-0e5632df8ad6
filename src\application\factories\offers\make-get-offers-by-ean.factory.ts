import { OfferKnexRepository } from "../../../infra/db/knex/repositories/offer.knex.repository"
import { GetOffersByEanUseCaseInterface } from "../../interfaces/use-cases/offers/get-offers-by-ean-use-case.interface"
import { GetOffersByEanUseCase } from "../../uses-cases/offers/get-offers-by-ean.use-case"

export const makeGetOffersByEanFactory = (): GetOffersByEanUseCaseInterface => {
  const offerRepository = new OfferKnexRepository()
  return new GetOffersByEanUseCase(offerRepository)
}