[2025-05-20T07:47:47.668Z] INFO [BigbuyController] Starting BigBuy file download process
Data: {
  "fileCount": 13,
  "destination": "./data/downloads/bigbuy"
}
---
[2025-05-20T07:50:40.880Z] INFO [BigbuyController] Files downloaded successfully
Data: {
  "count": 13
}
---
[2025-05-20T07:50:45.863Z] INFO [BigbuyController] Files combined successfully
Data: {
  "outputPath": "data\\downloads\\bigbuy\\bigbuy_combined.csv"
}
---
[2025-05-20T07:50:45.867Z] INFO [BigbuyController] Fetching latest stock data from BigBuy...
---
[2025-05-20T07:55:46.377Z] INFO [BigbuyController] Stock data updated successfully and saved to bigbuy-stock.json
---
[2025-05-20T07:55:46.380Z] INFO [BigbuyController] Starting stock combination process
---
[2025-05-20T07:55:46.382Z] ERROR [BigbuyController] Error in combineStocksInProducts:
Error: Stock JSON file not found at: C:\Users\<USER>\Documents\dev\feedmaster-pro\bigbuy-stock.json
Stack: Error: Stock JSON file not found at: C:\Users\<USER>\Documents\dev\feedmaster-pro\bigbuy-stock.json
    at BigbuyController.<anonymous> (C:\Users\<USER>\Documents\dev\feedmaster-pro\src\modules\suppliers\http\controllers\bigbuy.controller.ts:156:15)
    at Generator.next (<anonymous>)
    at C:\Users\<USER>\Documents\dev\feedmaster-pro\src\modules\suppliers\http\controllers\bigbuy.controller.ts:8:71
    at new Promise (<anonymous>)
    at __awaiter (C:\Users\<USER>\Documents\dev\feedmaster-pro\src\modules\suppliers\http\controllers\bigbuy.controller.ts:4:12)
    at BigbuyController.combineStocksInProducts (C:\Users\<USER>\Documents\dev\feedmaster-pro\src\modules\suppliers\http\controllers\bigbuy.controller.ts:179:16)
    at BigbuyController.<anonymous> (C:\Users\<USER>\Documents\dev\feedmaster-pro\src\modules\suppliers\http\controllers\bigbuy.controller.ts:114:18)
    at Generator.next (<anonymous>)
    at fulfilled (C:\Users\<USER>\Documents\dev\feedmaster-pro\src\modules\suppliers\http\controllers\bigbuy.controller.ts:5:58)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
---
[2025-05-20T07:55:46.395Z] ERROR [BigbuyController] Error in BigBuy export process
Error: Stock JSON file not found at: C:\Users\<USER>\Documents\dev\feedmaster-pro\bigbuy-stock.json
Stack: Error: Stock JSON file not found at: C:\Users\<USER>\Documents\dev\feedmaster-pro\bigbuy-stock.json
    at BigbuyController.<anonymous> (C:\Users\<USER>\Documents\dev\feedmaster-pro\src\modules\suppliers\http\controllers\bigbuy.controller.ts:156:15)
    at Generator.next (<anonymous>)
    at C:\Users\<USER>\Documents\dev\feedmaster-pro\src\modules\suppliers\http\controllers\bigbuy.controller.ts:8:71
    at new Promise (<anonymous>)
    at __awaiter (C:\Users\<USER>\Documents\dev\feedmaster-pro\src\modules\suppliers\http\controllers\bigbuy.controller.ts:4:12)
    at BigbuyController.combineStocksInProducts (C:\Users\<USER>\Documents\dev\feedmaster-pro\src\modules\suppliers\http\controllers\bigbuy.controller.ts:179:16)
    at BigbuyController.<anonymous> (C:\Users\<USER>\Documents\dev\feedmaster-pro\src\modules\suppliers\http\controllers\bigbuy.controller.ts:114:18)
    at Generator.next (<anonymous>)
    at fulfilled (C:\Users\<USER>\Documents\dev\feedmaster-pro\src\modules\suppliers\http\controllers\bigbuy.controller.ts:5:58)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
---
[2025-05-20T08:02:03.218Z] INFO [BigbuyController] Starting BigBuy file download process
Data: {
  "fileCount": 13,
  "destination": "./data/downloads/bigbuy"
}
---
[2025-05-20T08:05:28.002Z] INFO [BigbuyController] Files downloaded successfully
Data: {
  "count": 13
}
---
[2025-05-20T08:05:34.280Z] INFO [BigbuyController] Files combined successfully
Data: {
  "outputPath": "data\\downloads\\bigbuy\\bigbuy_combined.csv"
}
---
[2025-05-20T08:05:34.283Z] INFO [BigbuyController] Fetching latest stock data from BigBuy...
---
[2025-05-20T08:27:30.142Z] INFO [BigbuyController] Starting BigBuy file download process
Data: {
  "fileCount": 13,
  "destination": "./data/downloads/bigbuy"
}
---
[2025-05-20T09:28:10.003Z] INFO [BigbuyController] Starting BigBuy file download process
Data: {
  "fileCount": 13,
  "destination": "./data/downloads/bigbuy"
}
---
[2025-05-20T09:28:13.669Z] INFO [BigbuyController] Starting BigBuy file download process
Data: {
  "fileCount": 13,
  "destination": "./data/downloads/bigbuy"
}
---
[2025-05-20T09:30:01.247Z] INFO [BigbuyController] Files downloaded successfully
Data: {
  "count": 13
}
---
[2025-05-20T09:30:06.562Z] INFO [BigbuyController] Files combined successfully
Data: {
  "outputPath": "data\\downloads\\bigbuy\\bigbuy_combined.csv"
}
---
[2025-05-20T09:30:06.564Z] INFO [BigbuyController] Fetching latest stock data from BigBuy...
---
[2025-05-20T09:30:23.787Z] INFO [BigbuyController] Files downloaded successfully
Data: {
  "count": 13
}
---
[2025-05-20T09:30:28.872Z] INFO [BigbuyController] Files combined successfully
Data: {
  "outputPath": "data\\downloads\\bigbuy\\bigbuy_combined.csv"
}
---
[2025-05-20T09:30:28.874Z] INFO [BigbuyController] Fetching latest stock data from BigBuy...
---
[2025-05-20T09:32:08.325Z] INFO [BigbuyController] Starting BigBuy file download process
Data: {
  "fileCount": 13,
  "destination": "./data/downloads/bigbuy"
}
---
[2025-05-20T09:34:20.083Z] INFO [BigbuyController] Files downloaded successfully
Data: {
  "count": 13
}
---
[2025-05-20T09:34:24.828Z] INFO [BigbuyController] Files combined successfully
Data: {
  "outputPath": "data\\downloads\\bigbuy\\bigbuy_combined.csv"
}
---
[2025-05-20T09:34:24.830Z] INFO [BigbuyController] Fetching latest stock data from BigBuy...
---
[2025-05-20T09:39:35.240Z] INFO [BigbuyController] Stock data updated successfully and saved to bigbuy-stock.json
---
[2025-05-20T09:39:35.244Z] INFO [BigbuyController] Starting stock combination process
---
[2025-05-20T09:39:36.647Z] INFO [BigbuyController] Loaded 364719 stock records from JSON
---
[2025-05-20T09:39:36.860Z] INFO [BigbuyController] Created stock map with 364714 entries
---
[2025-05-20T09:39:36.948Z] ERROR [BigbuyController] Error parsing CSV:
Error: Unexpected Error: column header mismatch expected: 32 columns got: 34
Stack: Error: Unexpected Error: column header mismatch expected: 32 columns got: 34
    at HeaderTransformer.processRow (C:\Users\<USER>\Documents\dev\feedmaster-pro\node_modules\@fast-csv\parse\src\transforms\HeaderTransformer.ts:79:23)
    at HeaderTransformer.transform (C:\Users\<USER>\Documents\dev\feedmaster-pro\node_modules\@fast-csv\parse\src\transforms\HeaderTransformer.ts:46:30)
    at CsvParserStream.transformRow (C:\Users\<USER>\Documents\dev\feedmaster-pro\node_modules\@fast-csv\parse\src\CsvParserStream.ts:163:36)
    at iterate (C:\Users\<USER>\Documents\dev\feedmaster-pro\node_modules\@fast-csv\parse\src\CsvParserStream.ts:142:25)
    at Immediate._onImmediate (C:\Users\<USER>\Documents\dev\feedmaster-pro\node_modules\@fast-csv\parse\src\CsvParserStream.ts:123:46)
    at processImmediate (node:internal/timers:478:21)
---
[2025-05-20T09:39:36.950Z] ERROR [BigbuyController] Error in combineStocksInProducts:
Error: Unexpected Error: column header mismatch expected: 32 columns got: 34
Stack: Error: Unexpected Error: column header mismatch expected: 32 columns got: 34
    at HeaderTransformer.processRow (C:\Users\<USER>\Documents\dev\feedmaster-pro\node_modules\@fast-csv\parse\src\transforms\HeaderTransformer.ts:79:23)
    at HeaderTransformer.transform (C:\Users\<USER>\Documents\dev\feedmaster-pro\node_modules\@fast-csv\parse\src\transforms\HeaderTransformer.ts:46:30)
    at CsvParserStream.transformRow (C:\Users\<USER>\Documents\dev\feedmaster-pro\node_modules\@fast-csv\parse\src\CsvParserStream.ts:163:36)
    at iterate (C:\Users\<USER>\Documents\dev\feedmaster-pro\node_modules\@fast-csv\parse\src\CsvParserStream.ts:142:25)
    at Immediate._onImmediate (C:\Users\<USER>\Documents\dev\feedmaster-pro\node_modules\@fast-csv\parse\src\CsvParserStream.ts:123:46)
    at processImmediate (node:internal/timers:478:21)
---
[2025-05-20T09:39:36.952Z] ERROR [BigbuyController] Error in BigBuy export process
Error: Unexpected Error: column header mismatch expected: 32 columns got: 34
Stack: Error: Unexpected Error: column header mismatch expected: 32 columns got: 34
    at HeaderTransformer.processRow (C:\Users\<USER>\Documents\dev\feedmaster-pro\node_modules\@fast-csv\parse\src\transforms\HeaderTransformer.ts:79:23)
    at HeaderTransformer.transform (C:\Users\<USER>\Documents\dev\feedmaster-pro\node_modules\@fast-csv\parse\src\transforms\HeaderTransformer.ts:46:30)
    at CsvParserStream.transformRow (C:\Users\<USER>\Documents\dev\feedmaster-pro\node_modules\@fast-csv\parse\src\CsvParserStream.ts:163:36)
    at iterate (C:\Users\<USER>\Documents\dev\feedmaster-pro\node_modules\@fast-csv\parse\src\CsvParserStream.ts:142:25)
    at Immediate._onImmediate (C:\Users\<USER>\Documents\dev\feedmaster-pro\node_modules\@fast-csv\parse\src\CsvParserStream.ts:123:46)
    at processImmediate (node:internal/timers:478:21)
---
[2025-05-20T09:49:11.194Z] INFO [BigbuyController] Starting BigBuy file download process
Data: {
  "fileCount": 13,
  "destination": "./data/downloads/bigbuy"
}
---
[2025-05-20T09:51:36.473Z] INFO [BigbuyController] Starting BigBuy file download process
Data: {
  "fileCount": 13,
  "destination": "./data/downloads/bigbuy"
}
---
[2025-05-20T09:52:46.618Z] INFO [BigbuyController] Files downloaded successfully
Data: {
  "count": 13
}
---
[2025-05-20T09:52:52.480Z] INFO [BigbuyController] Files combined successfully
Data: {
  "outputPath": "data\\downloads\\bigbuy\\bigbuy_combined.csv"
}
---
[2025-05-20T09:52:52.483Z] INFO [BigbuyController] Starting stock combination process
---
[2025-05-20T09:52:52.485Z] INFO [BigbuyController] Found stock file at: C:\Users\<USER>\Documents\dev\feedmaster-pro\bigbuy-stock.json
---
[2025-05-20T09:52:55.341Z] INFO [BigbuyController] Loaded 364719 stock records from JSON
---
[2025-05-20T09:52:55.696Z] INFO [BigbuyController] Created stock map with 364714 entries
---
[2025-05-20T10:14:31.142Z] INFO [BigbuyController] Starting BigBuy file download process
Data: {
  "fileCount": 13,
  "destination": "./data/downloads/bigbuy"
}
---
[2025-05-20T10:16:24.198Z] INFO [BigbuyController] Files downloaded successfully
Data: {
  "count": 13
}
---
[2025-05-20T10:16:28.261Z] INFO [BigbuyController] Files combined successfully
Data: {
  "outputPath": "data\\downloads\\bigbuy\\bigbuy_combined.csv"
}
---
[2025-05-20T10:16:28.263Z] INFO [BigbuyController] Fetching latest stock data from BigBuy...
---
[2025-05-20T11:51:07.717Z] INFO [BigbuyController] Starting BigBuy file download process
Data: {
  "fileCount": 13,
  "destination": "./data/downloads/bigbuy"
}
---
[2025-05-20T11:52:40.005Z] INFO [BigbuyController] Files downloaded successfully
Data: {
  "count": 13
}
---
[2025-05-20T11:52:45.133Z] INFO [BigbuyController] Files combined successfully
Data: {
  "outputPath": "data\\downloads\\bigbuy\\bigbuy_combined.csv"
}
---
[2025-05-20T11:52:45.135Z] INFO [BigbuyController] Fetching latest stock data from BigBuy...
---
[2025-05-20T11:58:30.966Z] INFO [BigbuyController] Stock data updated successfully and saved to bigbuy-stock.json
---
[2025-05-20T11:58:30.968Z] INFO [BigbuyController] Starting stock combination process
---
[2025-05-20T11:58:30.970Z] INFO [BigbuyController] Found full stock file at: C:\Users\<USER>\Documents\dev\feedmaster-pro\bigbuy-stock.json
---
[2025-05-20T11:58:32.975Z] INFO [BigbuyController] Loaded 364856 stock records from JSON
---
[2025-05-20T11:58:33.290Z] INFO [BigbuyController] Created stock map with 364856 entries
---
[2025-05-20T13:09:51.162Z] INFO [BigbuyController] Starting BigBuy file download process
Data: {
  "fileCount": 13,
  "destination": "./data/downloads/bigbuy"
}
---
[2025-05-20T13:11:17.274Z] INFO [BigbuyController] Files downloaded successfully
Data: {
  "count": 13
}
---
[2025-05-20T13:11:22.979Z] INFO [BigbuyController] Files combined successfully
Data: {
  "outputPath": "data\\downloads\\bigbuy\\bigbuy_combined.csv"
}
---
[2025-05-20T13:11:22.981Z] INFO [BigbuyController] Fetching latest stock data from BigBuy...
---
[2025-05-20T13:16:28.340Z] INFO [BigbuyController] Stock data updated successfully and saved to bigbuy-stock.json
---
[2025-05-20T13:16:28.343Z] INFO [BigbuyController] Starting stock combination process
---
[2025-05-20T13:16:28.345Z] INFO [BigbuyController] Found stock file at: C:\Users\<USER>\Documents\dev\feedmaster-pro\bigbuy-stock.json
---
[2025-05-20T13:16:30.239Z] INFO [BigbuyController] Loaded 364935 stock records from JSON
---
[2025-05-20T13:16:30.308Z] INFO [BigbuyController] Created stock map with 0 entries
---
[2025-05-20T13:48:34.877Z] INFO [BigbuyController] Starting BigBuy file download process
Data: {
  "fileCount": 13,
  "destination": "./data/downloads/bigbuy"
}
---
[2025-05-20T14:06:46.759Z] INFO [BigbuyController] Starting BigBuy file download process
Data: {
  "fileCount": 13,
  "destination": "./data/downloads/bigbuy"
}
---
[2025-05-20T14:09:19.379Z] INFO [BigbuyController] Files downloaded successfully
Data: {
  "count": 13
}
---
[2025-05-20T14:09:24.784Z] INFO [BigbuyController] Files combined successfully
Data: {
  "outputPath": "data\\downloads\\bigbuy\\bigbuy_combined.csv"
}
---
[2025-05-20T14:09:24.789Z] INFO [BigbuyController] Fetching latest stock data from BigBuy...
---
[2025-05-20T14:15:12.850Z] INFO [BigbuyController] Starting stock file simplification
---
[2025-05-20T14:15:14.390Z] INFO [BigbuyController] Creating simplified stock data...
---
[2025-05-20T14:15:14.576Z] INFO [BigbuyController] Saving simplified stock data back to: C:\Users\<USER>\Documents\dev\feedmaster-pro\bigbuy-stock.json
---
[2025-05-20T14:15:14.832Z] INFO [BigbuyController] Simplified stock data saved: 16969 KB, 365839 items
---
[2025-05-20T14:15:14.834Z] INFO [BigbuyController] Stock data updated successfully and saved to bigbuy-stock.json
---
[2025-05-20T14:15:14.836Z] INFO [BigbuyController] Starting stock combination process
---
[2025-05-20T14:15:14.838Z] INFO [BigbuyController] Starting stock file simplification
---
[2025-05-20T14:15:15.138Z] INFO [BigbuyController] Stock data is already simplified
---
[2025-05-20T14:15:15.140Z] INFO [BigbuyController] Found stock file at: C:\Users\<USER>\Documents\dev\feedmaster-pro\bigbuy-stock.json
---
[2025-05-20T14:15:15.443Z] INFO [BigbuyController] Loaded 365839 stock records from JSON
---
[2025-05-20T14:15:15.647Z] INFO [BigbuyController] Created stock map with 365757 entries
---
[2025-05-20T15:49:08.748Z] INFO [BigbuyController] Starting BigBuy file download process
Data: {
  "fileCount": 13,
  "destination": "./data/downloads/bigbuy"
}
---
[2025-05-20T15:51:03.216Z] INFO [BigbuyController] Files downloaded successfully
Data: {
  "count": 13
}
---
[2025-05-20T15:51:08.580Z] INFO [BigbuyController] Files combined successfully
Data: {
  "outputPath": "data\\downloads\\bigbuy\\bigbuy_combined.csv"
}
---
[2025-05-20T15:51:08.582Z] INFO [BigbuyController] Fetching latest stock data from BigBuy...
---
[2025-05-20T15:56:08.038Z] INFO [BigbuyController] ⏳ Starting stock file simplification
---
[2025-05-20T15:56:09.932Z] INFO [BigbuyController] 📊 Parsed stock file with 368106 items in 1892ms
---
[2025-05-20T15:56:09.935Z] INFO [BigbuyController] 📋 Sample data format: {"id":265,"sku":"********","stocks":[{"quantity":1,"minHandlingDays":0,"maxHandlingDays":1,"warehouse":1},{"quantity":0,"minHandlingDays":1,"maxHandlingDays":2,"warehouse":1}]}
---
[2025-05-20T15:56:09.937Z] INFO [BigbuyController] 🔄 Creating simplified stock data...
---
[2025-05-20T15:56:10.049Z] INFO [BigbuyController] ✅ Transformed 368106 items in 110ms
---
[2025-05-20T15:56:10.051Z] INFO [BigbuyController] 📋 Sample simplified data: {"sku":"********","stock":1}
---
[2025-05-20T15:56:10.053Z] INFO [BigbuyController] 💾 Saving simplified stock data back to: C:\Users\<USER>\Documents\dev\feedmaster-pro\bigbuy-stock.json
---
[2025-05-20T15:56:10.351Z] INFO [BigbuyController] ✅ Simplified stock data saved in 296ms:
---
[2025-05-20T15:56:10.353Z] INFO [BigbuyController]    - Size: 17076 KB
---
[2025-05-20T15:56:10.356Z] INFO [BigbuyController]    - Items: 368106
---
[2025-05-20T15:56:10.358Z] INFO [BigbuyController]    - Format: { sku, stock }
---
[2025-05-20T15:56:10.360Z] INFO [BigbuyController] Stock data updated successfully and saved to bigbuy-stock.json
---
[2025-05-20T15:56:10.363Z] INFO [BigbuyController] Starting stock combination process
---
[2025-05-20T15:56:10.367Z] INFO [BigbuyController] ⏳ Starting stock file simplification
---
[2025-05-20T15:56:10.675Z] INFO [BigbuyController] 📊 Parsed stock file with 368106 items in 305ms
---
[2025-05-20T15:56:10.677Z] INFO [BigbuyController] 📋 Sample data format: {"sku":"********","stock":1}
---
[2025-05-20T15:56:10.679Z] INFO [BigbuyController] ✅ Stock data is already in simplified format
---
[2025-05-20T15:56:10.681Z] INFO [BigbuyController] Found stock file at: C:\Users\<USER>\Documents\dev\feedmaster-pro\bigbuy-stock.json
---
[2025-05-20T15:56:11.055Z] INFO [BigbuyController] Loaded 368106 stock records from JSON
---
[2025-05-20T15:56:11.276Z] INFO [BigbuyController] Created stock map with 368106 entries
---
[2025-05-20T15:56:11.278Z] INFO [BigbuyController] 📂 Original CSV file: data\downloads\bigbuy\bigbuy_combined.csv
---
[2025-05-20T15:56:11.280Z] INFO [BigbuyController]    - Size: 315387 KB
---
[2025-05-20T15:56:11.281Z] INFO [BigbuyController] 🔄 Starting CSV processing...
---
[2025-05-20T16:03:13.319Z] INFO [BigbuyController] Starting BigBuy file download process
Data: {
  "fileCount": 13,
  "destination": "./data/downloads/bigbuy"
}
---
[2025-05-20T16:08:34.668Z] INFO [BigbuyController] Files downloaded successfully
Data: {
  "count": 13
}
---
[2025-05-20T16:08:41.837Z] INFO [BigbuyController] Files combined successfully
Data: {
  "outputPath": "data\\downloads\\bigbuy\\bigbuy_combined.csv"
}
---
[2025-05-20T16:08:41.839Z] INFO [BigbuyController] Fetching latest stock data from BigBuy...