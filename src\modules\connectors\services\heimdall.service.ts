import { ProductModel as BighubProductModel } from '../../bighub/models/product.model';
import { BIGHubCsvPort } from '../../bighub/ports/bighub-csv.port';
import { ProductModel } from '../../catalogue/models/product.model';
import { YamlRepo } from '../../configurations/repos/yaml.repo';
import { HeimdallPort } from '../ports/heimdall.port';
import { HeimdallModel } from '../models/heimdall.model';
import { ConnectorCode } from '../enums/connector-code.enum';
import { HeimdallMapper } from '../mappers/heimdall.mapper';

export class HeimdallService {
  heimdallRepo: HeimdallPort;
  bighubRepo: BIGHubCsvPort
  configuration: YamlRepo

  constructor(
    repo: HeimdallPort,
    bighubRepo: BIGHubCsvPort,
    configuration: YamlRepo
  ) {
    this.heimdallRepo = repo
    this.bighubRepo = bighubRepo
    this.configuration = configuration
  }

  async getProducts(): Promise<ProductModel[]> {
    const heimdall = await this.heimdallRepo.getAll();
    return heimdall.map((item: HeimdallModel) => HeimdallMapper
      .toProduct(item))
      .filter(product => product.stock > 0)
  }

  async getProductsBighub(): Promise<BighubProductModel[]> {
    const BIGHubServiceProducts = await this.heimdallRepo.getAll();

    let products = BIGHubServiceProducts.map((item: HeimdallModel) => HeimdallMapper.toProductBigHub(item))

    // products = await this.heimdallRepo.validateOffers(products)
    console.log('Heimdall products', products.length)
    products = products.filter(product => product.ean !== '')
    console.log('Heimdall products with EAN filter', products.length)
    // stock rules
    products = products.filter(product => product.stock > 0)
    console.log('Heimdall products with stock filter', products.length)
    // price rules
    products = products.filter(product => product.global_price > 0)
    // NaN's filter
    products = products.filter(product => !isNaN(product.global_price))
    console.log('Heimdall products with price filter', products.length)
    // tax rules

    products = await this.bighubRepo.applyTaxes(products, ConnectorCode.HEIMDALL)

    return products;
  }
}