import { OfferProps } from "../../../../domain/entities/offer"
import { UseCase } from "../use-case.interface"

export interface CreateOffersBatchUseCaseInterface
  extends UseCase<CreateOffersBatchUseCaseInterface.Request, CreateOffersBatchUseCaseInterface.Response> {
  execute: (request: CreateOffersBatchUseCaseInterface.Request) => Promise<CreateOffersBatchUseCaseInterface.Response>
}

export namespace CreateOffersBatchUseCaseInterface {
  export type Request = Array<Omit<OfferProps, 'id' | 'created_at' | 'updated_at'>>
  export type Response = boolean
}