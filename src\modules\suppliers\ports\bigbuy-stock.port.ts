import { BigbuyStockModel } from '../models/bigbuy-stock.model';

export interface BigbuyStockPort {
    clear(): void;
    getAllProductsStock(): Promise<BigbuyStockModel[]>;
    getProductVariationsStock(): Promise<BigbuyStockModel[]>;
    getProductStockById(productId: string): Promise<BigbuyStockModel | null>;
    getProductVariationStockById(variationId: string): Promise<BigbuyStockModel | null>;
}