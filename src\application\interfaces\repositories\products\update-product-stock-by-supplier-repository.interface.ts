import { Product } from '../../../../domain/entities/product'

export interface UpdateProductStockBySupplierRepositoryInterface {
  UpdateProductStockBySupplier(request: UpdateProductStockBySupplierRepositoryInterface.Request): Promise<UpdateProductStockBySupplierRepositoryInterface.Response>
}

export namespace UpdateProductStockBySupplierRepositoryInterface {
  export type Request = Pick<Product, 'supplier' | 'stock'>
  export type Response = boolean
}