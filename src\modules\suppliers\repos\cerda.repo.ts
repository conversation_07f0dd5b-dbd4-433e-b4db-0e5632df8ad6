import FtpClient, { ListingElement } from 'ftp'
import { extname } from 'path';
import { FtpCredentials, StorageModel } from '../../storages/models/storage.model';
import { CerdaModel } from '../models/cerda.model';
import * as csv from 'fast-csv'
import { format, parse } from 'fast-csv'
import { promisify } from 'util'
import stream from 'stream';
import iconv from 'iconv-lite';
import * as fs from 'fs';
import { defer } from '../../../shared/utils/promise.util';
import { BigHubService } from 'src/modules/bighub/services/bighub.service';
import { CerdaPort } from '../ports/cerda.port';

type ItemType = {
  name: string,
  type: string
}

type ResponseType = {
  folder: string
  items: ItemType[]
}

export class CerdaRepo implements CerdaPort {

  private ftp: FtpClient = new FtpClient();

  private headers: string[] = []

  private rows: CerdaModel[] = []

  private credentials: FtpCredentials;

  constructor(credentials: FtpCredentials) {
    this.credentials = credentials;
  }

  async put(filename: string, content: CerdaModel[]): Promise<boolean> {
    throw new Error("Method not implemented.");
  }

  async list(folder: string): Promise<StorageModel> {
    const deferred = defer()

    //    let items: ItemType[] = []

    let response: ResponseType = {
      folder,
      items: [],
    }


    try {
      this.ftp.list(folder, async (err: Error, list: ListingElement[]) => {
        if (err) {
          //reject(err)
          deferred.reject(err)
        } else {
          const items = list.filter((file: any) => file.name.endsWith('.csv')).map(file => ({
            name: file.name,
            type: extname(file.name).replace('.', '')
          }))

          deferred.resolve()

          response = {
            folder: folder,
            items
          }
        }
      })
    } catch (error) {
      console.log('entrou aqui no error do ftp')
      console.error(error)
      deferred.resolve()
    }

    await deferred.promise

    return response
  }

  async get(folder: string, filename: string): Promise<CerdaModel[]> {
    const deferred = defer()

    this.rows = [];

    try {
      const ftp: FtpClient = new FtpClient();
      ftp.connect(this.credentials);

      ftp.on('ready', () => {
        ftp.get(`${folder}/${filename}`, (err: Error, stream: NodeJS.ReadableStream) => {
          if (err) {
            deferred.reject(err)
          } else {
            const convertedStream = stream.pipe(iconv.decodeStream('UTF-8'));
            const fileStream = fs.createWriteStream('./cerda-original.csv');
            convertedStream.pipe(fileStream);
            
            // Setup CSV parsing with comma delimiter
            const csvParser = parse({ 
              delimiter: ';',
              ignoreEmpty: true
            });
            convertedStream
              .pipe(csvParser)
              .on('data', (row: any) => this.processLine(row))
              .on('end', (rowCount: number) => {
                console.log(`Parsed ${rowCount} rows`)
                deferred.resolve()
              })
              .on('finish', () => {
                console.log('csv stream finished')
                //resolve(this.rows)
              })
              .on('close', () => {
                console.log('closing ftp connection')
                this.ftp.end()
                deferred.resolve()
              })
          }
        })
      });

      ftp.on('error', (err) => {
        deferred.reject(err)
      })

    } catch (err) {
      deferred.reject(err)
    }

    await deferred.promise

    // After successfully processing the CSV, upload the local file to S3
    try {
      const filePath = './cerda-original.csv';
      const s3Url = await BigHubService.uploadFileToS3(filePath, 'cerda-original', 'seller',1405);
      console.log(`Uploaded to S3: ${s3Url}`);
    } catch (s3Error) {
      // Log the error but don't fail the main operation
      console.error('Error uploading to S3, but continuing with data processing:', s3Error);
    }

    return this.rows
  }

  private processLine(row: any) {

    if (this.headers.length === 0) {
      this.headers = row;
    } else {
      const newRow: CerdaModel = {

        '1referencia': row[0],
        surtido: row[1],
        des_articulo: row[2],
        des_articulo_ing: row[3],
        des_articulo_it: row[4],
        des_articulo_fr: row[5],
        des_articulo_pt: row[6],
        familia: row[7],
        familia_ing: row[8],
        familia_it: row[9],
        familia_fr: row[10],
        familia_pt: row[11],
        subfamilia: row[12],
        subfamilia_ing: row[13],
        subfamilia_it: row[14],
        subfamilia_fr: row[15],
        subfamilia_pt: row[16],
        campana: row[17],
        año: row[18],
        composicion: row[19],
        coordinado: row[20],
        personaje: row[21],
        cod_sub_tipo: row[22],
        codArancel: row[23],
        u_inner: row[24],
        u_outer: row[25],
        unid_inc_sencillo: row[26],
        unid_min_venta: row[27],
        cod_barras: row[28],
        medida_gen: row[29],
        peso: row[30],
        intrastat: row[31],
        alto_out: row[32],
        ancho_out: row[33],
        largo_out: row[34],
        peso_out: row[35],
        alto_pc: row[36],
        ancho_pc: row[37],
        largo_pc: row[38],
        peso_pc: row[39],
        volumen_pc: row[40],
        imagen: row[41],
        img1: row[42],
        img2: row[43],
        img3: row[44],
        img4: row[45],
        img5: row[46],
        desc_color: row[47],
        desc_talla: row[48],
        gif: row[49],
        video: row[50],
        clasificacion: row[51],
        Stock: row[52],
        PVPR: row[53],
        Precio_tarifa: row[54],
        Precio_coste: row[55],
        desc_emocional: row[56],
        cod_color: row[57],
        cod_talla: row[58],
        cod_genero: row[59],
        des_genero: row[60],
        des_licencia: row[61],
        GPSRIsRequired: row[62],
        certificate_CE: row[63],
        certificate_additional_ES: row[64],
        certificate_additional_EN: row[65],
        availableLanguages: row[66],
        producerName: row[67],
        producerAddress: row[68],
        productManagerEMail: row[69],
        operatingInstructions_ES: row[70],
        operatingInstructions_EN: row[71],
        operatingInstructions_FR: row[72],
        operatingInstructions_IT: row[73],
        operatingInstructions_PT: row[74],
        operatingInstructions_DE: row[75],
        operatingInstructions_NL: row[76],
        operatingInstructions_RO: row[77],
        operatingInstructions_HR: row[78],    
        operatingInstructions_SR: row[79],
        operatingInstructions_PL: row[80],
        operatingInstructions_SK: row[81],
        operatingInstructions_CS: row[82],
        operatingInstructions_SV: row[83],
        operatingInstructions_BG: row[84],
        warningNote_ES: row[85],
        warningNote_EN: row[86],
        warningNote_FR: row[87],
        warningNote_IT: row[88],
        warningNote_PT: row[89],
        warningNote_DE: row[90],
        warningNote_NL: row[91],
        warningNote_RO: row[92],
        warningNote_HR: row[93],
        warningNote_SR: row[94],
        warningNote_PL: row[95],
        warningNote_SK: row[96],
        warningNote_CS: row[97],
        warningNote_SV: row[98],
        warningNote_BG: row[99],
      }
      
      this.rows.push(newRow);
    }
  }
}